apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '15.0.14'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.camera"
  defaultConfig {
    versionCode 32
    versionName "15.0.14"
  }
}

repositories {
  maven {
    url "$projectDir/maven"
  }
}

dependencies {
  def camerax_version = "1.4.0-beta02"

  api "androidx.exifinterface:exifinterface:1.3.7"
  api 'com.google.android:cameraview:1.0.0'

  implementation "androidx.camera:camera-core:${camerax_version}"
  implementation "androidx.camera:camera-camera2:${camerax_version}"
  implementation "androidx.camera:camera-lifecycle:${camerax_version}"
  implementation "androidx.camera:camera-video:${camerax_version}"

  implementation "androidx.camera:camera-view:${camerax_version}"
  implementation "androidx.camera:camera-extensions:${camerax_version}"
  implementation "com.google.mlkit:barcode-scanning:17.2.0"
  implementation "androidx.camera:camera-mlkit-vision:${camerax_version}"

  api 'com.google.android:cameraview:1.0.0'
}
