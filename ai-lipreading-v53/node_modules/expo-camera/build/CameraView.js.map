{"version": 3, "file": "CameraView.js", "sourceRoot": "", "sources": ["../src/CameraView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,YAAY,EAAgB,MAAM,mBAAmB,CAAC;AAC9F,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAc/B,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,aAAa,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEpE,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC;AAEhD,MAAM,eAAe,GAAG,GAAG,CAAC;AAE5B,MAAM,wBAAwB,GAAG,EAAE,CAAC;AAEpC,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAE3B,SAAS,oBAAoB,CAAC,OAA8B;IAC1D,MAAM,cAAc,GAClB,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IAEzD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;QAC3B,cAAc,CAAC,OAAO,GAAG,CAAC,CAAC;KAC5B;IACD,IAAI,cAAc,CAAC,cAAc,EAAE;QACjC,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;QAChC,wBAAwB,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,cAAc,CAAC,EAAE,GAAG,EAAE,CAAC;QACvB,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;KAChC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAgC;IAC9D,IAAI,gBAAgB,GAAG,OAAO,IAAI,EAAE,CAAC;IAErC,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;QAC7D,gBAAgB,GAAG,EAAE,CAAC;KACvB;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,eAAe,CAAC,EACvB,WAAW,GAGZ;IACC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;IACjC,MAAM,QAAQ,GAAG,wBAAwB,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,OAAO,wBAAwB,CAAC,EAAE,CAAC,CAAC;KACrC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,OAAO,UAAW,SAAQ,KAAK,CAAC,SAAsB;IAClE;;OAEG;IACH,MAAM,CAAC,+BAA+B,GAAY,aAAa,CAAC,+BAA+B,CAAC;IAChG;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;YACnC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;SAClE;QAED,OAAO,MAAM,aAAa,CAAC,gBAAgB,EAAE,CAAC;IAChD,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE;YAC/C,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;SACzE;QAED,OAAO,MAAM,aAAa,CAAC,4BAA4B,EAAE,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,6BAA6B;QACjC,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,EAAE,CAAC,IAAI,EAAE,CAAC;IAC3E,CAAC;IAED,2EAA2E;IAC3E,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAE3C,MAAM,CAAC,YAAY,GAAgB;QACjC,IAAI,EAAE,CAAC;QACP,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,KAAK;KACb,CAAC;IAEF,aAAa,CAAiB;IAC9B,UAAU,GAAG,KAAK,CAAC,SAAS,EAAiB,CAAC;IAC9C,WAAW,GAAoC,EAAE,CAAC;IAClD,gBAAgB,GAAkC,EAAE,CAAC;IAErD,cAAc;IACd;;;;;;;;;;;;;;;OAeG;IACH,KAAK,CAAC,gBAAgB,CACpB,OAA8B;QAE9B,MAAM,cAAc,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAErD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC;IACpE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAyB;QAClD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;SAChC;QACD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC,+BAA+B,EAAE;YACvE,MAAM,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC5C;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc;QACzB,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC,+BAA+B,EAAE;YACvE,MAAM,aAAa,CAAC,cAAc,EAAE,CAAC;SACtC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAyC;QACrE,OAAO,OAAO,CAAC,WAAW,CAAiB,wBAAwB,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CAAC,OAAgC;QAChD,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC;IAC3C,CAAC;IAED,cAAc,GAAG,GAAG,EAAE;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;SAC5B;IACH,CAAC,CAAC;IAEF,aAAa,GAAG,CAAC,EAAE,WAAW,EAAwC,EAAE,EAAE;QACxE,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;SACtC;IACH,CAAC,CAAC;IAEF,+BAA+B,GAAG,CAAC,EACjC,WAAW,GAGZ,EAAE,EAAE;QACH,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAC7C,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;SACxD;IACH,CAAC,CAAC;IAEF,iBAAiB,GACf,CAAC,QAAmB,EAAE,EAAE,CACxB,CAAC,EAAE,WAAW,EAAwB,EAAE,EAAE;QACxC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAC7B,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtD,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,eAAe,EAC9E;YACA,OAAO;SACR;QAED,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SACtD;IACH,CAAC,CAAC;IAEJ,aAAa,GAAG,CAAC,GAAuB,EAAE,EAAE;QAC1C,IAAI,GAAG,EAAE;YACP,iDAAiD;YACjD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;gBACzB,IAAI,CAAC,aAAa,GAAG,GAAU,CAAC;aACjC;SACF;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAClD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QAEd,OAAO,CACL,CAAC,UAAU,CACT,IAAI,WAAW,CAAC,CAChB,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CACrB,aAAa,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CACnC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CACjC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CACnC,cAAc,CAAC,CAAC,eAAe,CAAC,CAChC,8BAA8B,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,EACrE,CACH,CAAC;IACJ,CAAC", "sourcesContent": ["import { Platform, UnavailabilityError, EventEmitter, Subscription } from 'expo-modules-core';\nimport * as React from 'react';\nimport { Ref } from 'react';\n\nimport {\n  CameraCapturedPicture,\n  CameraOrientation,\n  CameraPictureOptions,\n  CameraProps,\n  CameraRecordingOptions,\n  CameraViewRef,\n  ScanningOptions,\n  ScanningResult,\n  VideoCodec,\n} from './Camera.types';\nimport ExpoCamera from './ExpoCamera';\nimport CameraManager from './ExpoCameraManager';\nimport { ConversionTables, ensureNativeProps } from './utils/props';\n\nconst emitter = new EventEmitter(CameraManager);\n\nconst EventThrottleMs = 500;\n\nconst _PICTURE_SAVED_CALLBACKS = {};\n\nlet _GLOBAL_PICTURE_ID = 1;\n\nfunction ensurePictureOptions(options?: CameraPictureOptions): CameraPictureOptions {\n  const pictureOptions: CameraPictureOptions =\n    !options || typeof options !== 'object' ? {} : options;\n\n  if (!pictureOptions.quality) {\n    pictureOptions.quality = 1;\n  }\n  if (pictureOptions.onPictureSaved) {\n    const id = _GLOBAL_PICTURE_ID++;\n    _PICTURE_SAVED_CALLBACKS[id] = pictureOptions.onPictureSaved;\n    pictureOptions.id = id;\n    pictureOptions.fastMode = true;\n  }\n  return pictureOptions;\n}\n\nfunction ensureRecordingOptions(options?: CameraRecordingOptions): CameraRecordingOptions {\n  let recordingOptions = options || {};\n\n  if (!recordingOptions || typeof recordingOptions !== 'object') {\n    recordingOptions = {};\n  }\n\n  return recordingOptions;\n}\n\nfunction _onPictureSaved({\n  nativeEvent,\n}: {\n  nativeEvent: { data: CameraCapturedPicture; id: number };\n}) {\n  const { id, data } = nativeEvent;\n  const callback = _PICTURE_SAVED_CALLBACKS[id];\n  if (callback) {\n    callback(data);\n    delete _PICTURE_SAVED_CALLBACKS[id];\n  }\n}\n\nexport default class CameraView extends React.Component<CameraProps> {\n  /**\n   * Property that determines if the current device has the ability to use `DataScannerViewController` (iOS 16+).\n   */\n  static isModernBarcodeScannerAvailable: boolean = CameraManager.isModernBarcodeScannerAvailable;\n  /**\n   * Check whether the current device has a camera. This is useful for web and simulators cases.\n   * This isn't influenced by the Permissions API (all platforms), or HTTP usage (in the browser).\n   * You will still need to check if the native permission has been accepted.\n   * @platform web\n   */\n  static async isAvailableAsync(): Promise<boolean> {\n    if (!CameraManager.isAvailableAsync) {\n      throw new UnavailabilityError('expo-camera', 'isAvailableAsync');\n    }\n\n    return await CameraManager.isAvailableAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Queries the device for the available video codecs that can be used in video recording.\n   * @return A promise that resolves to a list of strings that represents available codecs.\n   * @platform ios\n   */\n  static async getAvailableVideoCodecsAsync(): Promise<VideoCodec[]> {\n    if (!CameraManager.getAvailableVideoCodecsAsync) {\n      throw new UnavailabilityError('Camera', 'getAvailableVideoCodecsAsync');\n    }\n\n    return await CameraManager.getAvailableVideoCodecsAsync();\n  }\n\n  /**\n   * Get picture sizes that are supported by the device.\n   * @return Returns a Promise that resolves to an array of strings representing picture sizes that can be passed to `pictureSize` prop.\n   * The list varies across Android devices but is the same for every iOS.\n   */\n  async getAvailablePictureSizesAsync(): Promise<string[]> {\n    return (await this._cameraRef.current?.getAvailablePictureSizes()) ?? [];\n  }\n\n  // Values under keys from this object will be transformed to native options\n  static ConversionTables = ConversionTables;\n\n  static defaultProps: CameraProps = {\n    zoom: 0,\n    facing: 'back',\n    enableTorch: false,\n    mode: 'picture',\n    flash: 'off',\n  };\n\n  _cameraHandle?: number | null;\n  _cameraRef = React.createRef<CameraViewRef>();\n  _lastEvents: { [eventName: string]: string } = {};\n  _lastEventsTimes: { [eventName: string]: Date } = {};\n\n  // @needsAudit\n  /**\n   * Takes a picture and saves it to app's cache directory. Photos are rotated to match device's orientation\n   * (if `options.skipProcessing` flag is not enabled) and scaled to match the preview. Anyway on Android it is essential\n   * to set ratio prop to get a picture with correct dimensions.\n   * > **Note**: Make sure to wait for the [`onCameraReady`](#oncameraready) callback before calling this method.\n   * @param options An object in form of `CameraPictureOptions` type.\n   * @return Returns a Promise that resolves to `CameraCapturedPicture` object, where `uri` is a URI to the local image file on iOS,\n   * Android, and a base64 string on web (usable as the source for an `Image` element). The `width` and `height` properties specify\n   * the dimensions of the image. `base64` is included if the `base64` option was truthy, and is a string containing the JPEG data\n   * of the image in Base64--prepend that with `'data:image/jpg;base64,'` to get a data URI, which you can use as the source\n   * for an `Image` element for example. `exif` is included if the `exif` option was truthy, and is an object containing EXIF\n   * data for the image--the names of its properties are EXIF tags and their values are the values for those tags.\n   *\n   * > On native platforms, the local image URI is temporary. Use [`FileSystem.copyAsync`](filesystem/#filesystemcopyasyncoptions)\n   * > to make a permanent copy of the image.\n   */\n  async takePictureAsync(\n    options?: CameraPictureOptions\n  ): Promise<CameraCapturedPicture | undefined> {\n    const pictureOptions = ensurePictureOptions(options);\n\n    return await this._cameraRef.current?.takePicture(pictureOptions);\n  }\n\n  /**\n   * Presents a modal view controller that uses the [`DataScannerViewController`](https://developer.apple.com/documentation/visionkit/scanning_data_with_the_camera) available on iOS 16+.\n   * @platform ios\n   */\n  static async launchScanner(options?: ScanningOptions): Promise<void> {\n    if (!options) {\n      options = { barcodeTypes: [] };\n    }\n    if (Platform.OS === 'ios' && CameraView.isModernBarcodeScannerAvailable) {\n      await CameraManager.launchScanner(options);\n    }\n  }\n\n  /**\n   * Dimiss the scanner presented by `launchScanner`.\n   * @platform ios\n   */\n  static async dismissScanner(): Promise<void> {\n    if (Platform.OS === 'ios' && CameraView.isModernBarcodeScannerAvailable) {\n      await CameraManager.dismissScanner();\n    }\n  }\n\n  /**\n   * Invokes the `listener` function when a bar code has been successfully scanned. The callback is provided with\n   * an object of the `ScanningResult` shape, where the `type` refers to the bar code type that was scanned and the `data` is the information encoded in the bar code\n   * (in this case of QR codes, this is often a URL). See [`BarcodeType`](#barcodetype) for supported values.\n   * @param listener Invoked with the [ScanningResult](#scanningresult) when a bar code has been successfully scanned.\n   *\n   * @platform ios\n   */\n  static onModernBarcodeScanned(listener: (event: ScanningResult) => void): Subscription {\n    return emitter.addListener<ScanningResult>('onModernBarcodeScanned', listener);\n  }\n\n  /**\n   * Starts recording a video that will be saved to cache directory. Videos are rotated to match device's orientation.\n   * Flipping camera during a recording results in stopping it.\n   * @param options A map of `CameraRecordingOptions` type.\n   * @return Returns a Promise that resolves to an object containing video file `uri` property and a `codec` property on iOS.\n   * The Promise is returned if `stopRecording` was invoked, one of `maxDuration` and `maxFileSize` is reached or camera preview is stopped.\n   * @platform android\n   * @platform ios\n   */\n  async recordAsync(options?: CameraRecordingOptions): Promise<{ uri: string } | undefined> {\n    const recordingOptions = ensureRecordingOptions(options);\n    return await this._cameraRef.current?.record(recordingOptions);\n  }\n\n  /**\n   * Stops recording if any is in progress.\n   */\n  stopRecording() {\n    this._cameraRef.current?.stopRecording();\n  }\n\n  _onCameraReady = () => {\n    if (this.props.onCameraReady) {\n      this.props.onCameraReady();\n    }\n  };\n\n  _onMountError = ({ nativeEvent }: { nativeEvent: { message: string } }) => {\n    if (this.props.onMountError) {\n      this.props.onMountError(nativeEvent);\n    }\n  };\n\n  _onResponsiveOrientationChanged = ({\n    nativeEvent,\n  }: {\n    nativeEvent: { orientation: CameraOrientation };\n  }) => {\n    if (this.props.onResponsiveOrientationChanged) {\n      this.props.onResponsiveOrientationChanged(nativeEvent);\n    }\n  };\n\n  _onObjectDetected =\n    (callback?: Function) =>\n    ({ nativeEvent }: { nativeEvent: any }) => {\n      const { type } = nativeEvent;\n      if (\n        this._lastEvents[type] &&\n        this._lastEventsTimes[type] &&\n        JSON.stringify(nativeEvent) === this._lastEvents[type] &&\n        new Date().getTime() - this._lastEventsTimes[type].getTime() < EventThrottleMs\n      ) {\n        return;\n      }\n\n      if (callback) {\n        callback(nativeEvent);\n        this._lastEventsTimes[type] = new Date();\n        this._lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n\n  _setReference = (ref: Ref<CameraViewRef>) => {\n    if (ref) {\n      // TODO(Bacon): Unify these - perhaps with hooks?\n      if (Platform.OS === 'web') {\n        this._cameraHandle = ref as any;\n      }\n    }\n  };\n\n  render() {\n    const nativeProps = ensureNativeProps(this.props);\n    const onBarcodeScanned = this.props.onBarcodeScanned\n      ? this._onObjectDetected(this.props.onBarcodeScanned)\n      : undefined;\n\n    return (\n      <ExpoCamera\n        {...nativeProps}\n        ref={this._cameraRef}\n        onCameraReady={this._onCameraReady}\n        onMountError={this._onMountError}\n        onBarcodeScanned={onBarcodeScanned}\n        onPictureSaved={_onPictureSaved}\n        onResponsiveOrientationChanged={this._onResponsiveOrientationChanged}\n      />\n    );\n  }\n}\n"]}