{"version": 3, "file": "ExpoCamera.js", "sourceRoot": "", "sources": ["../../src/legacy/ExpoCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAK7D,MAAM,cAAc,GAClB,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;AAE/C,eAAe,cAAc,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport * as React from 'react';\n\nimport { CameraNativeProps } from './Camera.types';\n\nconst ExponentCamera: React.ComponentType<CameraNativeProps> =\n  requireNativeViewManager('ExpoCameraLegacy');\n\nexport default ExponentCamera;\n"]}