{"version": 3, "file": "Camera.js", "sourceRoot": "", "sources": ["../../src/legacy/Camera.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACxF,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAa9C,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,aAAa,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEpE,MAAM,eAAe,GAAG,GAAG,CAAC;AAE5B,MAAM,wBAAwB,GAAG,EAAE,CAAC;AAEpC,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAE3B,SAAS,oBAAoB,CAAC,OAA8B;IAC1D,MAAM,cAAc,GAClB,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IAEzD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;QAC3B,cAAc,CAAC,OAAO,GAAG,CAAC,CAAC;KAC5B;IACD,IAAI,cAAc,CAAC,cAAc,EAAE;QACjC,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;QAChC,wBAAwB,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC;QAC7D,cAAc,CAAC,EAAE,GAAG,EAAE,CAAC;QACvB,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;KAChC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAgC;IAC9D,IAAI,gBAAgB,GAAG,OAAO,IAAI,EAAE,CAAC;IAErC,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;QAC7D,gBAAgB,GAAG,EAAE,CAAC;KACvB;SAAM,IAAI,OAAO,gBAAgB,CAAC,OAAO,KAAK,QAAQ,EAAE;QACvD,gBAAgB,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;KACpF;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,eAAe,CAAC,EACvB,WAAW,GAGZ;IACC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;IACjC,MAAM,QAAQ,GAAG,wBAAwB,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,OAAO,wBAAwB,CAAC,EAAE,CAAC,CAAC;KACrC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,OAAO,MAAO,SAAQ,KAAK,CAAC,SAAsB;IAC9D;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;YACnC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;SAClE;QAED,OAAO,MAAM,aAAa,CAAC,gBAAgB,EAAE,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE;YAC/C,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;SAC9E;QAED,OAAO,MAAM,aAAa,CAAC,4BAA4B,EAAE,CAAC;IAC5D,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE;YAC/C,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;SACzE;QAED,OAAO,MAAM,aAAa,CAAC,4BAA4B,EAAE,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,SAAS,GAAkB;QAChC,IAAI,EAAE,aAAa,CAAC,IAAI;QACxB,SAAS,EAAE,aAAa,CAAC,SAAS;QAClC,SAAS,EAAE,aAAa,CAAC,SAAS;QAClC,YAAY,EAAE,aAAa,CAAC,YAAY;QACxC,YAAY,EAAE,aAAa,CAAC,YAAY;QACxC,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,IAAI,EAAE;QAC1D,UAAU,EAAE,aAAa,CAAC,UAAU;KACrC,CAAC;IAEF,2EAA2E;IAC3E,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAE3C,MAAM,CAAC,YAAY,GAAgB;QACjC,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,KAAK;QACZ,UAAU,EAAE,CAAC;QACb,oBAAoB,EAAE,EAAE;QACxB,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI;QAC7B,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE;QACrC,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,GAAG;QACtC,YAAY,EAAE,aAAa,CAAC,YAAY,CAAC,IAAI;KAC9C,CAAC;IAEF,cAAc;IACd;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAC9B,OAAO,CAAC,IAAI,CACV,mIAAmI,CACpI,CAAC;QACF,OAAO,aAAa,CAAC,mBAAmB,EAAE,CAAC;IAC7C,CAAC;IAED,cAAc;IACd;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB;QAClC,OAAO,CAAC,IAAI,CACV,+IAA+I,CAChJ,CAAC;QACF,OAAO,aAAa,CAAC,uBAAuB,EAAE,CAAC;IACjD,CAAC;IAED,cAAc;IACd;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,yBAAyB;QACpC,OAAO,aAAa,CAAC,yBAAyB,EAAE,CAAC;IACnD,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,6BAA6B;QACxC,OAAO,aAAa,CAAC,6BAA6B,EAAE,CAAC;IACvD,CAAC;IAED,cAAc;IACd;;;;;;;;OAQG;IACH,MAAM,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,SAAS,EAAE,MAAM,CAAC,yBAAyB;QAC3C,aAAa,EAAE,MAAM,CAAC,6BAA6B;KACpD,CAAC,CAAC;IAEH,cAAc;IACd;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,6BAA6B;QACxC,OAAO,aAAa,CAAC,6BAA6B,EAAE,CAAC;IACvD,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,iCAAiC;QAC5C,OAAO,aAAa,CAAC,iCAAiC,EAAE,CAAC;IAC3D,CAAC;IAED,cAAc;IACd;;;;;;;;OAQG;IACH,MAAM,CAAC,wBAAwB,GAAG,oBAAoB,CAAC;QACrD,SAAS,EAAE,MAAM,CAAC,6BAA6B;QAC/C,aAAa,EAAE,MAAM,CAAC,iCAAiC;KACxD,CAAC,CAAC;IAEH,aAAa,CAAiB;IAC9B,UAAU,CAA0B;IACpC,WAAW,GAAoC,EAAE,CAAC;IAClD,gBAAgB,GAAkC,EAAE,CAAC;IAErD,cAAc;IACd;;;;;;;;;;;;;;;OAeG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAA8B;QACnD,MAAM,cAAc,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAErD,OAAO,MAAM,aAAa,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE;YACrC,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,yBAAyB,CAAC,CAAC;SACpE;QAED,OAAO,MAAM,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACpE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,6BAA6B,CAAC,KAAa;QAC/C,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE;YAC3C,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,+BAA+B,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,aAAa,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CAAC,OAAgC;QAChD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACzB,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;SACxD;QAED,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,MAAM,aAAa,CAAC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YAChC,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;SAC1D;QAED,OAAO,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YAC/B,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;SACzD;QAED,OAAO,MAAM,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YAChC,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;SAC1D;QAED,OAAO,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED,cAAc,GAAG,GAAG,EAAE;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;SAC5B;IACH,CAAC,CAAC;IAEF,aAAa,GAAG,CAAC,EAAE,WAAW,EAAwC,EAAE,EAAE;QACxE,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;SACtC;IACH,CAAC,CAAC;IAEF,+BAA+B,GAAG,CAAC,EACjC,WAAW,GAGZ,EAAE,EAAE;QACH,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAC7C,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;SACxD;IACH,CAAC,CAAC;IAEF,iBAAiB,GACf,CAAC,QAAmB,EAAE,EAAE,CACxB,CAAC,EAAE,WAAW,EAAwB,EAAE,EAAE;QACxC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAC7B,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtD,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,eAAe,EAC9E;YACA,OAAO;SACR;QAED,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SACtD;IACH,CAAC,CAAC;IAEJ,aAAa,GAAG,CAAC,GAAqB,EAAE,EAAE;QACxC,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;YACtB,iDAAiD;YACjD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;gBACzB,IAAI,CAAC,aAAa,GAAG,GAAU,CAAC;aACjC;iBAAM;gBACL,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;aAC1C;SACF;aAAM;YACL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAClD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAE3E,OAAO,CACL,CAAC,UAAU,CACT,IAAI,WAAW,CAAC,CAChB,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CACxB,aAAa,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CACnC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CACjC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CACnC,eAAe,CAAC,CAAC,eAAe,CAAC,CACjC,cAAc,CAAC,CAAC,eAAe,CAAC,CAChC,8BAA8B,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,EACrE,CACH,CAAC;IACJ,CAAC;;AAGH,MAAM,CAAC,MAAM,EACX,SAAS,EACT,mBAAmB,EACnB,uBAAuB,EACvB,yBAAyB,EACzB,6BAA6B,EAC7B,6BAA6B,EAC7B,iCAAiC,GAClC,GAAG,MAAM,CAAC", "sourcesContent": ["import { createPermissionHook, Platform, UnavailabilityError } from 'expo-modules-core';\nimport * as React from 'react';\nimport { findNodeHandle } from 'react-native';\n\nimport {\n  CameraCapturedPicture,\n  CameraOrientation,\n  CameraPictureOptions,\n  CameraProps,\n  CameraRecordingOptions,\n  CameraType,\n  ConstantsType,\n  PermissionResponse,\n  VideoCodec,\n} from './Camera.types';\nimport ExpoCamera from './ExpoCamera';\nimport CameraManager from './ExpoCameraManager';\nimport { ConversionTables, ensureNativeProps } from './utils/props';\n\nconst EventThrottleMs = 500;\n\nconst _PICTURE_SAVED_CALLBACKS = {};\n\nlet _GLOBAL_PICTURE_ID = 1;\n\nfunction ensurePictureOptions(options?: CameraPictureOptions): CameraPictureOptions {\n  const pictureOptions: CameraPictureOptions =\n    !options || typeof options !== 'object' ? {} : options;\n\n  if (!pictureOptions.quality) {\n    pictureOptions.quality = 1;\n  }\n  if (pictureOptions.onPictureSaved) {\n    const id = _GLOBAL_PICTURE_ID++;\n    _PICTURE_SAVED_CALLBACKS[id] = pictureOptions.onPictureSaved;\n    pictureOptions.id = id;\n    pictureOptions.fastMode = true;\n  }\n  return pictureOptions;\n}\n\nfunction ensureRecordingOptions(options?: CameraRecordingOptions): CameraRecordingOptions {\n  let recordingOptions = options || {};\n\n  if (!recordingOptions || typeof recordingOptions !== 'object') {\n    recordingOptions = {};\n  } else if (typeof recordingOptions.quality === 'string') {\n    recordingOptions.quality = Camera.Constants.VideoQuality[recordingOptions.quality];\n  }\n\n  return recordingOptions;\n}\n\nfunction _onPictureSaved({\n  nativeEvent,\n}: {\n  nativeEvent: { data: CameraCapturedPicture; id: number };\n}) {\n  const { id, data } = nativeEvent;\n  const callback = _PICTURE_SAVED_CALLBACKS[id];\n  if (callback) {\n    callback(data);\n    delete _PICTURE_SAVED_CALLBACKS[id];\n  }\n}\n\nexport default class Camera extends React.Component<CameraProps> {\n  /**\n   * Check whether the current device has a camera. This is useful for web and simulators cases.\n   * This isn't influenced by the Permissions API (all platforms), or HTTP usage (in the browser).\n   * You will still need to check if the native permission has been accepted.\n   * @platform web\n   */\n  static async isAvailableAsync(): Promise<boolean> {\n    if (!CameraManager.isAvailableAsync) {\n      throw new UnavailabilityError('expo-camera', 'isAvailableAsync');\n    }\n\n    return await CameraManager.isAvailableAsync();\n  }\n\n  /**\n   * Returns a list of camera types `['front', 'back']`. This is useful for desktop browsers which only have front-facing cameras.\n   * @platform web\n   */\n  static async getAvailableCameraTypesAsync(): Promise<CameraType[]> {\n    if (!CameraManager.getAvailableCameraTypesAsync) {\n      throw new UnavailabilityError('expo-camera', 'getAvailableCameraTypesAsync');\n    }\n\n    return await CameraManager.getAvailableCameraTypesAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Queries the device for the available video codecs that can be used in video recording.\n   * @return A promise that resolves to a list of strings that represents available codecs.\n   * @platform ios\n   */\n  static async getAvailableVideoCodecsAsync(): Promise<VideoCodec[]> {\n    if (!CameraManager.getAvailableVideoCodecsAsync) {\n      throw new UnavailabilityError('Camera', 'getAvailableVideoCodecsAsync');\n    }\n\n    return await CameraManager.getAvailableVideoCodecsAsync();\n  }\n\n  static Constants: ConstantsType = {\n    Type: CameraManager.Type,\n    FlashMode: CameraManager.FlashMode,\n    AutoFocus: CameraManager.AutoFocus,\n    WhiteBalance: CameraManager.WhiteBalance,\n    VideoQuality: CameraManager.VideoQuality,\n    VideoStabilization: CameraManager.VideoStabilization || {},\n    VideoCodec: CameraManager.VideoCodec,\n  };\n\n  // Values under keys from this object will be transformed to native options\n  static ConversionTables = ConversionTables;\n\n  static defaultProps: CameraProps = {\n    zoom: 0,\n    ratio: '4:3',\n    focusDepth: 0,\n    faceDetectorSettings: {},\n    type: CameraManager.Type.back,\n    autoFocus: CameraManager.AutoFocus.on,\n    flashMode: CameraManager.FlashMode.off,\n    whiteBalance: CameraManager.WhiteBalance.auto,\n  };\n\n  // @needsAudit\n  /**\n   * @deprecated Use `getCameraPermissionsAsync` or `getMicrophonePermissionsAsync` instead.\n   * Checks user's permissions for accessing camera.\n   */\n  static async getPermissionsAsync(): Promise<PermissionResponse> {\n    console.warn(\n      `\"getPermissionsAsync()\" is now deprecated. Please use \"getCameraPermissionsAsync()\" or \"getMicrophonePermissionsAsync()\" instead.`\n    );\n    return CameraManager.getPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing camera.\n   * On iOS this will require apps to specify both `NSCameraUsageDescription` and `NSMicrophoneUsageDescription` entries in the **Info.plist**.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   * @deprecated Use `requestCameraPermissionsAsync` or `requestMicrophonePermissionsAsync` instead.\n   */\n  static async requestPermissionsAsync(): Promise<PermissionResponse> {\n    console.warn(\n      `\"requestPermissionsAsync()\" is now deprecated. Please use \"requestCameraPermissionsAsync()\" or \"requestMicrophonePermissionsAsync()\" instead.`\n    );\n    return CameraManager.requestPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing camera.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  static async getCameraPermissionsAsync(): Promise<PermissionResponse> {\n    return CameraManager.getCameraPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing camera.\n   * On iOS this will require apps to specify an `NSCameraUsageDescription` entry in the **Info.plist**.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  static async requestCameraPermissionsAsync(): Promise<PermissionResponse> {\n    return CameraManager.requestCameraPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Check or request permissions to access the camera.\n   * This uses both `requestCameraPermissionsAsync` and `getCameraPermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [status, requestPermission] = Camera.useCameraPermissions();\n   * ```\n   */\n  static useCameraPermissions = createPermissionHook({\n    getMethod: Camera.getCameraPermissionsAsync,\n    requestMethod: Camera.requestCameraPermissionsAsync,\n  });\n\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing microphone.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  static async getMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n    return CameraManager.getMicrophonePermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing the microphone.\n   * On iOS this will require apps to specify an `NSMicrophoneUsageDescription` entry in the **Info.plist**.\n   * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n   */\n  static async requestMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n    return CameraManager.requestMicrophonePermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Check or request permissions to access the microphone.\n   * This uses both `requestMicrophonePermissionsAsync` and `getMicrophonePermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [status, requestPermission] = Camera.useMicrophonePermissions();\n   * ```\n   */\n  static useMicrophonePermissions = createPermissionHook({\n    getMethod: Camera.getMicrophonePermissionsAsync,\n    requestMethod: Camera.requestMicrophonePermissionsAsync,\n  });\n\n  _cameraHandle?: number | null;\n  _cameraRef?: React.Component | null;\n  _lastEvents: { [eventName: string]: string } = {};\n  _lastEventsTimes: { [eventName: string]: Date } = {};\n\n  // @needsAudit\n  /**\n   * Takes a picture and saves it to app's cache directory. Photos are rotated to match device's orientation\n   * (if `options.skipProcessing` flag is not enabled) and scaled to match the preview. Anyway on Android it is essential\n   * to set ratio prop to get a picture with correct dimensions.\n   * > **Note**: Make sure to wait for the [`onCameraReady`](#oncameraready) callback before calling this method.\n   * @param options An object in form of `CameraPictureOptions` type.\n   * @return Returns a Promise that resolves to `CameraCapturedPicture` object, where `uri` is a URI to the local image file on iOS,\n   * Android, and a base64 string on web (usable as the source for an `Image` element). The `width` and `height` properties specify\n   * the dimensions of the image. `base64` is included if the `base64` option was truthy, and is a string containing the JPEG data\n   * of the image in Base64--prepend that with `'data:image/jpg;base64,'` to get a data URI, which you can use as the source\n   * for an `Image` element for example. `exif` is included if the `exif` option was truthy, and is an object containing EXIF\n   * data for the image--the names of its properties are EXIF tags and their values are the values for those tags.\n   *\n   * > On native platforms, the local image URI is temporary. Use [`FileSystem.copyAsync`](filesystem/#filesystemcachedirectory)\n   * > to make a permanent copy of the image.\n   */\n  async takePictureAsync(options?: CameraPictureOptions): Promise<CameraCapturedPicture> {\n    const pictureOptions = ensurePictureOptions(options);\n\n    return await CameraManager.takePicture(pictureOptions, this._cameraHandle);\n  }\n\n  /**\n   * Get aspect ratios that are supported by the device and can be passed via `ratio` prop.\n   * @return Returns a Promise that resolves to an array of strings representing ratios, eg. `['4:3', '1:1']`.\n   * @platform android\n   */\n  async getSupportedRatiosAsync(): Promise<string[]> {\n    if (!CameraManager.getSupportedRatios) {\n      throw new UnavailabilityError('Camera', 'getSupportedRatiosAsync');\n    }\n\n    return await CameraManager.getSupportedRatios(this._cameraHandle);\n  }\n\n  /**\n   * Get picture sizes that are supported by the device for given `ratio`.\n   * @param ratio A string representing aspect ratio of sizes to be returned.\n   * @return Returns a Promise that resolves to an array of strings representing picture sizes that can be passed to `pictureSize` prop.\n   * The list varies across Android devices but is the same for every iOS.\n   */\n  async getAvailablePictureSizesAsync(ratio: string): Promise<string[]> {\n    if (!CameraManager.getAvailablePictureSizes) {\n      throw new UnavailabilityError('Camera', 'getAvailablePictureSizesAsync');\n    }\n    return await CameraManager.getAvailablePictureSizes(ratio, this._cameraHandle);\n  }\n\n  /**\n   * Starts recording a video that will be saved to cache directory. Videos are rotated to match device's orientation.\n   * Flipping camera during a recording results in stopping it.\n   * @param options A map of `CameraRecordingOptions` type.\n   * @return Returns a Promise that resolves to an object containing video file `uri` property and a `codec` property on iOS.\n   * The Promise is returned if `stopRecording` was invoked, one of `maxDuration` and `maxFileSize` is reached or camera preview is stopped.\n   * @platform android\n   * @platform ios\n   */\n  async recordAsync(options?: CameraRecordingOptions): Promise<{ uri: string }> {\n    if (!CameraManager.record) {\n      throw new UnavailabilityError('Camera', 'recordAsync');\n    }\n\n    const recordingOptions = ensureRecordingOptions(options);\n    return await CameraManager.record(recordingOptions, this._cameraHandle);\n  }\n\n  /**\n   * Stops recording if any is in progress.\n   */\n  async stopRecording(): Promise<void> {\n    if (!CameraManager.stopRecording) {\n      throw new UnavailabilityError('Camera', 'stopRecording');\n    }\n\n    return await CameraManager.stopRecording(this._cameraHandle);\n  }\n\n  /**\n   * Pauses the camera preview. It is not recommended to use `takePictureAsync` when preview is paused.\n   */\n  async pausePreview(): Promise<void> {\n    if (!CameraManager.pausePreview) {\n      throw new UnavailabilityError('Camera', 'pausePreview');\n    }\n\n    return await CameraManager.pausePreview(this._cameraHandle);\n  }\n\n  /**\n   * Resumes the camera preview.\n   */\n  async resumePreview(): Promise<void> {\n    if (!CameraManager.resumePreview) {\n      throw new UnavailabilityError('Camera', 'resumePreview');\n    }\n\n    return await CameraManager.resumePreview(this._cameraHandle);\n  }\n\n  _onCameraReady = () => {\n    if (this.props.onCameraReady) {\n      this.props.onCameraReady();\n    }\n  };\n\n  _onMountError = ({ nativeEvent }: { nativeEvent: { message: string } }) => {\n    if (this.props.onMountError) {\n      this.props.onMountError(nativeEvent);\n    }\n  };\n\n  _onResponsiveOrientationChanged = ({\n    nativeEvent,\n  }: {\n    nativeEvent: { orientation: CameraOrientation };\n  }) => {\n    if (this.props.onResponsiveOrientationChanged) {\n      this.props.onResponsiveOrientationChanged(nativeEvent);\n    }\n  };\n\n  _onObjectDetected =\n    (callback?: Function) =>\n    ({ nativeEvent }: { nativeEvent: any }) => {\n      const { type } = nativeEvent;\n      if (\n        this._lastEvents[type] &&\n        this._lastEventsTimes[type] &&\n        JSON.stringify(nativeEvent) === this._lastEvents[type] &&\n        new Date().getTime() - this._lastEventsTimes[type].getTime() < EventThrottleMs\n      ) {\n        return;\n      }\n\n      if (callback) {\n        callback(nativeEvent);\n        this._lastEventsTimes[type] = new Date();\n        this._lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n\n  _setReference = (ref?: React.Component) => {\n    if (ref) {\n      this._cameraRef = ref;\n      // TODO(Bacon): Unify these - perhaps with hooks?\n      if (Platform.OS === 'web') {\n        this._cameraHandle = ref as any;\n      } else {\n        this._cameraHandle = findNodeHandle(ref);\n      }\n    } else {\n      this._cameraRef = null;\n      this._cameraHandle = null;\n    }\n  };\n\n  render() {\n    const nativeProps = ensureNativeProps(this.props);\n\n    const onBarCodeScanned = this.props.onBarCodeScanned\n      ? this._onObjectDetected(this.props.onBarCodeScanned)\n      : undefined;\n\n    const onFacesDetected = this._onObjectDetected(this.props.onFacesDetected);\n\n    return (\n      <ExpoCamera\n        {...nativeProps}\n        ref={this._setReference}\n        onCameraReady={this._onCameraReady}\n        onMountError={this._onMountError}\n        onBarCodeScanned={onBarCodeScanned}\n        onFacesDetected={onFacesDetected}\n        onPictureSaved={_onPictureSaved}\n        onResponsiveOrientationChanged={this._onResponsiveOrientationChanged}\n      />\n    );\n  }\n}\n\nexport const {\n  Constants,\n  getPermissionsAsync,\n  requestPermissionsAsync,\n  getCameraPermissionsAsync,\n  requestCameraPermissionsAsync,\n  getMicrophonePermissionsAsync,\n  requestMicrophonePermissionsAsync,\n} = Camera;\n"]}