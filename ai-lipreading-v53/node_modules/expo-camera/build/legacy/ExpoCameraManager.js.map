{"version": 3, "file": "ExpoCameraManager.js", "sourceRoot": "", "sources": ["../../src/legacy/ExpoCameraManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,MAAM,aAAa,GAAwB,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAEnF,eAAe,aAAa,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nconst CameraManager: Record<string, any> = requireNativeModule('ExpoCameraLegacy');\n\nexport default CameraManager;\n"]}