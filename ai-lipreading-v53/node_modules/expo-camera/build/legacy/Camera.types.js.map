{"version": 3, "file": "Camera.types.js", "sourceRoot": "", "sources": ["../../src/legacy/Camera.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,GAGjB,MAAM,mBAAmB,CAAC;AAG3B,MAAM,CAAN,IAAY,UAGX;AAHD,WAAY,UAAU;IACpB,6BAAe,CAAA;IACf,2BAAa,CAAA;AACf,CAAC,EAHW,UAAU,KAAV,UAAU,QAGrB;AAED,MAAM,CAAN,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,sBAAS,CAAA;IACT,wBAAW,CAAA;IACX,0BAAa,CAAA;IACb,4BAAe,CAAA;AACjB,CAAC,EALW,SAAS,KAAT,SAAS,QAKpB;AAED,MAAM,CAAN,IAAY,SAWX;AAXD,WAAY,SAAS;IACnB,sBAAS,CAAA;IACT,wBAAW,CAAA;IACX;;OAEG;IACH,0BAAa,CAAA;IACb;;OAEG;IACH,sCAAyB,CAAA;AAC3B,CAAC,EAXW,SAAS,KAAT,SAAS,QAWpB;AAED,MAAM,CAAN,IAAY,YAmCX;AAnCD,WAAY,YAAY;IACtB,6BAAa,CAAA;IACb;;;OAGG;IACH,+BAAe,CAAA;IACf;;;OAGG;IACH,iCAAiB,CAAA;IACjB;;;OAGG;IACH,iCAAiB,CAAA;IACjB;;;OAGG;IACH,6CAA6B,CAAA;IAC7B;;;OAGG;IACH,2CAA2B,CAAA;IAC3B;;OAEG;IACH,yCAAyB,CAAA;IACzB;;OAEG;IACH,iCAAiB,CAAA;AACnB,CAAC,EAnCW,YAAY,KAAZ,YAAY,QAmCvB;AAED,MAAM,CAAN,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,wBAAW,CAAA;IACX,wBAAW,CAAA;AACb,CAAC,EAHW,SAAS,KAAT,SAAS,QAGpB;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,UAMX;AAND,WAAY,UAAU;IACpB,2BAAa,CAAA;IACb,2BAAa,CAAA;IACb,2BAAa,CAAA;IACb,qCAAuB,CAAA;IACvB,sCAAwB,CAAA;AAC1B,CAAC,EANW,UAAU,KAAV,UAAU,QAMrB;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,iCAAW,CAAA;IACX,2CAAqB,CAAA;IACrB,6CAAuB,CAAA;IACvB,mCAAa,CAAA;AACf,CAAC,EALW,kBAAkB,KAAlB,kBAAkB,QAK7B;AAED,eAAe;AACf,MAAM,CAAN,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,+BAAiB,CAAA;IACjB,+BAAiB,CAAA;IACjB,6BAAe,CAAA;IACf,6BAAe,CAAA;IACf,2BAAa,CAAA;AACf,CAAC,EANW,YAAY,KAAZ,YAAY,QAMvB;AAED,MAAM,CAAN,IAAY,iBAKX;AALD,WAAY,iBAAiB;IAC3B,iEAAY,CAAA;IACZ,qFAAsB,CAAA;IACtB,2EAAiB,CAAA;IACjB,6EAAkB,CAAA;AACpB,CAAC,EALW,iBAAiB,KAAjB,iBAAiB,QAK5B;AAkbD,OAAO,EAAsB,gBAAgB,EAA+C,CAAC", "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionExpiration,\n  PermissionHookOptions,\n} from 'expo-modules-core';\nimport type { ViewProps } from 'react-native';\n\nexport enum CameraType {\n  front = 'front',\n  back = 'back',\n}\n\nexport enum FlashMode {\n  on = 'on',\n  off = 'off',\n  auto = 'auto',\n  torch = 'torch',\n}\n\nexport enum AutoFocus {\n  on = 'on',\n  off = 'off',\n  /**\n   * @platform web\n   */\n  auto = 'auto',\n  /**\n   * @platform web\n   */\n  singleShot = 'singleShot',\n}\n\nexport enum WhiteBalance {\n  auto = 'auto',\n  /**\n   * @platform android\n   * @platform ios\n   */\n  sunny = 'sunny',\n  /**\n   * @platform android\n   * @platform ios\n   */\n  cloudy = 'cloudy',\n  /**\n   * @platform android\n   * @platform ios\n   */\n  shadow = 'shadow',\n  /**\n   * @platform android\n   * @platform ios\n   */\n  incandescent = 'incandescent',\n  /**\n   * @platform android\n   * @platform ios\n   */\n  fluorescent = 'fluorescent',\n  /**\n   * @platform web\n   */\n  continuous = 'continuous',\n  /**\n   * @platform web\n   */\n  manual = 'manual',\n}\n\nexport enum ImageType {\n  png = 'png',\n  jpg = 'jpg',\n}\n\n/**\n * This option specifies what codec to use when recording a video.\n * @platform ios\n */\nexport enum VideoCodec {\n  H264 = 'avc1',\n  HEVC = 'hvc1',\n  JPEG = 'jpeg',\n  AppleProRes422 = 'apcn',\n  AppleProRes4444 = 'ap4h',\n}\n\n/**\n * This option specifies the stabilization mode to use when recording a video.\n * @platform ios\n */\nexport enum VideoStabilization {\n  off = 'off',\n  standard = 'standard',\n  cinematic = 'cinematic',\n  auto = 'auto',\n}\n\n// @docsMissing\nexport enum VideoQuality {\n  '2160p' = '2160p',\n  '1080p' = '1080p',\n  '720p' = '720p',\n  '480p' = '480p',\n  '4:3' = '4:3',\n}\n\nexport enum CameraOrientation {\n  portrait = 1,\n  portraitUpsideDown = 2,\n  landscapeLeft = 3,\n  landscapeRight = 4,\n}\n\n// @docsMissing\n/**\n * @hidden We do not expose related web methods in docs.\n * @platform web\n */\nexport type ImageSize = {\n  width: number;\n  height: number;\n};\n\n// @docsMissing\n/**\n * @hidden We do not expose related web methods in docs.\n * @platform web\n */\nexport type WebCameraSettings = {\n  autoFocus?: string;\n  flashMode?: string;\n  whiteBalance?: string;\n  exposureCompensation?: number;\n  colorTemperature?: number;\n  iso?: number;\n  brightness?: number;\n  contrast?: number;\n  saturation?: number;\n  sharpness?: number;\n  focusDistance?: number;\n  zoom?: number;\n};\n\n// @needsAudit\nexport type CameraCapturedPicture = {\n  /**\n   * Captured image width.\n   */\n  width: number;\n  /**\n   * Captured image height.\n   */\n  height: number;\n  /**\n   * On web, the value of `uri` is the same as `base64` because file system URLs are not supported in the browser.\n   */\n  uri: string;\n  /**\n   * A Base64 representation of the image.\n   */\n  base64?: string;\n  /**\n   * On Android and iOS this object may include various fields based on the device and operating system.\n   * On web, it is a partial representation of the [`MediaTrackSettings`](https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackSettings) dictionary.\n   */\n  exif?: Partial<MediaTrackSettings> | any;\n};\n\n// @needsAudit\nexport type CameraPictureOptions = {\n  /**\n   * Specify the compression quality from `0` to `1`. `0` means compress for small size, and `1` means compress for maximum quality.\n   */\n  quality?: number;\n  /**\n   * Whether to also include the image data in Base64 format.\n   */\n  base64?: boolean;\n  /**\n   * Whether to also include the EXIF data for the image.\n   */\n  exif?: boolean;\n  /**\n   * Additional EXIF data to be included for the image. Only useful when `exif` option is set to `true`.\n   * @platform android\n   * @platform ios\n   */\n  additionalExif?: Record<string, any>;\n  /**\n   * A callback invoked when picture is saved. If set, the promise of this method will resolve immediately with no data after picture is captured.\n   * The data that it should contain will be passed to this callback. If displaying or processing a captured photo right after taking it\n   * is not your case, this callback lets you skip waiting for it to be saved.\n   * @param picture\n   */\n  onPictureSaved?: (picture: CameraCapturedPicture) => void;\n  // TODO(Bacon): Is it possible to implement this in the browser?\n  /**\n   * If set to `true`, camera skips orientation adjustment and returns an image straight from the device's camera.\n   * If enabled, `quality` option is discarded (processing pipeline is skipped as a whole).\n   * Although enabling this option reduces image delivery time significantly, it may cause the image to appear in a wrong orientation\n   * in the `Image` component (at the time of writing, it does not respect EXIF orientation of the images).\n   * > **Note**: Enabling `skipProcessing` would cause orientation uncertainty. `Image` component does not respect EXIF\n   * > stored orientation information, that means obtained image would be displayed wrongly (rotated by 90°, 180° or 270°).\n   * > Different devices provide different orientations. For example some Sony Xperia or Samsung devices don't provide\n   * > correctly oriented images by default. To always obtain correctly oriented image disable `skipProcessing` option.\n   */\n  skipProcessing?: boolean;\n  /**\n   * @platform web\n   */\n  scale?: number;\n  /**\n   * @platform web\n   */\n  imageType?: ImageType;\n  /**\n   * @platform web\n   */\n  isImageMirror?: boolean;\n  /**\n   * @hidden\n   */\n  id?: number;\n  /**\n   * @hidden\n   */\n  fastMode?: boolean;\n  /**\n   * @hidden\n   */\n  maxDownsampling?: number;\n};\n\n// @needsAudit\nexport type CameraRecordingOptions = {\n  /**\n   * Maximum video duration in seconds.\n   */\n  maxDuration?: number;\n  /**\n   * Maximum video file size in bytes.\n   */\n  maxFileSize?: number;\n  /**\n   * Specify the quality of recorded video. Use one of [`VideoQuality.<value>`](#videoquality).\n   * Possible values: for 16:9 resolution `2160p`, `1080p`, `720p`, `480p` : `Android only` and for 4:3 `4:3` (the size is 640x480).\n   * If the chosen quality is not available for a device, the highest available is chosen.\n   */\n  quality?: number | string;\n  /**\n   * If present, video will be recorded with no sound.\n   */\n  mute?: boolean;\n  /**\n   * If `true`, the recorded video will be flipped along the vertical axis. iOS flips videos recorded with the front camera by default,\n   * but you can reverse that back by setting this to `true`. On Android, this is handled in the user's device settings.\n   * @platform ios\n   */\n  mirror?: boolean;\n  /**\n   * Only works if `useCamera2Api` is set to `true`. This option specifies a desired video bitrate. For example, `5*1000*1000` would be 5Mbps.\n   * @platform android\n   */\n  videoBitrate?: number;\n  /**\n   * This option specifies what codec to use when recording the video. See [`VideoCodec`](#videocodec) for the possible values.\n   * @platform ios\n   */\n  codec?: VideoCodec;\n};\n\n/**\n * @hidden\n */\nexport type PictureSavedListener = (event: {\n  nativeEvent: { data: CameraCapturedPicture; id: number };\n}) => void;\n\n/**\n * @hidden\n */\nexport type CameraReadyListener = () => void;\n\n/**\n * @hidden\n */\nexport type ResponsiveOrientationChangedListener = (event: {\n  nativeEvent: ResponsiveOrientationChanged;\n}) => void;\n\nexport type ResponsiveOrientationChanged = { orientation: CameraOrientation };\n\n/**\n * @hidden\n */\nexport type MountErrorListener = (event: { nativeEvent: CameraMountError }) => void;\n\n// @docsMissing\nexport type CameraMountError = { message: string };\n\n// @docsMissing\nexport type Point = {\n  x: number;\n  y: number;\n};\n\nexport type BarCodeSize = {\n  /**\n   * The height value.\n   */\n  height: number;\n  /**\n   * The width value.\n   */\n  width: number;\n};\n\n/**\n * These coordinates are represented in the coordinate space of the camera source (e.g. when you\n * are using the camera view, these values are adjusted to the dimensions of the view).\n */\nexport type BarCodePoint = Point;\n\nexport type BarCodeBounds = {\n  /**\n   * The origin point of the bounding box.\n   */\n  origin: BarCodePoint;\n  /**\n   * The size of the bounding box.\n   */\n  size: BarCodeSize;\n};\n\n// @needsAudit\nexport type BarCodeScanningResult = {\n  /**\n   * The barcode type.\n   */\n  type: string;\n  /**\n   * The parsed information encoded in the bar code.\n   */\n  data: string;\n  /**\n   * The raw information encoded in the bar code.\n   * May be different from `data` depending on the barcode type.\n   * @platform android\n   * @hidden\n   */\n  raw?: string;\n  /**\n   * Corner points of the bounding box.\n   * `cornerPoints` is not always available and may be empty. On iOS, for `code39` and `pdf417`\n   * you don't get this value.\n   */\n  cornerPoints: BarCodePoint[];\n  /**\n   * The [BarCodeBounds](#barcodebounds) object.\n   * `bounds` in some case will be representing an empty rectangle.\n   * Moreover, `bounds` doesn't have to bound the whole barcode.\n   * For some types, they will represent the area used by the scanner.\n   */\n  bounds: BarCodeBounds;\n};\n\nexport type FaceDetectionResult = {\n  /**\n   * Array of objects representing results of face detection.\n   * See [`FaceFeature`](facedetector/#facefeature) in FaceDetector documentation for more details.\n   */\n  faces: object[];\n};\n\n/**\n * @hidden\n */\nexport type ConstantsType = {\n  Type: CameraType;\n  FlashMode: FlashMode;\n  AutoFocus: AutoFocus;\n  WhiteBalance: WhiteBalance;\n  VideoQuality: VideoQuality;\n  VideoStabilization: VideoStabilization;\n  VideoCodec: VideoCodec;\n};\n\n// @needsAudit\nexport type CameraProps = ViewProps & {\n  /**\n   * Camera facing. Use one of `CameraType`. When `CameraType.front`, use the front-facing camera.\n   * When `CameraType.back`, use the back-facing camera.\n   * @default CameraType.back\n   */\n  type?: number | CameraType;\n  /**\n   * Camera flash mode. Use one of [`FlashMode.<value>`](#flashmode-1). When `FlashMode.on`, the flash on your device will\n   * turn on when taking a picture, when `FlashMode.off`, it won't. Setting to `FlashMode.auto` will fire flash if required,\n   * `FlashMode.torch` turns on flash during the preview.\n   * @default FlashMode.off\n   */\n  flashMode?: number | FlashMode;\n  /**\n   * Camera white balance. Use one of [`WhiteBalance.<value>`](#whitebalance). If a device does not support any of these values previous one is used.\n   * @default WhiteBalance.auto\n   */\n  whiteBalance?: number | WhiteBalance;\n  /**\n   * State of camera auto focus. Use one of [`AutoFocus.<value>`](#autofocus-1). When `AutoFocus.on`,\n   * auto focus will be enabled, when `AutoFocus.off`, it won't and focus will lock as it was in the moment of change,\n   * but it can be adjusted on some devices via `focusDepth` prop.\n   * @default AutoFocus.on\n   */\n  autoFocus?: boolean | number | AutoFocus;\n  /**\n   * A value between `0` and `1` being a percentage of device's max zoom. `0` - not zoomed, `1` - maximum zoom.\n   * @default 0\n   */\n  zoom?: number;\n  /**\n   * A string representing aspect ratio of the preview, eg. `4:3`, `16:9`, `1:1`. To check if a ratio is supported\n   * by the device use [`getSupportedRatiosAsync`](#getsupportedratiosasync).\n   * @default 4:3\n   * @platform android\n   */\n  ratio?: string;\n  /**\n   * Distance to plane of the sharpest focus. A value between `0` and `1` where: `0` - infinity focus, `1` - focus as close as possible.\n   * For Android this is available only for some devices and when `useCamera2Api` is set to `true`.\n   * @default 0\n   */\n  focusDepth?: number;\n  /**\n   * Callback invoked when camera preview has been set.\n   */\n  onCameraReady?: () => void;\n  /**\n   * Whether to use Android's Camera2 API. See `Note` at the top of this page.\n   * @platform android\n   */\n  useCamera2Api?: boolean;\n  /**\n   * A string representing the size of pictures [`takePictureAsync`](#takepictureasyncoptions) will take.\n   * Available sizes can be fetched with [`getAvailablePictureSizesAsync`](#getavailablepicturesizesasyncratio).\n   */\n  pictureSize?: string;\n  /**\n   * The video stabilization mode used for a video recording. Use one of [`VideoStabilization.<value>`](#videostabilization).\n   * You can read more about each stabilization type in [Apple Documentation](https://developer.apple.com/documentation/avfoundation/avcapturevideostabilizationmode).\n   * @platform ios\n   */\n  videoStabilizationMode?: VideoStabilization;\n  /**\n   * Callback invoked when camera preview could not been started.\n   * @param event Error object that contains a `message`.\n   */\n  onMountError?: (event: CameraMountError) => void;\n  /**\n   * Settings exposed by [`BarCodeScanner`](bar-code-scanner) module. Supported settings: **barCodeTypes**.\n   * @example\n   * ```tsx\n   * <Camera\n   *   barCodeScannerSettings={{\n   *     barCodeTypes: [BarCodeScanner.Constants.BarCodeType.qr],\n   *   }}\n   * />\n   * ```\n   */\n  barCodeScannerSettings?: BarCodeSettings;\n  /**\n   * Callback that is invoked when a bar code has been successfully scanned. The callback is provided with\n   * an object of the [`BarCodeScanningResult`](#barcodescanningresult) shape, where the `type`\n   * refers to the bar code type that was scanned and the `data` is the information encoded in the bar code\n   * (in this case of QR codes, this is often a URL). See [`BarCodeScanner.Constants.BarCodeType`](bar-code-scanner#supported-formats)\n   * for supported values.\n   * @param scanningResult\n   */\n  onBarCodeScanned?: (scanningResult: BarCodeScanningResult) => void;\n  /**\n   * A settings object passed directly to an underlying module providing face detection features.\n   * See [`DetectionOptions`](facedetector/#detectionoptions) in FaceDetector documentation for details.\n   */\n  faceDetectorSettings?: object;\n  /**\n   * Callback invoked with results of face detection on the preview.\n   * See [`DetectionResult`](facedetector/#detectionresult) in FaceDetector documentation for more details.\n   * @param faces\n   */\n  onFacesDetected?: (faces: FaceDetectionResult) => void;\n  /**\n   * A URL for an image to be shown while the camera is loading.\n   * @platform web\n   */\n  poster?: string;\n  /**\n   * Whether to allow responsive orientation of the camera when the screen orientation is locked (i.e. when set to `true`\n   * landscape photos will be taken if the device is turned that way, even if the app or device orientation is locked to portrait)\n   * @platform ios\n   */\n  responsiveOrientationWhenOrientationLocked?: boolean;\n  /**\n   * Callback invoked when responsive orientation changes. Only applicable if `responsiveOrientationWhenOrientationLocked` is `true`\n   * @param event result object that contains updated orientation of camera\n   * @platform ios\n   */\n  onResponsiveOrientationChanged?: (event: ResponsiveOrientationChanged) => void;\n};\n\n/**\n * @hidden\n */\nexport type CameraNativeProps = {\n  pointerEvents?: any;\n  style?: any;\n  ref?: Function;\n  onCameraReady?: CameraReadyListener;\n  onMountError?: MountErrorListener;\n  onBarCodeScanned?: (event: { nativeEvent: BarCodeScanningResult }) => void;\n  onFacesDetected?: (event: { nativeEvent: FaceDetectionResult }) => void;\n  onFaceDetectionError?: (event: { nativeEvent: Error }) => void;\n  onPictureSaved?: PictureSavedListener;\n  onResponsiveOrientationChanged?: ResponsiveOrientationChangedListener;\n  type?: number | string;\n  flashMode?: number | string;\n  autoFocus?: string | boolean | number;\n  focusDepth?: number;\n  zoom?: number;\n  whiteBalance?: number | string;\n  pictureSize?: string;\n  barCodeScannerSettings?: BarCodeSettings;\n  faceDetectorSettings?: object;\n  barCodeScannerEnabled?: boolean;\n  faceDetectorEnabled?: boolean;\n  ratio?: string;\n  useCamera2Api?: boolean;\n  poster?: string;\n  responsiveOrientationWhenOrientationLocked?: boolean;\n};\n\n// @docsMissing\nexport type BarCodeSettings = {\n  barCodeTypes: string[];\n  interval?: number;\n};\n\nexport { PermissionResponse, PermissionStatus, PermissionExpiration, PermissionHookOptions };\n"]}