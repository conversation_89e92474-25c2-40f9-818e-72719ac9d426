{"version": 3, "file": "ExpoCamera.js", "sourceRoot": "", "sources": ["../src/ExpoCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAK7D,MAAM,UAAU,GAA2C,wBAAwB,CAAC,YAAY,CAAC,CAAC;AAElG,eAAe,UAAU,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport * as React from 'react';\n\nimport { CameraNativeProps } from './Camera.types';\n\nconst ExpoCamera: React.ComponentType<CameraNativeProps> = requireNativeViewManager('ExpoCamera');\n\nexport default ExpoCamera;\n"]}