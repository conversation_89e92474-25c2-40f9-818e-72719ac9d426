{"version": 3, "file": "Camera.types.js", "sourceRoot": "", "sources": ["../src/Camera.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,GAGjB,MAAM,mBAAmB,CAAC;AA2d3B,OAAO,EAAsB,gBAAgB,EAA+C,CAAC", "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionExpiration,\n  PermissionHookOptions,\n} from 'expo-modules-core';\nimport { Ref } from 'react';\nimport type { ViewProps } from 'react-native';\n\nexport type CameraType = 'front' | 'back';\n\nexport type FlashMode = 'off' | 'on' | 'auto';\n\nexport type ImageType = 'png' | 'jpg';\n\nexport type CameraMode = 'picture' | 'video';\n\n/**\n * This option specifies the mode of focus on the device.\n * - `on` - Indicates that the device should autofocus once and then lock the focus.\n * - `off` - Indicates that the device should automatically focus when needed.\n * @default off\n */\nexport type FocusMode = 'on' | 'off';\n\n/**\n * This option specifies what codec to use when recording a video.\n * @platform ios\n */\nexport type VideoCodec = 'avc1' | 'hvc1' | 'jpeg' | 'apcn' | 'ap4h';\n\n/**\n * This option specifies the stabilization mode to use when recording a video.\n * @platform ios\n */\nexport type VideoStabilization = 'off' | 'standard' | 'cinematic' | 'auto';\n\n// @docsMissing\nexport type VideoQuality = '2160p' | '1080p' | '720p' | '480p' | '4:3';\n\nexport type CameraOrientation =\n  | 'portrait'\n  | 'portraitUpsideDown'\n  | 'landscapeLeft'\n  | 'landscapeRight';\n\n// @docsMissing\n/**\n * @hidden We do not expose related web methods in docs.\n * @platform web\n */\nexport type ImageSize = {\n  width: number;\n  height: number;\n};\n\n// @docsMissing\n/**\n * @hidden We do not expose related web methods in docs.\n * @platform web\n */\nexport type WebCameraSettings = {\n  autoFocus?: string;\n  flashMode?: string;\n  whiteBalance?: string;\n  exposureCompensation?: number;\n  colorTemperature?: number;\n  iso?: number;\n  brightness?: number;\n  contrast?: number;\n  saturation?: number;\n  sharpness?: number;\n  focusDistance?: number;\n  zoom?: number;\n};\n\n// @needsAudit\nexport type CameraCapturedPicture = {\n  /**\n   * Captured image width.\n   */\n  width: number;\n  /**\n   * Captured image height.\n   */\n  height: number;\n  /**\n   * On web, the value of `uri` is the same as `base64` because file system URLs are not supported in the browser.\n   */\n  uri: string;\n  /**\n   * A Base64 representation of the image.\n   */\n  base64?: string;\n  /**\n   * On Android and iOS this object may include various fields based on the device and operating system.\n   * On web, it is a partial representation of the [`MediaTrackSettings`](https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackSettings) dictionary.\n   */\n  exif?: Partial<MediaTrackSettings> | any;\n};\n\n// @needsAudit\nexport type CameraPictureOptions = {\n  /**\n   * Specify the compression quality from `0` to `1`. `0` means compress for small size, and `1` means compress for maximum quality.\n   */\n  quality?: number;\n  /**\n   * Whether to also include the image data in Base64 format.\n   */\n  base64?: boolean;\n  /**\n   * Whether to also include the EXIF data for the image.\n   */\n  exif?: boolean;\n  /**\n   * Additional EXIF data to be included for the image. Only useful when `exif` option is set to `true`.\n   * @platform android\n   * @platform ios\n   */\n  additionalExif?: Record<string, any>;\n  /**\n   * A callback invoked when picture is saved. If set, the promise of this method will resolve immediately with no data after picture is captured.\n   * The data that it should contain will be passed to this callback. If displaying or processing a captured photo right after taking it\n   * is not your case, this callback lets you skip waiting for it to be saved.\n   * @param picture\n   */\n  onPictureSaved?: (picture: CameraCapturedPicture) => void;\n  // TODO(Bacon): Is it possible to implement this in the browser?\n  /**\n   * If set to `true`, camera skips orientation adjustment and returns an image straight from the device's camera.\n   * If enabled, `quality` option is discarded (processing pipeline is skipped as a whole).\n   * Although enabling this option reduces image delivery time significantly, it may cause the image to appear in a wrong orientation\n   * in the `Image` component (at the time of writing, it does not respect EXIF orientation of the images).\n   * > **Note**: Enabling `skipProcessing` would cause orientation uncertainty. `Image` component does not respect EXIF\n   * > stored orientation information, that means obtained image would be displayed wrongly (rotated by 90°, 180° or 270°).\n   * > Different devices provide different orientations. For example some Sony Xperia or Samsung devices don't provide\n   * > correctly oriented images by default. To always obtain correctly oriented image disable `skipProcessing` option.\n   */\n  skipProcessing?: boolean;\n  /**\n   * @platform web\n   */\n  scale?: number;\n  /**\n   * @platform web\n   */\n  imageType?: ImageType;\n  /**\n   * @platform web\n   */\n  isImageMirror?: boolean;\n  /**\n   * @hidden\n   */\n  id?: number;\n  /**\n   * @hidden\n   */\n  fastMode?: boolean;\n  /**\n   * @hidden\n   */\n  maxDownsampling?: number;\n};\n\n// @needsAudit\nexport type CameraRecordingOptions = {\n  /**\n   * Maximum video duration in seconds.\n   */\n  maxDuration?: number;\n  /**\n   * Maximum video file size in bytes.\n   */\n  maxFileSize?: number;\n  /**\n   * If `true`, the recorded video will be flipped along the vertical axis. iOS flips videos recorded with the front camera by default,\n   * but you can reverse that back by setting this to `true`. On Android, this is handled in the user's device settings.\n   * @platform ios\n   */\n  mirror?: boolean;\n  /**\n   * This option specifies what codec to use when recording the video. See [`VideoCodec`](#videocodec) for the possible values.\n   * @platform ios\n   */\n  codec?: VideoCodec;\n};\n\n/**\n * @hidden\n */\nexport type PictureSavedListener = (event: {\n  nativeEvent: { data: CameraCapturedPicture; id: number };\n}) => void;\n\n/**\n * @hidden\n */\nexport type CameraReadyListener = () => void;\n\n/**\n * @hidden\n */\nexport type ResponsiveOrientationChangedListener = (event: {\n  nativeEvent: ResponsiveOrientationChanged;\n}) => void;\n\nexport type ResponsiveOrientationChanged = { orientation: CameraOrientation };\n\n/**\n * @hidden\n */\nexport type MountErrorListener = (event: { nativeEvent: CameraMountError }) => void;\n\n// @docsMissing\nexport type CameraMountError = { message: string };\n\n// @docsMissing\nexport type Point = {\n  x: number;\n  y: number;\n};\n\nexport type BarcodeSize = {\n  /**\n   * The height value.\n   */\n  height: number;\n  /**\n   * The width value.\n   */\n  width: number;\n};\n\n/**\n * These coordinates are represented in the coordinate space of the camera source (e.g. when you\n * are using the camera view, these values are adjusted to the dimensions of the view).\n */\nexport type BarcodePoint = Point;\n\nexport type BarcodeBounds = {\n  /**\n   * The origin point of the bounding box.\n   */\n  origin: BarcodePoint;\n  /**\n   * The size of the bounding box.\n   */\n  size: BarcodeSize;\n};\n\n// @needsAudit\nexport type BarcodeScanningResult = {\n  /**\n   * The barcode type.\n   */\n  type: string;\n  /**\n   * The parsed information encoded in the barcode.\n   */\n  data: string;\n  /**\n   * The raw information encoded in the barcode.\n   * May be different from `data` depending on the barcode type.\n   * @platform android\n   * @hidden\n   */\n  raw?: string;\n  /**\n   * Corner points of the bounding box.\n   * `cornerPoints` is not always available and may be empty. On iOS, for `code39` and `pdf417`\n   * you don't get this value.\n   */\n  cornerPoints: BarcodePoint[];\n  /**\n   * The [BarcodeBounds](#barcodebounds) object.\n   * `bounds` in some case will be representing an empty rectangle.\n   * Moreover, `bounds` doesn't have to bound the whole barcode.\n   * For some types, they will represent the area used by the scanner.\n   */\n  bounds: BarcodeBounds;\n};\n\nexport type ScanningResult = Omit<BarcodeScanningResult, 'bounds'>;\n\n// @needsAudit\nexport type CameraProps = ViewProps & {\n  /**\n   * Camera facing. Use one of `CameraType`. When `front`, use the front-facing camera.\n   * When `back`, use the back-facing camera.\n   * @default 'back'\n   */\n  facing?: CameraType;\n  /**\n   * Camera flash mode. Use one of `FlashMode` values. When `on`, the flash on your device will\n   * turn on when taking a picture. When `off`, it won't. Setting it to `auto` will fire flash if required.\n   * @default 'off'\n   */\n  flash?: FlashMode;\n  /**\n   * A value between `0` and `1` being a percentage of device's max zoom. `0` - not zoomed, `1` - maximum zoom.\n   * @default 0\n   */\n  zoom?: number;\n  /**\n   * Used to select image or video output\n   * @default 'picture'\n   */\n  mode?: CameraMode;\n  /**\n   * If present, video will be recorded with no sound.\n   * @default false\n   */\n  mute?: boolean;\n  /**\n   * Indicates the focus mode to use.\n   * @default off\n   * @platform ios\n   */\n  autofocus?: FocusMode;\n  /**\n   * Specify the quality of the recorded video. Use one of `VideoQuality` possible values:\n   * for 16:9 resolution `2160p`, `1080p`, `720p`, `480p` : `Android only` and for 4:3 `4:3` (the size is 640x480).\n   * If the chosen quality is not available for a device, the highest available is chosen.\n   */\n  videoQuality?: VideoQuality;\n  /**\n   * A boolean that determines whether the camera shutter animation should be enabled.\n   * @default true\n   */\n  animateShutter?: boolean;\n  /**\n   * A string representing the size of pictures [`takePictureAsync`](#takepictureasync) will take.\n   * Available sizes can be fetched with [`getAvailablePictureSizes`](#getavailablepicturesizes).\n   */\n  pictureSize?: string;\n  /**\n   * A boolean to enable or disable the torch\n   * @default false\n   */\n  enableTorch?: boolean;\n  /**\n   * Callback invoked when camera preview has been set.\n   */\n  onCameraReady?: () => void;\n  /**\n   * The video stabilization mode used for a video recording. Use one of [`VideoStabilization.<value>`](#videostabilization).\n   * You can read more about each stabilization type in [Apple Documentation](https://developer.apple.com/documentation/avfoundation/avcapturevideostabilizationmode).\n   * @platform ios\n   */\n  videoStabilizationMode?: VideoStabilization;\n  /**\n   * Callback invoked when camera preview could not been started.\n   * @param event Error object that contains a `message`.\n   */\n  onMountError?: (event: CameraMountError) => void;\n  /**\n   * @example\n   * ```tsx\n   * <CameraView\n   *   barcodeScannerSettings={{\n   *     barcodeTypes: [\"qr\"],\n   *   }}\n   * />\n   * ```\n   */\n  barcodeScannerSettings?: BarcodeSettings;\n  /**\n   * Callback that is invoked when a barcode has been successfully scanned. The callback is provided with\n   * an object of the [`BarcodeScanningResult`](#barcodescanningresult) shape, where the `type`\n   * refers to the barcode type that was scanned and the `data` is the information encoded in the barcode\n   * (in this case of QR codes, this is often a URL). See [`BarcodeType`](#barcodetype) for supported values.\n   * for supported values.\n   * @param scanningResult\n   */\n  onBarcodeScanned?: (scanningResult: BarcodeScanningResult) => void;\n  /**\n   * A URL for an image to be shown while the camera is loading.\n   * @platform web\n   */\n  poster?: string;\n  /**\n   * Whether to allow responsive orientation of the camera when the screen orientation is locked (i.e. when set to `true`\n   * landscape photos will be taken if the device is turned that way, even if the app or device orientation is locked to portrait)\n   * @platform ios\n   */\n  responsiveOrientationWhenOrientationLocked?: boolean;\n  /**\n   * Callback invoked when responsive orientation changes. Only applicable if `responsiveOrientationWhenOrientationLocked` is `true`\n   * @param event result object that contains updated orientation of camera\n   * @platform ios\n   */\n  onResponsiveOrientationChanged?: (event: ResponsiveOrientationChanged) => void;\n};\n\n/**\n * @hidden\n */\nexport interface CameraViewRef {\n  readonly takePicture: (options: CameraPictureOptions) => Promise<CameraCapturedPicture>;\n  readonly getAvailablePictureSizes: () => Promise<string[]>;\n  readonly record: (options?: CameraRecordingOptions) => Promise<{ uri: string }>;\n  readonly stopRecording: () => Promise<void>;\n  readonly launchModernScanner: () => Promise<void>;\n}\n\n/**\n * @hidden\n */\nexport type CameraNativeProps = {\n  pointerEvents?: any;\n  style?: any;\n  ref?: Ref<CameraViewRef>;\n  onCameraReady?: CameraReadyListener;\n  onMountError?: MountErrorListener;\n  onBarcodeScanned?: (event: { nativeEvent: BarcodeScanningResult }) => void;\n  onPictureSaved?: PictureSavedListener;\n  onResponsiveOrientationChanged?: ResponsiveOrientationChangedListener;\n  facing?: string;\n  flashMode?: string;\n  enableTorch?: boolean;\n  animateShutter?: boolean;\n  autoFocus?: FocusMode;\n  mute?: boolean;\n  zoom?: number;\n  barcodeScannerSettings?: BarcodeSettings;\n  barcodeScannerEnabled?: boolean;\n  poster?: string;\n  responsiveOrientationWhenOrientationLocked?: boolean;\n};\n\n// @docsMissing\nexport type BarcodeSettings = {\n  barcodeTypes: BarcodeType[];\n};\n\n/**\n * @platform ios\n */\nexport type ScanningOptions = {\n  /**\n   * The type of codes to scan for.\n   */\n  barcodeTypes: BarcodeType[];\n  /**\n   * Indicates whether people can use a two-finger pinch-to-zoom gesture.\n   * @default true\n   */\n  isPinchToZoomEnabled?: boolean;\n  /**\n   * Guidance text, such as “Slow Down,” appears over the live video.\n   * @default true\n   */\n  isGuidanceEnabled?: boolean;\n  /**\n   * Indicates whether the scanner displays highlights around recognized items.\n   * @default false\n   */\n  isHighlightingEnabled?: boolean;\n};\n\n/**\n * The available barcode types that can be scanned.\n */\nexport type BarcodeType =\n  | 'aztec'\n  | 'ean13'\n  | 'ean8'\n  | 'qr'\n  | 'pdf417'\n  | 'upc_e'\n  | 'datamatrix'\n  | 'code39'\n  | 'code93'\n  | 'itf14'\n  | 'codabar'\n  | 'code128'\n  | 'upc_a';\n\nexport { PermissionResponse, PermissionStatus, PermissionExpiration, PermissionHookOptions };\n"]}