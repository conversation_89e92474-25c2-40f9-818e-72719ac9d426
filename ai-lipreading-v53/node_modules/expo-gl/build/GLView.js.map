{"version": 3, "file": "GLView.js", "sourceRoot": "", "sources": ["../src/GLView.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,wBAAwB,EACxB,UAAU,GACX,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAE9D,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAS7C,OAAO,EAAE,2BAA2B,EAAE,MAAM,2BAA2B,CAAC;AASxE,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;AAC/E,MAAM,EAAE,qBAAqB,EAAE,GAAG,kBAAkB,CAAC;AAErD,MAAM,UAAU,GAAG,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;AAC9D,MAAM,qBAAqB,GAAG,2BAA2B,EAAE,CAAC;AAE5D,MAAM,UAAU,iBAAiB,CAAC,SAAiB;IACjD,SAAS,CAAC;IACV,OAAO,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACrD,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,OAAO,MAAO,SAAQ,KAAK,CAAC,SAAsB;IACtD,MAAM,CAAC,UAAU,CAAM;IAEvB,MAAM,CAAC,YAAY,GAAG;QACpB,WAAW,EAAE,CAAC;QACd,gCAAgC,EAAE,KAAK;KACxC,CAAC;IAEF;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;QACzE,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAyC;QACxE,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACrC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC/B,OAAO,uBAAuB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAChE,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,IAAyC,EACzC,UAA2B,EAAE;QAE7B,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,uBAAuB,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,iBAAiB,GACtB,qBAAqB,CAAC,UAAU,CAAC;IAEnC,SAAS,GAAsB,IAAI,CAAC;IACpC,SAAS,CAAU;IAEnB,MAAM;QACJ,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,gCAAgC,EAAE,GAAG,SAAS,EAAE,GACpF,IAAI,CAAC,KAAK,CAAC;QAEb,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAClB;QAAA,CAAC,UAAU,CACT,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CACxB,KAAK,CAAC,CAAC;gBACL,IAAI,EAAE,CAAC;gBACP,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,KAAK;oBACvB,CAAC,CAAC;wBACE,eAAe,EAAE,aAAa;qBAC/B;oBACH,CAAC,CAAC,EAAE,CAAC;aACR,CAAC,CACF,eAAe,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACvC,gCAAgC,CAAC,CAAC,gCAAgC,CAAC,CACnE,WAAW,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,EAEjE;MAAA,EAAE,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;IAED,aAAa,GAAG,CAAC,SAA4B,EAAQ,EAAE;QACrD,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC,CAAC;IAEF,gBAAgB,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,EAAsB,EAAQ,EAAE;QAC9E,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;QAE5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAEF,oBAAoB;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,SAAsB;QACvC,IACE,IAAI,CAAC,KAAK,CAAC,gCAAgC,KAAK,SAAS,CAAC,gCAAgC,EAC1F,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,eAAe;IACf,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;YAC/C,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,MAAM,qBAAqB,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,eAAe;IACf,KAAK,CAAC,wBAAwB,CAAC,iBAAoC;QACjE,IAAI,CAAC,uBAAuB,CAAC,wBAAwB,EAAE,CAAC;YACtD,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,SAAS,GAAG,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACpD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,uBAAuB,CAAC,wBAAwB,CAC1E,SAAS,EACT,SAAS,CACV,CAAC;QACF,OAAO,EAAE,EAAE,EAAE,SAAS,EAAkB,CAAC;IAC3C,CAAC;IAED,eAAe;IACf,KAAK,CAAC,kBAAkB,CAAC,QAAqB;QAC5C,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;YAChD,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,MAAM,uBAAuB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAA2B,EAAE;QACnD,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC9B,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QACD,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC3B,OAAO,MAAM,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;;AAGH,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;AAE/B,SAAS,mBAAmB,CAAC,SAAiB;IAC5C,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,OAAO,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAClD,CAAC;IACD,qBAAqB,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,CAAC;AAChD,CAAC;AAED,6CAA6C;AAC7C,MAAM,KAAK,GAAG,CAAC,SAAiB,EAA6B,EAAE;IAC7D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAC3B,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,mHAAmH,CACpH,CAAC;IACJ,CAAC;IACD,MAAM,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAEpD,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAErB,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,IAAyC,EAAU,EAAE;IACzE,MAAM,SAAS,GAAG,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;IAE3E,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CAAC,2BAA2B,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAClE,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import {\n  NativeModulesProxy,\n  UnavailabilityError,\n  requireNativeModule,\n  requireNativeViewManager,\n  CodedError,\n} from 'expo-modules-core';\nimport * as React from 'react';\nimport { Platform, View, findNodeHandle } from 'react-native';\n\nimport { configureLogging } from './GLUtils';\nimport {\n  ComponentOrHandle,\n  SurfaceCreateEvent,\n  GLSnapshot,\n  ExpoWebGLRenderingContext,\n  SnapshotOptions,\n  GLViewProps,\n} from './GLView.types';\nimport { createWorkletContextManager } from './GLWorkletContextManager';\n\n// @docsMissing\nexport type WebGLObject = {\n  id: number;\n};\n\ndeclare let global: any;\n\nconst ExponentGLObjectManager = requireNativeModule('ExponentGLObjectManager');\nconst { ExponentGLViewManager } = NativeModulesProxy;\n\nconst NativeView = requireNativeViewManager('ExponentGLView');\nconst workletContextManager = createWorkletContextManager();\n\nexport function getWorkletContext(contextId: number): ExpoWebGLRenderingContext | undefined {\n  'worklet';\n  return workletContextManager.getContext(contextId);\n}\n\n// @needsAudit\n/**\n * A View that acts as an OpenGL ES render target. On mounting, an OpenGL ES context is created.\n * Its drawing buffer is presented as the contents of the View every frame.\n */\nexport class GLView extends React.Component<GLViewProps> {\n  static NativeView: any;\n\n  static defaultProps = {\n    msaaSamples: 4,\n    enableExperimentalWorkletSupport: false,\n  };\n\n  /**\n   * Imperative API that creates headless context which is devoid of underlying view.\n   * It's useful for headless rendering or in case you want to keep just one context per application and share it between multiple components.\n   * It is slightly faster than usual context as it doesn't swap framebuffers and doesn't present them on the canvas,\n   * however it may require you to take a snapshot in order to present its results.\n   * Also, keep in mind that you need to set up a viewport and create your own framebuffer and texture that you will be drawing to, before you take a snapshot.\n   * @return A promise that resolves to WebGL context object. See [WebGL API](#webgl-api) for more details.\n   */\n  static async createContextAsync(): Promise<ExpoWebGLRenderingContext> {\n    const { exglCtxId } = await ExponentGLObjectManager.createContextAsync();\n    return getGl(exglCtxId);\n  }\n\n  /**\n   * Destroys given context.\n   * @param exgl WebGL context to destroy.\n   * @return A promise that resolves to boolean value that is `true` if given context existed and has been destroyed successfully.\n   */\n  static async destroyContextAsync(exgl?: ExpoWebGLRenderingContext | number): Promise<boolean> {\n    const exglCtxId = getContextId(exgl);\n    unregisterGLContext(exglCtxId);\n    return ExponentGLObjectManager.destroyContextAsync(exglCtxId);\n  }\n\n  /**\n   * Takes a snapshot of the framebuffer and saves it as a file to app's cache directory.\n   * @param exgl WebGL context to take a snapshot from.\n   * @param options\n   * @return A promise that resolves to `GLSnapshot` object.\n   */\n  static async takeSnapshotAsync(\n    exgl?: ExpoWebGLRenderingContext | number,\n    options: SnapshotOptions = {}\n  ): Promise<GLSnapshot> {\n    const exglCtxId = getContextId(exgl);\n    return ExponentGLObjectManager.takeSnapshotAsync(exglCtxId, options);\n  }\n\n  /**\n   * This method doesn't work inside of the worklets with new reanimated versions.\n   * @deprecated Use `getWorkletContext` from the global scope instead.\n   */\n  static getWorkletContext: (contextId: number) => ExpoWebGLRenderingContext | undefined =\n    workletContextManager.getContext;\n\n  nativeRef: ComponentOrHandle = null;\n  exglCtxId?: number;\n\n  render() {\n    const { onContextCreate, msaaSamples, enableExperimentalWorkletSupport, ...viewProps } =\n      this.props;\n\n    return (\n      <View {...viewProps}>\n        <NativeView\n          ref={this._setNativeRef}\n          style={{\n            flex: 1,\n            ...(Platform.OS === 'ios'\n              ? {\n                  backgroundColor: 'transparent',\n                }\n              : {}),\n          }}\n          onSurfaceCreate={this._onSurfaceCreate}\n          enableExperimentalWorkletSupport={enableExperimentalWorkletSupport}\n          msaaSamples={Platform.OS === 'ios' ? msaaSamples : undefined}\n        />\n      </View>\n    );\n  }\n\n  _setNativeRef = (nativeRef: ComponentOrHandle): void => {\n    if (this.props.nativeRef_EXPERIMENTAL) {\n      this.props.nativeRef_EXPERIMENTAL(nativeRef);\n    }\n    this.nativeRef = nativeRef;\n  };\n\n  _onSurfaceCreate = ({ nativeEvent: { exglCtxId } }: SurfaceCreateEvent): void => {\n    const gl = getGl(exglCtxId);\n\n    this.exglCtxId = exglCtxId;\n\n    if (this.props.onContextCreate) {\n      this.props.onContextCreate(gl);\n    }\n  };\n\n  componentWillUnmount(): void {\n    if (this.exglCtxId) {\n      unregisterGLContext(this.exglCtxId);\n    }\n  }\n\n  componentDidUpdate(prevProps: GLViewProps): void {\n    if (\n      this.props.enableExperimentalWorkletSupport !== prevProps.enableExperimentalWorkletSupport\n    ) {\n      console.warn('Updating prop enableExperimentalWorkletSupport is not supported');\n    }\n  }\n\n  // @docsMissing\n  async startARSessionAsync(): Promise<any> {\n    if (!ExponentGLViewManager.startARSessionAsync) {\n      throw new UnavailabilityError('expo-gl', 'startARSessionAsync');\n    }\n    return await ExponentGLViewManager.startARSessionAsync(findNodeHandle(this.nativeRef));\n  }\n\n  // @docsMissing\n  async createCameraTextureAsync(cameraRefOrHandle: ComponentOrHandle): Promise<WebGLTexture> {\n    if (!ExponentGLObjectManager.createCameraTextureAsync) {\n      throw new UnavailabilityError('expo-gl', 'createCameraTextureAsync');\n    }\n\n    const { exglCtxId } = this;\n\n    if (!exglCtxId) {\n      throw new Error(\"GLView's surface is not created yet!\");\n    }\n\n    const cameraTag = findNodeHandle(cameraRefOrHandle);\n    const { exglObjId } = await ExponentGLObjectManager.createCameraTextureAsync(\n      exglCtxId,\n      cameraTag\n    );\n    return { id: exglObjId } as WebGLTexture;\n  }\n\n  // @docsMissing\n  async destroyObjectAsync(glObject: WebGLObject): Promise<boolean> {\n    if (!ExponentGLObjectManager.destroyObjectAsync) {\n      throw new UnavailabilityError('expo-gl', 'destroyObjectAsync');\n    }\n    return await ExponentGLObjectManager.destroyObjectAsync(glObject.id);\n  }\n\n  /**\n   * Same as static [`takeSnapshotAsync()`](#takesnapshotasyncoptions),\n   * but uses WebGL context that is associated with the view on which the method is called.\n   * @param options\n   */\n  async takeSnapshotAsync(options: SnapshotOptions = {}): Promise<GLSnapshot> {\n    if (!GLView.takeSnapshotAsync) {\n      throw new UnavailabilityError('expo-gl', 'takeSnapshotAsync');\n    }\n    const { exglCtxId } = this;\n    return await GLView.takeSnapshotAsync(exglCtxId, options);\n  }\n}\n\nGLView.NativeView = NativeView;\n\nfunction unregisterGLContext(exglCtxId: number) {\n  if (global.__EXGLContexts) {\n    delete global.__EXGLContexts[String(exglCtxId)];\n  }\n  workletContextManager.unregister?.(exglCtxId);\n}\n\n// Get the GL interface from an EXGLContextId\nconst getGl = (exglCtxId: number): ExpoWebGLRenderingContext => {\n  if (!global.__EXGLContexts) {\n    throw new CodedError(\n      'ERR_GL_NOT_AVAILABLE',\n      'GL is currently not available. (Have you enabled remote debugging? GL is not available while debugging remotely.)'\n    );\n  }\n  const gl = global.__EXGLContexts[String(exglCtxId)];\n\n  configureLogging(gl);\n\n  return gl;\n};\n\nconst getContextId = (exgl?: ExpoWebGLRenderingContext | number): number => {\n  const exglCtxId = exgl && typeof exgl === 'object' ? exgl.contextId : exgl;\n\n  if (!exglCtxId || typeof exglCtxId !== 'number') {\n    throw new Error(`Invalid EXGLContext id: ${String(exglCtxId)}`);\n  }\n  return exglCtxId;\n};\n"]}