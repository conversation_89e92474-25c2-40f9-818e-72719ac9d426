{"version": 3, "file": "GLUtils.js", "sourceRoot": "", "sources": ["../src/GLUtils.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,EAA6B,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAE5E;;GAEG;AACH,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,EAA6B;IAC5D,kDAAkD;IAClD,IAAI,aAAa,GAAG,eAAe,CAAC,QAAQ,CAAC;IAE7C,EAAE,CAAC,gBAAgB,GAAG,CAAC,MAAuB,EAAQ,EAAE;QACtD,kEAAkE;QAClE,0DAA0D;QAC1D,IAAI,CAAC,aAAa,KAAK,CAAC,MAAM,EAAE,CAAC;YAC/B,aAAa,GAAG,MAAM,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,EAA2C,CAAC;QAEzD,oBAAoB;QACpB,IAAI,MAAM,KAAK,eAAe,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC5C,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;oBACtD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC,CAAC,CAAC;YACH,aAAa,GAAG,MAAM,CAAC;YACvB,OAAO;QACT,CAAC;QAED,mBAAmB;QACnB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,aAAa,CAAC,EAAE,EAAE;YAC3E,IAAI,OAAO,aAAa,KAAK,UAAU,IAAI,GAAG,KAAK,kBAAkB,EAAE,CAAC;gBACtE,OAAO;YACT,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;gBAC7B,IAAI,aAAa,GAAG,eAAe,CAAC,YAAY,EAAE,CAAC;oBACjD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;wBAC9B,sFAAsF;wBACtF,0EAA0E;wBAC1E,oFAAoF;wBACpF,IAAI,aAAa,GAAG,eAAe,CAAC,iBAAiB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;4BACjF,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;gCACxB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;oCACvB,OAAO,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;gCAC5B,CAAC;4BACH,CAAC;wBACH,CAAC;wBAED,qFAAqF;wBACrF,2DAA2D;wBAC3D,IAAI,aAAa,GAAG,eAAe,CAAC,gBAAgB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;4BAChF,IAAI,GAAG,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;gCACnC,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;gCAC1D,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,KAAK,CAAC;4BAC/E,CAAC;wBACH,CAAC;wBAED,yCAAyC;wBACzC,OAAO,EAAE,GAAG,GAAG,CAAC;oBAClB,CAAC,CAAC,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACtD,CAAC;gBAED,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAE/C,IAAI,aAAa,GAAG,eAAe,CAAC,YAAY,EAAE,CAAC;oBACjD,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,EAAE,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,aAAa,GAAG,eAAe,CAAC,UAAU,IAAI,GAAG,KAAK,UAAU,EAAE,CAAC;oBACrE,2DAA2D;oBAC3D,oCAAoC;oBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAEjD,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACrC,8EAA8E;wBAC9E,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,CAAC,KAA8B,CAAC,SAAS,CAAC,CAAC;oBAC1F,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;gBAC9B,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,aAAa,GAAG,MAAM,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import GLErrors from './GLErrors';\nimport { ExpoWebGLRenderingContext, GLLoggingOption } from './GLView.types';\n\n/**\n * Maximum length of the strings printed to the console.\n */\nconst MAX_STRING_LENGTH = 20;\n\n/**\n * Sets up `__expoSetLogging` method providing some logging options useful when debugging GL calls.\n */\nexport function configureLogging(gl: ExpoWebGLRenderingContext): void {\n  // Enable/disable logging of all GL function calls\n  let loggingOption = GLLoggingOption.DISABLED;\n\n  gl.__expoSetLogging = (option: GLLoggingOption): void => {\n    // If boolean values are the same, just change the internal value,\n    // there is no need to wrap/unwrap functions in this case.\n    if (!loggingOption === !option) {\n      loggingOption = option;\n      return;\n    }\n\n    const __gl = gl as Record<keyof typeof gl | string, any>;\n\n    // Turn off logging.\n    if (option === GLLoggingOption.DISABLED || !option) {\n      Object.entries(__gl).forEach(([key, value]) => {\n        if (typeof value === 'function' && value.__logWrapper) {\n          delete __gl[key];\n        }\n      });\n      loggingOption = option;\n      return;\n    }\n\n    // Turn on logging.\n    Object.entries(Object.getPrototypeOf(__gl)).forEach(([key, originalValue]) => {\n      if (typeof originalValue !== 'function' || key === '__expoSetLogging') {\n        return;\n      }\n\n      __gl[key] = (...args: any[]) => {\n        if (loggingOption & GLLoggingOption.METHOD_CALLS) {\n          const params = args.map((arg) => {\n            // If the type is `number`, then try to find name of the constant that has such value,\n            // so it's easier to read these logs. In some cases it might be misleading\n            // if the parameter is for example a width or height, so the number is still logged.\n            if (loggingOption & GLLoggingOption.RESOLVE_CONSTANTS && typeof arg === 'number') {\n              for (const prop in __gl) {\n                if (__gl[prop] === arg) {\n                  return `${arg} (${prop})`;\n                }\n              }\n            }\n\n            // Truncate strings so they don't produce too much output and don't block the bridge.\n            // It mostly applies to shaders which might be very long...\n            if (loggingOption & GLLoggingOption.TRUNCATE_STRINGS && typeof arg === 'string') {\n              if (arg.length > MAX_STRING_LENGTH) {\n                const lastIndex = arg.lastIndexOf(' ', MAX_STRING_LENGTH);\n                return arg.substr(0, lastIndex >= 0 ? lastIndex : MAX_STRING_LENGTH) + '...';\n              }\n            }\n\n            // Just return the parameter as a string.\n            return '' + arg;\n          });\n          console.log(`ExpoGL: ${key}(${params.join(', ')})`);\n        }\n\n        const result = originalValue.apply(__gl, args);\n\n        if (loggingOption & GLLoggingOption.METHOD_CALLS) {\n          console.log(`ExpoGL:   = ${result}`);\n        }\n        if (loggingOption & GLLoggingOption.GET_ERRORS && key !== 'getError') {\n          // @ts-ignore We need to call into the original `getError`.\n          // eslint-disable-next-line no-proto\n          const error = __gl.__proto__.getError.call(__gl);\n\n          if (error && error !== __gl.NO_ERROR) {\n            // `console.error` would cause a red screen, so let's just log with red color.\n            console.log(`\\x1b[31mExpoGL: Error ${GLErrors[error as keyof typeof GLErrors]}\\x1b[0m`);\n          }\n        }\n        __gl[key].__logWrapper = true;\n        return result;\n      };\n    });\n\n    loggingOption = option;\n  };\n}\n"]}