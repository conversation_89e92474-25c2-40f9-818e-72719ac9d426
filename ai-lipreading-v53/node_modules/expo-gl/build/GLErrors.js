const GLErrors = {
    1280: 'INVALID ENUM: An unacceptable value has been specified for an enumerated argument.',
    1281: 'INVALID_VALUE: A numeric argument is out of range.',
    1282: 'INVALID_OPERATION: The specified command is not allowed for the current state.',
    1285: 'OUT_OF_MEMORY: Not enough memory is left to execute the command.',
    1286: 'INVALID_FRAMEBUFFER_OPERATION: The currently bound framebuffer is not complete when trying to render to or to read from it.',
    37442: 'CONTEXT_LOST_WEBGL: The WebGL context is lost.',
};
export default GLErrors;
//# sourceMappingURL=GLErrors.js.map