{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["// WebGLObject was removed from lib.dom.ts in v4.2 - copied from the old lib.dom.ts\n// ref: https://github.com/microsoft/TypeScript/blob/bc76e7f03c6a80706246f7c35e392bfc88a93ac3/lib/lib.dom.d.ts#L17355-L17361\n/**\n * @hidden\n */\nexport type WebGLObject = object;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\ndeclare let WebGLObject: {\n  prototype: WebGLObject;\n  new (): WebGLObject;\n};\n"]}