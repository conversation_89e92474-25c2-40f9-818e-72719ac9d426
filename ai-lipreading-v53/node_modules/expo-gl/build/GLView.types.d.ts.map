{"version": 3, "file": "GLView.types.d.ts", "sourceRoot": "", "sources": ["../src/GLView.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,OAAO,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAGzC,MAAM,MAAM,kBAAkB,GAAG;IAC/B,WAAW,EAAE;QACX,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;CACH,CAAC;AAGF,MAAM,MAAM,eAAe,GAAG;IAC5B;;;OAGG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;IACf;;;OAGG;IACH,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAC/B;;OAEG;IACH,IAAI,CAAC,EAAE;QACL,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;IACjC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAGF,MAAM,MAAM,UAAU,GAAG;IACvB;;OAEG;IACH,GAAG,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1B;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAGF,MAAM,WAAW,yBAA0B,SAAQ,sBAAsB;IACvE,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,IAAI,IAAI,CAAC;IACpB,QAAQ,IAAI,IAAI,CAAC;IACjB,gBAAgB,CAAC,MAAM,EAAE,eAAe,GAAG,IAAI,CAAC;IAEhD,gBAAgB;IAChB,mBAAmB,CAAC,CAAC,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAC5C,gBAAgB,CAAC,CAAC,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;CAC1C;AAGD,MAAM,MAAM,iBAAiB,GAAG,IAAI,GAAG,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAG1F,MAAM,MAAM,WAAW,GAAG;IACxB;;;OAGG;IACH,eAAe,CAAC,EAAE,EAAE,yBAAyB,GAAG,IAAI,CAAC;IACrD;;;;;OAKG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;;OAGG;IACH,gCAAgC,EAAE,OAAO,CAAC;IAC1C;;;OAGG;IACH,sBAAsB,CAAC,CAAC,QAAQ,EAAE,iBAAiB,GAAG,IAAI,GAAG,IAAI,CAAC;CACnE,GAAG,SAAS,CAAC;AAGd,oBAAY,eAAe;IACzB;;OAEG;IACH,QAAQ,IAAI;IACZ;;OAEG;IACH,YAAY,IAAI;IAChB;;;OAGG;IACH,UAAU,IAAI;IACd;;OAEG;IACH,iBAAiB,IAAI;IACrB;;;OAGG;IACH,gBAAgB,IAAI;IACpB;;OAEG;IACH,GAAG,KAAmE;CACvE"}