{"version": 3, "file": "GLWorkletContextManager.js", "sourceRoot": "", "sources": ["../src/GLWorkletContextManager.ts"], "names": [], "mappings": "AAEA,6EAA6E;AAC7E,iFAAiF;AACjF,qBAAqB;AACrB,EAAE;AACF,6EAA6E;AAC7E,sFAAsF;AACtF,mFAAmF;AACnF,8CAA8C;AAC9C,MAAM,UAAU,2BAA2B;IAIzC,IAAI,CAAC;QACH,8DAA8D;QAC9D,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;QACvD,OAAO;YACL,UAAU,EAAE,CAAC,SAAiB,EAAyC,EAAE;gBACvE,SAAS,CAAC;gBACV,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;YACpD,CAAC;YACD,UAAU,EAAE,CAAC,SAAiB,EAAQ,EAAE;gBACtC,OAAO,CAAC,CAAC,SAAiB,EAAE,EAAE;oBAC5B,SAAS,CAAC;oBACV,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;wBAC1B,OAAO,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;oBAClD,CAAC;gBACH,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAChB,CAAC;SACF,CAAC;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,OAAO;YACL,UAAU,EAAE,GAAG,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;SACF,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["import { ExpoWebGLRenderingContext } from './GLView.types';\n\n// This method needs to be in a separate file because react-native-reanimated\n// import wrapped in try catch does not work correctly with inlineRequires option\n// in metro.config.js\n//\n// It looks like in generated bundle \"react-native-reanimated\" is not present\n// in _dependencyMap, but references to it count it as if it was, e.g. bundle contains\n// a line \"(0, _$$_REQUIRE(_dependencyMap[15], \"./GLUtils\").configureLogging)(gl);\"\n// but dependencyMap contains only 15 elements\nexport function createWorkletContextManager(): {\n  getContext: (contextId: number) => ExpoWebGLRenderingContext | undefined;\n  unregister?: (contextId: number) => void;\n} {\n  try {\n    // reanimated needs to be imported before any workletized code\n    // is created, but we don't want to make it dependency on expo-gl.\n    const { runOnUI } = require('react-native-reanimated');\n    return {\n      getContext: (contextId: number): ExpoWebGLRenderingContext | undefined => {\n        'worklet';\n        return global.__EXGLContexts?.[String(contextId)];\n      },\n      unregister: (contextId: number): void => {\n        runOnUI((contextId: number) => {\n          'worklet';\n          if (global.__EXGLContexts) {\n            delete global.__EXGLContexts[String(contextId)];\n          }\n        })(contextId);\n      },\n    };\n  } catch {\n    return {\n      getContext: () => {\n        throw new Error('Worklet runtime is not available');\n      },\n    };\n  }\n}\n"]}