{"version": 3, "file": "GLView.web.js", "sourceRoot": "", "sources": ["../src/GLView.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAC9E,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,MAAM,MAAM,UAAU,CAAC;AAU9B,SAAS,gBAAgB,CAAC,KAIzB;IACC,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;QACtE,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAY;IACrC,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,aAAa,KAAK,UAAU,CAAC;AAChE,CAAC;AAED,SAAS,aAAa,CAAC,EAA6B;IAClD,EAAE,CAAC,WAAW,GAAG,SAAS,aAAa,KAAU,CAAC,CAAC;IAEnD,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;QACvC,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,KAAY,EAAO,EAAE;YACvC,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAC7B,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC,kBAAkB,CAAE,CAAC,GAAG,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,EAAE,CAAC,qBAAqB,CAAC,EAAE,CAAC;QAC/B,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC;QAC7C,EAAE,CAAC,aAAa,GAAG,CAAC,GAAG,KAAY,EAAO,EAAE;YAC1C,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAC7B,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAClD,OAAO,EAAE,CAAC,qBAAqB,CAAE,CAAC,GAAG,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,aAAa,CACpB,MAA0B,EAC1B,iBAA0C;IAE1C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,UAAU,CAClB,gBAAgB,EAChB,8DAA8D,CAC/D,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GACX,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,CAAC;QAC9C,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC;QAC7C,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;QAC1D,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;IAC7D,SAAS,CAAC,OAAO,EAAE,gCAAgC,CAAC,CAAC;IACrD,OAAO,aAAa,CAAC,OAAoC,CAAC,CAAC;AAC7D,CAAC;AAYD,KAAK,UAAU,gCAAgC,CAC7C,EAAyB,EACzB,UAA2B,EAAE;IAE7B,SAAS,CAAC,EAAE,EAAE,4EAA4E,CAAC,CAAC;IAE5F,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IAEtB,IAAI,IAAI,GAAgB,IAAI,CAAC;IAE7B,IAAI,OAAQ,MAAc,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;QACnD,qEAAqE;QACrE,IAAI,GAAG,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC;IACjC,CAAC;SAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;QACrC,IAAI,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACzF,CAAC;SAAM,CAAC;QACN,IAAI,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAiB,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,IAAI;QACJ,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,MAAM,EAAE,MAAM,CAAC,MAAM;KACtB,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,MAAO,SAAQ,KAAK,CAAC,SAAyB;IACzD,MAAM,CAAqB;IAE3B,EAAE,CAAyB;IAE3B,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QAC7B,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC/B,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAqC;QACpE,aAAa;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAC5B,EAAyB,EACzB,UAA2B,EAAE;QAE7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,gCAAgC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAEpF,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,UAAU,CAAC,iBAAiB,EAAE,+BAA+B,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO;YACL,GAAG,EAAE,IAAI;YACT,QAAQ,EAAE,EAAE;YACZ,KAAK;YACL,MAAM;SACP,CAAC;IACJ,CAAC;IAED,oBAAoB;QAClB,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,MAAM,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAClE,IAAI,cAAc,EAAE,CAAC;gBACnB,cAAc,CAAC,WAAW,EAAE,CAAC;YAC/B,CAAC;YACD,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACtB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,aAAa,EACb,sBAAsB,EACtB,WAAW,EACX,sBAAsB;QACtB,iCAAiC;QACjC,GAAG,EACH,GAAG,QAAQ,EACZ,GAAG,IAAI,CAAC,KAAK,CAAC;QAEf,OAAO,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAG,CAAC;IAChE,CAAC;IAED,kBAAkB,CAAC,SAAyB;QAC1C,MAAM,EAAE,sBAAsB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9C,IAAI,IAAI,CAAC,MAAM,IAAI,sBAAsB,KAAK,SAAS,CAAC,sBAAsB,EAAE,CAAC;YAC/E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/B,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,MAAM,IAAI,UAAU,CAClB,gBAAgB,EAChB,8DAA8D,CAC/D,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,aAAa,GAAG,CAAC,KAAmB,EAAQ,EAAE;QACpD,IAAI,KAAK,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YAClC,KAAK,CAAC,cAAc,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QAEpB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK,UAAU,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IAEM,iBAAiB,GAAG,GAAS,EAAE;QACrC,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,UAAU,CAAC,gBAAgB,EAAE,+BAA+B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC,CAAC;IAEM,YAAY;QAClB,IAAI,IAAI,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YACxE,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;gBACrD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,GAAG,CAAC,MAAyB,EAAQ,EAAE;QACzD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,UAAU,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE7E,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC,CAAC;IAEK,KAAK,CAAC,iBAAiB,CAAC,UAA2B,EAAE;QAC1D,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC9B,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACvC,OAAO,MAAM,MAAM,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAEM,KAAK,CAAC,mBAAmB;QAC9B,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK,CAAC,wBAAwB;QACnC,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,0BAA0B,CAAC,CAAC;IACtE,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,QAAqB;QACnD,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;IAChE,CAAC;CACF", "sourcesContent": ["import { CodedError, Platform, UnavailabilityError } from 'expo-modules-core';\nimport invariant from 'invariant';\nimport * as React from 'react';\nimport { Dimensions } from 'react-native';\n\nimport Canvas from './Canvas';\nimport { WebGLObject } from './GLView';\nimport {\n  GLViewProps,\n  ExpoWebGLRenderingContext,\n  GLSnapshot,\n  SnapshotOptions,\n  ComponentOrHandle,\n} from './GLView.types';\n\nfunction getImageForAsset(asset: {\n  downloadAsync?: () => Promise<any>;\n  uri?: string;\n  localUri?: string;\n}): HTMLImageElement | any {\n  if (asset != null && typeof asset === 'object' && asset.downloadAsync) {\n    const dataURI = asset.localUri || asset.uri || '';\n    const image = new Image();\n    image.src = dataURI;\n    return image;\n  }\n  return asset;\n}\n\nfunction isOffscreenCanvas(element: any): element is OffscreenCanvas {\n  return element && typeof element.convertToBlob === 'function';\n}\n\nfunction asExpoContext(gl: ExpoWebGLRenderingContext): WebGLRenderingContext {\n  gl.endFrameEXP = function glEndFrameEXP(): void {};\n\n  if (!gl['_expo_texImage2D']) {\n    gl['_expo_texImage2D'] = gl.texImage2D;\n    gl.texImage2D = (...props: any[]): any => {\n      const nextProps = [...props];\n      nextProps.push(getImageForAsset(nextProps.pop()));\n      return gl['_expo_texImage2D']!(...nextProps);\n    };\n  }\n\n  if (!gl['_expo_texSubImage2D']) {\n    gl['_expo_texSubImage2D'] = gl.texSubImage2D;\n    gl.texSubImage2D = (...props: any[]): any => {\n      const nextProps = [...props];\n      nextProps.push(getImageForAsset(nextProps.pop()));\n      return gl['_expo_texSubImage2D']!(...nextProps);\n    };\n  }\n\n  return gl;\n}\n\nfunction ensureContext(\n  canvas?: HTMLCanvasElement,\n  contextAttributes?: WebGLContextAttributes\n): WebGLRenderingContext {\n  if (!canvas) {\n    throw new CodedError(\n      'ERR_GL_INVALID',\n      'Attempting to use the GL context before it has been created.'\n    );\n  }\n\n  const context =\n    canvas.getContext('webgl2', contextAttributes) ||\n    canvas.getContext('webgl', contextAttributes) ||\n    canvas.getContext('webgl-experimental', contextAttributes) ||\n    canvas.getContext('experimental-webgl', contextAttributes);\n  invariant(context, 'Browser does not support WebGL');\n  return asExpoContext(context as ExpoWebGLRenderingContext);\n}\n\n// @needsAudit @docsMissing\nexport type GLViewWebProps = GLViewProps & {\n  onContextCreate: (gl: WebGLRenderingContext) => void;\n  onContextRestored?: (gl?: WebGLRenderingContext) => void;\n  onContextLost?: () => void;\n  webglContextAttributes?: WebGLContextAttributes;\n  // type overwrite\n  nativeRef_EXPERIMENTAL?(callback: ComponentOrHandle | HTMLCanvasElement | null): unknown;\n};\n\nasync function getBlobFromWebGLRenderingContext(\n  gl: WebGLRenderingContext,\n  options: SnapshotOptions = {}\n): Promise<{ width: number; height: number; blob: Blob | null }> {\n  invariant(gl, 'getBlobFromWebGLRenderingContext(): WebGL Rendering Context is not defined');\n\n  const { canvas } = gl;\n\n  let blob: Blob | null = null;\n\n  if (typeof (canvas as any).msToBlob === 'function') {\n    // @ts-ignore: polyfill: https://stackoverflow.com/a/29815058/4047926\n    blob = await canvas.msToBlob();\n  } else if (isOffscreenCanvas(canvas)) {\n    blob = await canvas.convertToBlob({ quality: options.compress, type: options.format });\n  } else {\n    blob = await new Promise((resolve) => {\n      canvas.toBlob((blob: Blob | null) => resolve(blob), options.format, options.compress);\n    });\n  }\n\n  return {\n    blob,\n    width: canvas.width,\n    height: canvas.height,\n  };\n}\n\nexport class GLView extends React.Component<GLViewWebProps> {\n  canvas?: HTMLCanvasElement;\n\n  gl?: WebGLRenderingContext;\n\n  static async createContextAsync(): Promise<WebGLRenderingContext | null> {\n    if (!Platform.isDOMAvailable) {\n      return null;\n    }\n    const canvas = document.createElement('canvas');\n    const { width, height, scale } = Dimensions.get('window');\n    canvas.width = width * scale;\n    canvas.height = height * scale;\n    return ensureContext(canvas);\n  }\n\n  static async destroyContextAsync(exgl?: WebGLRenderingContext | number): Promise<boolean> {\n    // Do nothing\n    return true;\n  }\n\n  static async takeSnapshotAsync(\n    gl: WebGLRenderingContext,\n    options: SnapshotOptions = {}\n  ): Promise<GLSnapshot> {\n    const { blob, width, height } = await getBlobFromWebGLRenderingContext(gl, options);\n\n    if (!blob) {\n      throw new CodedError('ERR_GL_SNAPSHOT', 'Failed to save the GL context');\n    }\n\n    return {\n      uri: blob,\n      localUri: '',\n      width,\n      height,\n    };\n  }\n\n  componentWillUnmount() {\n    if (this.gl) {\n      const loseContextExt = this.gl.getExtension('WEBGL_lose_context');\n      if (loseContextExt) {\n        loseContextExt.loseContext();\n      }\n      this.gl = undefined;\n    }\n    if (this.canvas) {\n      this.canvas.removeEventListener('webglcontextlost', this.onContextLost);\n      this.canvas.removeEventListener('webglcontextrestored', this.onContextRestored);\n    }\n  }\n\n  render() {\n    const {\n      onContextCreate,\n      onContextRestored,\n      onContextLost,\n      webglContextAttributes,\n      msaaSamples,\n      nativeRef_EXPERIMENTAL,\n      // @ts-ignore: ref does not exist\n      ref,\n      ...domProps\n    } = this.props;\n\n    return <Canvas {...domProps} canvasRef={this.setCanvasRef} />;\n  }\n\n  componentDidUpdate(prevProps: GLViewWebProps) {\n    const { webglContextAttributes } = this.props;\n    if (this.canvas && webglContextAttributes !== prevProps.webglContextAttributes) {\n      this.onContextLost(null);\n      this.onContextRestored();\n    }\n  }\n\n  private getGLContextOrReject(): WebGLRenderingContext {\n    const gl = this.getGLContext();\n    if (!gl) {\n      throw new CodedError(\n        'ERR_GL_INVALID',\n        'Attempting to use the GL context before it has been created.'\n      );\n    }\n    return gl;\n  }\n\n  private onContextLost = (event: Event | null): void => {\n    if (event && event.preventDefault) {\n      event.preventDefault();\n    }\n    this.gl = undefined;\n\n    if (typeof this.props.onContextLost === 'function') {\n      this.props.onContextLost();\n    }\n  };\n\n  private onContextRestored = (): void => {\n    this.gl = undefined;\n    if (this.getGLContext() == null) {\n      throw new CodedError('ERR_GL_INVALID', 'Failed to restore GL context.');\n    }\n  };\n\n  private getGLContext(): WebGLRenderingContext | null {\n    if (this.gl) return this.gl;\n\n    if (this.canvas) {\n      this.gl = ensureContext(this.canvas, this.props.webglContextAttributes);\n      if (typeof this.props.onContextCreate === 'function') {\n        this.props.onContextCreate(this.gl);\n      }\n      return this.gl;\n    }\n    return null;\n  }\n\n  private setCanvasRef = (canvas: HTMLCanvasElement): void => {\n    this.canvas = canvas;\n\n    if (typeof this.props.nativeRef_EXPERIMENTAL === 'function') {\n      this.props.nativeRef_EXPERIMENTAL(canvas);\n    }\n\n    if (this.canvas) {\n      this.canvas.addEventListener('webglcontextlost', this.onContextLost);\n      this.canvas.addEventListener('webglcontextrestored', this.onContextRestored);\n\n      this.getGLContext();\n    }\n  };\n\n  public async takeSnapshotAsync(options: SnapshotOptions = {}): Promise<GLSnapshot> {\n    if (!GLView.takeSnapshotAsync) {\n      throw new UnavailabilityError('expo-gl', 'takeSnapshotAsync');\n    }\n\n    const gl = this.getGLContextOrReject();\n    return await GLView.takeSnapshotAsync(gl, options);\n  }\n\n  public async startARSessionAsync(): Promise<void> {\n    throw new UnavailabilityError('GLView', 'startARSessionAsync');\n  }\n\n  public async createCameraTextureAsync(): Promise<void> {\n    throw new UnavailabilityError('GLView', 'createCameraTextureAsync');\n  }\n\n  public async destroyObjectAsync(glObject: WebGLObject): Promise<void> {\n    throw new UnavailabilityError('GLView', 'destroyObjectAsync');\n  }\n}\n"]}