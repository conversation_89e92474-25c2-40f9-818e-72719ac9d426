{"version": 3, "file": "Canvas.js", "sourceRoot": "", "sources": ["../src/Canvas.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACxC,OAAO,EAAqB,UAAU,EAAE,UAAU,EAAE,IAAI,EAAa,MAAM,cAAc,CAAC;AAC1F,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAYxE,SAAS,UAAU,CAAC,SAA8B;IAChD,IAAI,CAAC;QACH,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,MAAM,CAAI,OAAqB,EAAE,GAAa;IACrD,IAAI,CAAC,OAAO;QAAE,OAAO;IAErB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;SAAM,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;QAChC,aAAa;QACb,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;IACxB,CAAC;AACH,CAAC;AAED,SAAS,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAiB;IAC3C,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QACzD,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IACjC,CAAC;IACD,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACxC,MAAM,EAAE,WAAW,EAAE,KAAK,GAAG,CAAC,EAAE,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,OAAsB,CAAC;IACpF,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC3B,CAAC;AAED,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAC7B,CAAC,KAAwC,EAAE,GAAiC,EAAE,EAAE,CAC9E,aAAa,CAAC,QAAQ,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAC7C,CAAC;AAEF,MAAM,aAAa,GAIf,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE;IACnD,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAc,IAAI,CAAC,CAAC;IAE1D,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAO,IAAI,CAAC,CAAC;IACrC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAoB,IAAI,CAAC,CAAC;IAEzD,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QAC9C,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,IAAI,OAAO,iBAAiB,KAAK,WAAW,IAAI,MAAM,YAAY,iBAAiB,EAAE,CAAC;YACpF,MAAM,WAAW,GAAG,OAAO,CAAC;gBAC1B,IAAI;gBACJ,GAAG;aACJ,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;YAE/B,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW,CAAC,KAAK,IAAI,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC;YAEhD,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;YACzC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;QAC7C,CAAC;IACH,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAEhC,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAChC,CAAC,KAAwB,EAAE,EAAE;QAC3B,MAAM,EACJ,WAAW,EAAE,EACX,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAC1B,GACF,GAAG,KAAK,CAAC;QAEV,IAAI,KAAK,KAAK,IAAI,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,EAAE,MAAM,EAAE,CAAC;YACrD,OAAO,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAE3B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC,EACD,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5C,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;IACH,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAEV,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,gBAAgB,EAAE,CAAC;IACrB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,IAAI,MAAM,EAAE,CAAC;YACX,gBAAgB,EAAE,CAAC;QACrB,CAAC;QACD,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAC5E;MAAA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,aAAa,CAAC,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,EACtF;MAAA,CAAC,QAAQ,CACX;IAAA,EAAE,IAAI,CAAC,CACR,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,OAAO,EAAE;QACP,aAAa,EAAE,UAAU;KAC1B;CACF,CAAC,CAAC;AAEH,eAAe,aAAa,CAAC", "sourcesContent": ["import * as React from 'react';\nimport { findDOMNode } from 'react-dom';\nimport { LayoutChangeEvent, PixelRatio, StyleSheet, View, ViewProps } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\ninterface Size {\n  width: number;\n  height: number;\n}\n\ninterface GetSizeParams {\n  size: Size | null;\n  ref: React.RefObject<View | null>;\n}\n\nfunction getElement(component: React.ReactInstance): React.ReactInstance | Element | null | Text {\n  try {\n    return findDOMNode(component);\n  } catch {\n    return component;\n  }\n}\n\nfunction setRef<T>(refProp: React.Ref<T>, ref: T | null) {\n  if (!refProp) return;\n\n  if (typeof refProp === 'function') {\n    refProp(ref);\n  } else if ('current' in refProp) {\n    // @ts-ignore\n    refProp.current = ref;\n  }\n}\n\nfunction getSize({ size, ref }: GetSizeParams): Size {\n  if (size) {\n    return size;\n  } else if (!ref.current || typeof window === 'undefined') {\n    return { width: 0, height: 0 };\n  }\n  const element = getElement(ref.current);\n  const { offsetWidth: width = 0, offsetHeight: height = 0 } = element as HTMLElement;\n  return { width, height };\n}\n\nconst Canvas = React.forwardRef(\n  (props: React.ComponentProps<typeof View>, ref: React.Ref<HTMLCanvasElement>) =>\n    createElement('canvas', { ...props, ref })\n);\n\nconst CanvasWrapper: React.FunctionComponent<\n  ViewProps & {\n    canvasRef: React.Ref<HTMLCanvasElement>;\n  }\n> = ({ pointerEvents, children, style, ...props }) => {\n  const [size, setSize] = React.useState<Size | null>(null);\n\n  const ref = React.useRef<View>(null);\n  const _canvasRef = React.useRef<HTMLCanvasElement>(null);\n\n  const updateCanvasSize = React.useCallback(() => {\n    const canvas = _canvasRef.current;\n    if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement) {\n      const currentSize = getSize({\n        size,\n        ref,\n      });\n      const scale = PixelRatio.get();\n\n      canvas.style.width = `${currentSize.width}px`;\n      canvas.style.height = `${currentSize.height}px`;\n\n      canvas.width = currentSize.width * scale;\n      canvas.height = currentSize.height * scale;\n    }\n  }, [size?.width, size?.height]);\n\n  const onLayout = React.useCallback(\n    (event: LayoutChangeEvent) => {\n      const {\n        nativeEvent: {\n          layout: { width, height },\n        },\n      } = event;\n\n      if (width !== size?.width || height !== size?.height) {\n        setSize({ width, height });\n\n        if (props.onLayout) {\n          props.onLayout(event);\n        }\n      }\n    },\n    [size?.width, size?.height, props.onLayout]\n  );\n\n  React.useEffect(() => {\n    if (ref.current != null) {\n      setSize(getSize({ size, ref }));\n    }\n  }, [ref]);\n\n  React.useEffect(() => {\n    updateCanvasSize();\n  }, [size]);\n\n  React.useEffect(() => {\n    const canvas = _canvasRef.current;\n    if (canvas) {\n      updateCanvasSize();\n    }\n    setRef(props.canvasRef, canvas);\n  }, [_canvasRef]);\n\n  return (\n    <View {...props} style={[styles.wrapper, style]} ref={ref} onLayout={onLayout}>\n      <Canvas ref={_canvasRef} pointerEvents={pointerEvents} style={StyleSheet.absoluteFill} />\n      {children}\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  wrapper: {\n    pointerEvents: 'box-none',\n  },\n});\n\nexport default CanvasWrapper;\n"]}