{"version": 3, "file": "GLErrors.js", "sourceRoot": "", "sources": ["../src/GLErrors.ts"], "names": [], "mappings": "AAAA,MAAM,QAAQ,GAAG;IACf,IAAI,EAAE,oFAAoF;IAC1F,IAAI,EAAE,oDAAoD;IAC1D,IAAI,EAAE,gFAAgF;IACtF,IAAI,EAAE,kEAAkE;IACxE,IAAI,EAAE,6HAA6H;IACnI,KAAK,EAAE,gDAAgD;CACxD,CAAC;AAEF,eAAe,QAAQ,CAAC", "sourcesContent": ["const GLErrors = {\n  1280: 'INVALID ENUM: An unacceptable value has been specified for an enumerated argument.',\n  1281: 'INVALID_VALUE: A numeric argument is out of range.',\n  1282: 'INVALID_OPERATION: The specified command is not allowed for the current state.',\n  1285: 'OUT_OF_MEMORY: Not enough memory is left to execute the command.',\n  1286: 'INVALID_FRAMEBUFFER_OPERATION: The currently bound framebuffer is not complete when trying to render to or to read from it.',\n  37442: 'CONTEXT_LOST_WEBGL: The WebGL context is lost.',\n};\n\nexport default GLErrors;\n"]}