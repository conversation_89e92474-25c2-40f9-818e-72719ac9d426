{"version": 3, "file": "GLView.types.js", "sourceRoot": "", "sources": ["../src/GLView.types.ts"], "names": [], "mappings": "AA4GA,cAAc;AACd,MAAM,CAAN,IAAY,eA2BX;AA3BD,WAAY,eAAe;IACzB;;OAEG;IACH,6DAAY,CAAA;IACZ;;OAEG;IACH,qEAAgB,CAAA;IAChB;;;OAGG;IACH,iEAAc,CAAA;IACd;;OAEG;IACH,+EAAqB,CAAA;IACrB;;;OAGG;IACH,6EAAoB,CAAA;IACpB;;OAEG;IACH,oDAAsE,CAAA;AACxE,CAAC,EA3BW,eAAe,KAAf,eAAe,QA2B1B", "sourcesContent": ["import { Component, ComponentClass } from 'react';\nimport { ViewProps } from 'react-native';\n\n// @docsMissing\nexport type SurfaceCreateEvent = {\n  nativeEvent: {\n    exglCtxId: number;\n  };\n};\n\n// @needsAudit\nexport type SnapshotOptions = {\n  /**\n   * Whether to flip the snapshot vertically.\n   * @default false\n   */\n  flip?: boolean;\n  /**\n   * Specify the framebuffer that we will be reading from.\n   * Defaults to underlying framebuffer that is presented in the view or the current framebuffer if context is headless.\n   */\n  framebuffer?: WebGLFramebuffer;\n  /**\n   * Rect to crop the snapshot. It's passed directly to `glReadPixels`.\n   */\n  rect?: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n  };\n  /**\n   * Specifies what type of compression should be used and what is the result file extension.\n   * PNG compression is lossless but slower, JPEG is faster but the image has visible artifacts.\n   * > **Note:** When using WebP format, the iOS version will print a warning, and generate a `'png'` file instead.\n   * > It is recommended to use [platform-specific](https://reactnative.dev/docs/platform-specific-code) code in this case.\n   * @default 'jpeg'\n   */\n  format?: 'jpeg' | 'png' | 'webp';\n  /**\n   * A value in range `0` to `1.0` specifying compression level of the result image.\n   * `1.0` means no compression and `0` the highest compression.\n   * @default 1.0\n   */\n  compress?: number;\n};\n\n// @needsAudit\nexport type GLSnapshot = {\n  /**\n   * URI to the snapshot.\n   */\n  uri: string | Blob | null;\n  /**\n   * Synonym for `uri`. Makes snapshot object compatible with `texImage2D`.\n   */\n  localUri: string;\n  /**\n   * Width of the snapshot.\n   */\n  width: number;\n  /**\n   * Height of the snapshot.\n   */\n  height: number;\n};\n\n// @docsMissing\nexport interface ExpoWebGLRenderingContext extends WebGL2RenderingContext {\n  contextId: number;\n  endFrameEXP(): void;\n  flushEXP(): void;\n  __expoSetLogging(option: GLLoggingOption): void;\n\n  /** @internal */\n  _expo_texSubImage2D?(...props: any[]): void;\n  _expo_texImage2D?(...props: any[]): void;\n}\n\n// @docsMissing\nexport type ComponentOrHandle = null | number | Component<any, any> | ComponentClass<any>;\n\n// @needsAudit\nexport type GLViewProps = {\n  /**\n   * A function that will be called when the OpenGL ES context is created.\n   * The function is passed a single argument `gl` that extends a [WebGLRenderingContext](https://www.khronos.org/registry/webgl/specs/latest/1.0/#5.14) interface.\n   */\n  onContextCreate(gl: ExpoWebGLRenderingContext): void;\n  /**\n   * `GLView` can enable iOS's built-in [multisampling](https://www.khronos.org/registry/OpenGL/extensions/APPLE/APPLE_framebuffer_multisample.txt).\n   * This prop specifies the number of samples to use. Setting this to `0` turns off multisampling.\n   * @platform ios\n   * @default 4\n   */\n  msaaSamples: number;\n  /**\n   * Enables support for interacting with a `gl` object from code running on the Reanimated worklet thread.\n   * @default false\n   */\n  enableExperimentalWorkletSupport: boolean;\n  /**\n   * @hidden\n   * A ref callback for the native GLView\n   */\n  nativeRef_EXPERIMENTAL?(callback: ComponentOrHandle | null): void;\n} & ViewProps;\n\n// @needsAudit\nexport enum GLLoggingOption {\n  /**\n   * Disables logging entirely.\n   */\n  DISABLED = 0,\n  /**\n   * Logs method calls, their parameters and results.\n   */\n  METHOD_CALLS = 1,\n  /**\n   * Calls `gl.getError()` after each other method call and prints an error if any is returned.\n   * This option has a significant impact on the performance as this method is blocking.\n   */\n  GET_ERRORS = 2,\n  /**\n   * Resolves parameters of type `number` to their constant names.\n   */\n  RESOLVE_CONSTANTS = 4,\n  /**\n   * When this option is enabled, long strings will be truncated.\n   * It's useful if your shaders are really big and logging them significantly reduces performance.\n   */\n  TRUNCATE_STRINGS = 8,\n  /**\n   * Enables all other options. It implies `GET_ERRORS` so be aware of the slowdown.\n   */\n  ALL = METHOD_CALLS | GET_ERRORS | RESOLVE_CONSTANTS | TRUNCATE_STRINGS,\n}\n"]}