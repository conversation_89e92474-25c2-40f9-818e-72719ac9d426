{"name": "expo-gl", "version": "15.1.7", "description": "Provides GLView that acts as OpenGL ES render target and gives GL context object implementing WebGL 2.0 specification.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:snapshots": "jest --updateSnapshot", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "gl", "glview", "webgl"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-gl"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://docs.expo.dev/versions/latest/sdk/gl-view/", "author": "650 Industries, Inc.", "license": "MIT", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"@types/invariant": "^2.2.33", "@types/offscreencanvas": "2019.6.4", "@types/webgl2": "^0.0.6", "expo-module-scripts": "^4.1.8", "react-test-renderer": "19.0.0"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}