GL_CONSTANT(ACTIVE_ATTRIBUTES) /* 35721 */
// GL_CONSTANT(ACTIVE_ATTRIBUTE_MAX_LENGTH) /*35722 */
GL_CONSTANT(ACTIVE_TEXTURE) /* 34016 */
GL_CONSTANT(ACTIVE_UNIFORMS) /* 35718 */
GL_CONSTANT(ACTIVE_UNIFORM_BLOCKS) /* 35382 */
// GL_CONSTANT(ACTIVE_UNIFORM_MAX_LENGTH) /*35719 */
GL_CONSTANT(ALIASED_LINE_WIDTH_RANGE) /* 33902 */
GL_CONSTANT(ALIASED_POINT_SIZE_RANGE) /* 33901 */
GL_CONSTANT(ALPHA) /* 6406 */
GL_CONSTANT(ALPHA_BITS) /* 3413 */
GL_CONSTANT(ALREADY_SIGNALED) /* 37146 */
GL_CONSTANT(ALWAYS) /* 519 */
GL_CONSTANT(ANY_SAMPLES_PASSED) /* 35887 */
GL_CONSTANT(ANY_SAMPLES_PASSED_CONSERVATIVE) /* 36202 */
GL_CONSTANT(ARRAY_BUFFER) /* 34962 */
GL_CONSTANT(ARRAY_BUFFER_BINDING) /* 34964 */
GL_CONSTANT(ATTACHED_SHADERS) /* 35717 */
GL_CONSTANT(BACK) /* 1029 */
GL_CONSTANT(BLEND) /* 3042 */
GL_CONSTANT(BLEND_COLOR) /* 32773 */
GL_CONSTANT(BLEND_DST_ALPHA) /* 32970 */
GL_CONSTANT(BLEND_DST_RGB) /* 32968 */
GL_CONSTANT(BLEND_EQUATION) /* 32777 */
GL_CONSTANT(BLEND_EQUATION_ALPHA) /* 34877 */
GL_CONSTANT(BLEND_EQUATION_RGB) /* 32777 */
GL_CONSTANT(BLEND_SRC_ALPHA) /* 32971 */
GL_CONSTANT(BLEND_SRC_RGB) /* 32969 */
GL_CONSTANT(BLUE_BITS) /* 3412 */
GL_CONSTANT(BOOL) /* 35670 */
GL_CONSTANT(BOOL_VEC2) /* 35671 */
GL_CONSTANT(BOOL_VEC3) /* 35672 */
GL_CONSTANT(BOOL_VEC4) /* 35673 */
GL_CONSTANT(BROWSER_DEFAULT_WEBGL) /* 37444 */
GL_CONSTANT(BUFFER_SIZE) /* 34660 */
GL_CONSTANT(BUFFER_USAGE) /* 34661 */
GL_CONSTANT(BYTE) /* 5120 */
GL_CONSTANT(CCW) /* 2305 */
GL_CONSTANT(CLAMP_TO_EDGE) /* 33071 */
GL_CONSTANT(COLOR) /* 6144 */
GL_CONSTANT(COLOR_ATTACHMENT0) /* 36064 */
GL_CONSTANT(COLOR_ATTACHMENT1) /* 36065 */
GL_CONSTANT(COLOR_ATTACHMENT2) /* 36066 */
GL_CONSTANT(COLOR_ATTACHMENT3) /* 36067 */
GL_CONSTANT(COLOR_ATTACHMENT4) /* 36068 */
GL_CONSTANT(COLOR_ATTACHMENT5) /* 36069 */
GL_CONSTANT(COLOR_ATTACHMENT6) /* 36070 */
GL_CONSTANT(COLOR_ATTACHMENT7) /* 36071 */
GL_CONSTANT(COLOR_ATTACHMENT8) /* 36072 */
GL_CONSTANT(COLOR_ATTACHMENT9) /* 36073 */
GL_CONSTANT(COLOR_ATTACHMENT10) /* 36074 */
GL_CONSTANT(COLOR_ATTACHMENT11) /* 36075 */
GL_CONSTANT(COLOR_ATTACHMENT12) /* 36076 */
GL_CONSTANT(COLOR_ATTACHMENT13) /* 36077 */
GL_CONSTANT(COLOR_ATTACHMENT14) /* 36078 */
GL_CONSTANT(COLOR_ATTACHMENT15) /* 36079 */
GL_CONSTANT(COLOR_BUFFER_BIT) /* 16384 */
GL_CONSTANT(COLOR_CLEAR_VALUE) /* 3106 */
GL_CONSTANT(COLOR_WRITEMASK) /* 3107 */
GL_CONSTANT(COMPARE_REF_TO_TEXTURE) /* 34894 */
GL_CONSTANT(COMPILE_STATUS) /* 35713 */
GL_CONSTANT(COMPRESSED_TEXTURE_FORMATS) /* 34467 */
GL_CONSTANT(CONDITION_SATISFIED) /* 37148 */
GL_CONSTANT(CONSTANT_ALPHA) /* 32771 */
GL_CONSTANT(CONSTANT_COLOR) /* 32769 */
GL_CONSTANT(CONTEXT_LOST_WEBGL) /* 37442 */
GL_CONSTANT(COPY_READ_BUFFER) /* 36662 */
GL_CONSTANT(COPY_READ_BUFFER_BINDING) /* 36662 */
GL_CONSTANT(COPY_WRITE_BUFFER) /* 36663 */
GL_CONSTANT(COPY_WRITE_BUFFER_BINDING) /* 36663 */
GL_CONSTANT(CULL_FACE) /* 2884 */
GL_CONSTANT(CULL_FACE_MODE) /* 2885 */
GL_CONSTANT(CURRENT_PROGRAM) /* 35725 */
GL_CONSTANT(CURRENT_QUERY) /* 34917 */
GL_CONSTANT(CURRENT_VERTEX_ATTRIB) /* 34342 */
GL_CONSTANT(CW) /* 2304 */
GL_CONSTANT(DECR) /* 7683 */
GL_CONSTANT(DECR_WRAP) /* 34056 */
GL_CONSTANT(DELETE_STATUS) /* 35712 */
GL_CONSTANT(DEPTH) /* 6145 */
GL_CONSTANT(DEPTH24_STENCIL8) /* 35056 */
GL_CONSTANT(DEPTH32F_STENCIL8) /* 36013 */
GL_CONSTANT(DEPTH_ATTACHMENT) /* 36096 */
GL_CONSTANT(DEPTH_BITS) /* 3414 */
GL_CONSTANT(DEPTH_BUFFER_BIT) /* 256 */
GL_CONSTANT(DEPTH_COMPONENT24) /* 33190 */
GL_CONSTANT(DEPTH_COMPONENT32F) /* 36012 */
GL_CONSTANT(DEPTH_CLEAR_VALUE) /* 2931 */
GL_CONSTANT(DEPTH_COMPONENT) /* 6402 */
GL_CONSTANT(DEPTH_COMPONENT16) /* 33189 */
GL_CONSTANT(DEPTH_FUNC) /* 2932 */
GL_CONSTANT(DEPTH_RANGE) /* 2928 */
GL_CONSTANT(DEPTH_STENCIL) /* 34041 */
GL_CONSTANT(DEPTH_STENCIL_ATTACHMENT) /* 33306 */
GL_CONSTANT(DEPTH_TEST) /* 2929 */
GL_CONSTANT(DEPTH_WRITEMASK) /* 2930 */
GL_CONSTANT(DITHER) /* 3024 */
GL_CONSTANT(DONT_CARE) /* 4352 */
GL_CONSTANT(DRAW_BUFFER0) /* 34853 */
GL_CONSTANT(DRAW_BUFFER1) /* 34854 */
GL_CONSTANT(DRAW_BUFFER2) /* 34855 */
GL_CONSTANT(DRAW_BUFFER3) /* 34856 */
GL_CONSTANT(DRAW_BUFFER4) /* 34857 */
GL_CONSTANT(DRAW_BUFFER5) /* 34858 */
GL_CONSTANT(DRAW_BUFFER6) /* 34859 */
GL_CONSTANT(DRAW_BUFFER7) /* 34860 */
GL_CONSTANT(DRAW_BUFFER8) /* 34861 */
GL_CONSTANT(DRAW_BUFFER9) /* 34862 */
GL_CONSTANT(DRAW_BUFFER10) /* 34863 */
GL_CONSTANT(DRAW_BUFFER11) /* 34864 */
GL_CONSTANT(DRAW_BUFFER12) /* 34865 */
GL_CONSTANT(DRAW_BUFFER13) /* 34866 */
GL_CONSTANT(DRAW_BUFFER14) /* 34867 */
GL_CONSTANT(DRAW_BUFFER15) /* 34868 */
GL_CONSTANT(DRAW_FRAMEBUFFER) /* 36009 */
GL_CONSTANT(DRAW_FRAMEBUFFER_BINDING) /* 36006 */
GL_CONSTANT(DST_ALPHA) /* 772 */
GL_CONSTANT(DST_COLOR) /* 774 */
GL_CONSTANT(DYNAMIC_COPY) /* 35050 */
GL_CONSTANT(DYNAMIC_DRAW) /* 35048 */
GL_CONSTANT(DYNAMIC_READ) /* 35049 */
GL_CONSTANT(ELEMENT_ARRAY_BUFFER) /* 34963 */
GL_CONSTANT(ELEMENT_ARRAY_BUFFER_BINDING) /* 34965 */
GL_CONSTANT(EQUAL) /* 514 */
// GL_CONSTANT(FALSE) /*0 */
GL_CONSTANT(FASTEST) /* 4353 */
GL_CONSTANT(FLOAT) /* 5126 */
GL_CONSTANT(FLOAT_32_UNSIGNED_INT_24_8_REV) /* 36269 */
GL_CONSTANT(FLOAT_MAT2) /* 35674 */
GL_CONSTANT(FLOAT_MAT2x3) /* 35685 */
GL_CONSTANT(FLOAT_MAT2x4) /* 35686 */
GL_CONSTANT(FLOAT_MAT3) /* 35675 */
GL_CONSTANT(FLOAT_MAT3x2) /* 35687 */
GL_CONSTANT(FLOAT_MAT3x4) /* 35688 */
GL_CONSTANT(FLOAT_MAT4) /* 35676 */
GL_CONSTANT(FLOAT_MAT4x2) /* 35689 */
GL_CONSTANT(FLOAT_MAT4x3) /* 35690 */
GL_CONSTANT(FLOAT_VEC2) /* 35664 */
GL_CONSTANT(FLOAT_VEC3) /* 35665 */
GL_CONSTANT(FLOAT_VEC4) /* 35666 */
GL_CONSTANT(FRAGMENT_SHADER) /* 35632 */
GL_CONSTANT(FRAGMENT_SHADER_DERIVATIVE_HINT) /* 35723 */
GL_CONSTANT(FRAMEBUFFER) /* 36160 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE) /* 33301 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_BLUE_SIZE) /* 33300 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING) /* 33296 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE) /* 33297 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE) /* 33302 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_GREEN_SIZE) /* 33299 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_OBJECT_NAME) /* 36049 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE) /* 36048 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_RED_SIZE) /* 33298 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE) /* 33303 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE) /* 36051 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER) /* 36052 */
GL_CONSTANT(FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL) /* 36050 */
GL_CONSTANT(FRAMEBUFFER_BINDING) /* 36006 */
GL_CONSTANT(FRAMEBUFFER_COMPLETE) /* 36053 */
GL_CONSTANT(FRAMEBUFFER_DEFAULT) /* 33304 */
GL_CONSTANT(FRAMEBUFFER_INCOMPLETE_ATTACHMENT) /* 36054 */
GL_CONSTANT(FRAMEBUFFER_INCOMPLETE_DIMENSIONS) /* 36057 */
GL_CONSTANT(FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT) /* 36055 */
GL_CONSTANT(FRAMEBUFFER_INCOMPLETE_MULTISAMPLE) /* 36182 */
GL_CONSTANT(FRAMEBUFFER_UNSUPPORTED) /* 36061 */
GL_CONSTANT(FRONT) /* 1028 */
GL_CONSTANT(FRONT_AND_BACK) /* 1032 */
GL_CONSTANT(FRONT_FACE) /* 2886 */
GL_CONSTANT(FUNC_ADD) /* 32774 */
GL_CONSTANT(FUNC_REVERSE_SUBTRACT) /* 32779 */
GL_CONSTANT(FUNC_SUBTRACT) /* 32778 */
GL_CONSTANT(GENERATE_MIPMAP_HINT) /* 33170 */
GL_CONSTANT(GEQUAL) /* 518 */
GL_CONSTANT(GREATER) /* 516 */
GL_CONSTANT(GREEN_BITS) /* 3411 */
GL_CONSTANT(HALF_FLOAT) /* 5131 */
GL_CONSTANT(HIGH_FLOAT) /* 36338 */
GL_CONSTANT(HIGH_INT) /* 36341 */
GL_CONSTANT(IMPLEMENTATION_COLOR_READ_TYPE) /* 35738 */
GL_CONSTANT(IMPLEMENTATION_COLOR_READ_FORMAT) /* 35739 */
GL_CONSTANT(INCR) /* 7682 */
GL_CONSTANT(INCR_WRAP) /* 34055 */
// GL_CONSTANT(INFO_LOG_LENGTH) /*35716 */
GL_CONSTANT(INT) /* 5124 */
GL_CONSTANT(INTERLEAVED_ATTRIBS) /* 35980 */
GL_CONSTANT(INT_2_10_10_10_REV) /* 36255 */
GL_CONSTANT(INT_SAMPLER_2D) /* 36298 */
GL_CONSTANT(INT_SAMPLER_3D) /* 36299 */
GL_CONSTANT(INT_SAMPLER_CUBE) /* 36300 */
GL_CONSTANT(INT_SAMPLER_2D_ARRAY) /* 36303 */
GL_CONSTANT(INT_VEC2) /* 35667 */
GL_CONSTANT(INT_VEC3) /* 35668 */
GL_CONSTANT(INT_VEC4) /* 35669 */
GL_CONSTANT(INVALID_ENUM) /* 1280 */
GL_CONSTANT(INVALID_FRAMEBUFFER_OPERATION) /* 1286 */
GL_CONSTANT(INVALID_INDEX) /* 4294967295 */
GL_CONSTANT(INVALID_OPERATION) /* 1282 */
GL_CONSTANT(INVALID_VALUE) /* 1281 */
GL_CONSTANT(INVERT) /* 5386 */
GL_CONSTANT(KEEP) /* 7680 */
GL_CONSTANT(LEQUAL) /* 515 */
GL_CONSTANT(LESS) /* 513 */
GL_CONSTANT(LINEAR) /* 9729 */
GL_CONSTANT(LINEAR_MIPMAP_LINEAR) /* 9987 */
GL_CONSTANT(LINEAR_MIPMAP_NEAREST) /* 9985 */
GL_CONSTANT(LINES) /* 1 */
GL_CONSTANT(LINE_LOOP) /* 2 */
GL_CONSTANT(LINE_STRIP) /* 3 */
GL_CONSTANT(LINE_WIDTH) /* 2849 */
GL_CONSTANT(LINK_STATUS) /* 35714 */
GL_CONSTANT(LOW_FLOAT) /* 36336 */
GL_CONSTANT(LOW_INT) /* 36339 */
GL_CONSTANT(LUMINANCE) /* 6409 */
GL_CONSTANT(LUMINANCE_ALPHA) /* 6410 */
GL_CONSTANT(MAX) /* 32776 */
GL_CONSTANT(MAX_3D_TEXTURE_SIZE) /* 32883 */
GL_CONSTANT(MAX_ARRAY_TEXTURE_LAYERS) /* 35071 */
GL_CONSTANT(MAX_CLIENT_WAIT_TIMEOUT_WEBGL) /* 37447 */
GL_CONSTANT(MAX_COLOR_ATTACHMENTS) /* 36063 */
GL_CONSTANT(MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS) /* 35379 */
GL_CONSTANT(MAX_COMBINED_TEXTURE_IMAGE_UNITS) /* 35661 */
GL_CONSTANT(MAX_COMBINED_UNIFORM_BLOCKS) /* 35374 */
GL_CONSTANT(MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS) /* 35377 */
GL_CONSTANT(MAX_CUBE_MAP_TEXTURE_SIZE) /* 34076 */
GL_CONSTANT(MAX_DRAW_BUFFERS) /* 34852 */
GL_CONSTANT(MAX_ELEMENTS_INDICES) /* 33001 */
GL_CONSTANT(MAX_ELEMENTS_VERTICES) /* 33000 */
GL_CONSTANT(MAX_ELEMENT_INDEX) /* 36203 */
GL_CONSTANT(MAX_FRAGMENT_INPUT_COMPONENTS) /* 37157 */
GL_CONSTANT(MAX_FRAGMENT_UNIFORM_BLOCKS) /* 35373 */
GL_CONSTANT(MAX_FRAGMENT_UNIFORM_COMPONENTS) /* 35657 */
GL_CONSTANT(MAX_FRAGMENT_UNIFORM_VECTORS) /* 36349 */
GL_CONSTANT(MAX_PROGRAM_TEXEL_OFFSET) /* 35077 */
GL_CONSTANT(MAX_RENDERBUFFER_SIZE) /* 34024 */
GL_CONSTANT(MAX_SAMPLES) /* 36183 */
GL_CONSTANT(MAX_SERVER_WAIT_TIMEOUT) /* 37137 */
GL_CONSTANT(MAX_TEXTURE_IMAGE_UNITS) /* 34930 */
GL_CONSTANT(MAX_TEXTURE_LOD_BIAS) /* 34045 */
GL_CONSTANT(MAX_TEXTURE_SIZE) /* 3379 */
GL_CONSTANT(MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS) /* 35978 */
GL_CONSTANT(MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS) /* 35979 */
GL_CONSTANT(MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS) /* 35968 */
GL_CONSTANT(MAX_UNIFORM_BLOCK_SIZE) /* 35376 */
GL_CONSTANT(MAX_UNIFORM_BUFFER_BINDINGS) /* 35375 */
GL_CONSTANT(MAX_VARYING_COMPONENTS) /* 35659 */
GL_CONSTANT(MAX_VARYING_VECTORS) /* 36348 */
GL_CONSTANT(MAX_VERTEX_ATTRIBS) /* 34921 */
GL_CONSTANT(MAX_VERTEX_OUTPUT_COMPONENTS) /* 37154 */
GL_CONSTANT(MAX_VERTEX_TEXTURE_IMAGE_UNITS) /* 35660 */
GL_CONSTANT(MAX_VERTEX_UNIFORM_BLOCKS) /* 35371 */
GL_CONSTANT(MAX_VERTEX_UNIFORM_COMPONENTS) /* 35658 */
GL_CONSTANT(MAX_VERTEX_UNIFORM_VECTORS) /* 36347 */
GL_CONSTANT(MAX_VIEWPORT_DIMS) /* 3386 */
GL_CONSTANT(MEDIUM_FLOAT) /* 36337 */
GL_CONSTANT(MEDIUM_INT) /* 36340 */
GL_CONSTANT(MIN) /* 32775 */
GL_CONSTANT(MIN_PROGRAM_TEXEL_OFFSET) /* 35076 */
GL_CONSTANT(MIRRORED_REPEAT) /* 33648 */
GL_CONSTANT(NEAREST) /* 9728 */
GL_CONSTANT(NEAREST_MIPMAP_LINEAR) /* 9986 */
GL_CONSTANT(NEAREST_MIPMAP_NEAREST) /* 9984 */
GL_CONSTANT(NEVER) /* 512 */
GL_CONSTANT(NICEST) /* 4354 */
GL_CONSTANT(NONE) /* 0 */
GL_CONSTANT(NOTEQUAL) /* 517 */
GL_CONSTANT(NO_ERROR) /* 0 */
// GL_CONSTANT(NUM_COMPRESSED_TEXTURE_FORMATS) /*34466 */
GL_CONSTANT(OBJECT_TYPE) /* 37138 */
GL_CONSTANT(ONE) /* 1 */
GL_CONSTANT(ONE_MINUS_CONSTANT_ALPHA) /* 32772 */
GL_CONSTANT(ONE_MINUS_CONSTANT_COLOR) /* 32770 */
GL_CONSTANT(ONE_MINUS_DST_ALPHA) /* 773 */
GL_CONSTANT(ONE_MINUS_DST_COLOR) /* 775 */
GL_CONSTANT(ONE_MINUS_SRC_ALPHA) /* 771 */
GL_CONSTANT(ONE_MINUS_SRC_COLOR) /* 769 */
GL_CONSTANT(OUT_OF_MEMORY) /* 1285 */
GL_CONSTANT(PACK_ALIGNMENT) /* 3333 */
GL_CONSTANT(PACK_ROW_LENGTH) /* 3330 */
GL_CONSTANT(PACK_SKIP_PIXELS) /* 3332 */
GL_CONSTANT(PACK_SKIP_ROWS) /* 3331 */
GL_CONSTANT(PIXEL_PACK_BUFFER) /* 35051 */
GL_CONSTANT(PIXEL_PACK_BUFFER_BINDING) /* 35053 */
GL_CONSTANT(PIXEL_UNPACK_BUFFER) /* 35052 */
GL_CONSTANT(PIXEL_UNPACK_BUFFER_BINDING) /* 35055 */
GL_CONSTANT(POINTS) /* 0 */
GL_CONSTANT(POLYGON_OFFSET_FACTOR) /* 32824 */
GL_CONSTANT(POLYGON_OFFSET_FILL) /* 32823 */
GL_CONSTANT(POLYGON_OFFSET_UNITS) /* 10752 */
GL_CONSTANT(QUERY_RESULT) /* 34918 */
GL_CONSTANT(QUERY_RESULT_AVAILABLE) /* 34919 */
GL_CONSTANT(R11F_G11F_B10F) /* 35898 */
GL_CONSTANT(R16F) /* 33325 */
GL_CONSTANT(R16I) /* 33331 */
GL_CONSTANT(R16UI) /* 33332 */
GL_CONSTANT(R32F) /* 33326 */
GL_CONSTANT(R32I) /* 33333 */
GL_CONSTANT(R32UI) /* 33334 */
GL_CONSTANT(R8) /* 33321 */
GL_CONSTANT(R8I) /* 33329 */
GL_CONSTANT(R8UI) /* 33330 */
GL_CONSTANT(R8_SNORM) /* 36756 */
GL_CONSTANT(RASTERIZER_DISCARD) /* 35977 */
GL_CONSTANT(READ_BUFFER) /* 3074 */
GL_CONSTANT(READ_FRAMEBUFFER) /* 36008 */
GL_CONSTANT(READ_FRAMEBUFFER_BINDING) /* 36010 */
GL_CONSTANT(RED) /* 6403 */
GL_CONSTANT(RED_BITS) /* 3410 */
GL_CONSTANT(RED_INTEGER) /* 36244 */
GL_CONSTANT(RENDERBUFFER) /* 36161 */
GL_CONSTANT(RENDERBUFFER_ALPHA_SIZE) /* 36179 */
GL_CONSTANT(RENDERBUFFER_BINDING) /* 36007 */
GL_CONSTANT(RENDERBUFFER_BLUE_SIZE) /* 36178 */
GL_CONSTANT(RENDERBUFFER_DEPTH_SIZE) /* 36180 */
GL_CONSTANT(RENDERBUFFER_GREEN_SIZE) /* 36177 */
GL_CONSTANT(RENDERBUFFER_HEIGHT) /* 36163 */
GL_CONSTANT(RENDERBUFFER_INTERNAL_FORMAT) /* 36164 */
GL_CONSTANT(RENDERBUFFER_RED_SIZE) /* 36176 */
GL_CONSTANT(RENDERBUFFER_SAMPLES) /* 36011 */
GL_CONSTANT(RENDERBUFFER_STENCIL_SIZE) /* 36181 */
GL_CONSTANT(RENDERBUFFER_WIDTH) /* 36162 */
GL_CONSTANT(RENDERER) /* 7937 */
GL_CONSTANT(REPEAT) /* 10497 */
GL_CONSTANT(REPLACE) /* 7681 */
GL_CONSTANT(RG) /* 33319 */
GL_CONSTANT(RG16F) /* 33327 */
GL_CONSTANT(RG16I) /* 33337 */
GL_CONSTANT(RG16UI) /* 33338 */
GL_CONSTANT(RG32F) /* 33328 */
GL_CONSTANT(RG32I) /* 33339 */
GL_CONSTANT(RG32UI) /* 33340 */
GL_CONSTANT(RG8) /* 33323 */
GL_CONSTANT(RG8I) /* 33335 */
GL_CONSTANT(RG8UI) /* 33336 */
GL_CONSTANT(RG8_SNORM) /* 36757 */
GL_CONSTANT(RGB) /* 6407 */
GL_CONSTANT(RGB10_A2) /* 32857 */
GL_CONSTANT(RGB10_A2UI) /* 36975 */
GL_CONSTANT(RGB16F) /* 34843 */
GL_CONSTANT(RGB16I) /* 36233 */
GL_CONSTANT(RGB16UI) /* 36215 */
GL_CONSTANT(RGB32F) /* 34837 */
GL_CONSTANT(RGB32I) /* 36227 */
GL_CONSTANT(RGB32UI) /* 36209 */
GL_CONSTANT(RGB5_A1) /* 32855 */
GL_CONSTANT(RGB565) /* 36194 */
GL_CONSTANT(RGB8) /* 32849 */
GL_CONSTANT(RGB8I) /* 36239 */
GL_CONSTANT(RGB8UI) /* 36221 */
GL_CONSTANT(RGB8_SNORM) /* 36758 */
GL_CONSTANT(RGB9_E5) /* 35901 */
GL_CONSTANT(RGBA) /* 6408 */
GL_CONSTANT(RGBA4) /* 32854 */
GL_CONSTANT(RGBA8) /* 32856 */
GL_CONSTANT(RGBA8I) /* 36238 */
GL_CONSTANT(RGBA8UI) /* 36220 */
GL_CONSTANT(RGBA8_SNORM) /* 36759 */
GL_CONSTANT(RGBA16F) /* 34842 */
GL_CONSTANT(RGBA16I) /* 36232 */
GL_CONSTANT(RGBA16UI) /* 36214 */
GL_CONSTANT(RGBA32F) /* 34836 */
GL_CONSTANT(RGBA32I) /* 36226 */
GL_CONSTANT(RGBA32UI) /* 36208 */
GL_CONSTANT(RGB_INTEGER) /* 36248 */
GL_CONSTANT(RGBA_INTEGER) /* 36249 */
GL_CONSTANT(RG_INTEGER) /* 33320 */
GL_CONSTANT(SAMPLER_2D) /* 35678 */
GL_CONSTANT(SAMPLER_2D_ARRAY) /* 36289 */
GL_CONSTANT(SAMPLER_2D_ARRAY_SHADOW) /* 36292 */
GL_CONSTANT(SAMPLER_2D_SHADOW) /* 35682 */
GL_CONSTANT(SAMPLER_3D) /* 35679 */
GL_CONSTANT(SAMPLER_BINDING) /* 35097 */
GL_CONSTANT(SAMPLER_CUBE) /* 35680 */
GL_CONSTANT(SAMPLER_CUBE_SHADOW) /* 36293 */
GL_CONSTANT(SAMPLES) /* 32937 */
GL_CONSTANT(SAMPLE_ALPHA_TO_COVERAGE) /* 32926 */
GL_CONSTANT(SAMPLE_BUFFERS) /* 32936 */
GL_CONSTANT(SAMPLE_COVERAGE) /* 32928 */
GL_CONSTANT(SAMPLE_COVERAGE_INVERT) /* 32939 */
GL_CONSTANT(SAMPLE_COVERAGE_VALUE) /* 32938 */
GL_CONSTANT(SCISSOR_BOX) /* 3088 */
GL_CONSTANT(SCISSOR_TEST) /* 3089 */
GL_CONSTANT(SEPARATE_ATTRIBS) /* 35981 */
// GL_CONSTANT(SHADER_COMPILER) /*36346 */
// GL_CONSTANT(SHADER_SOURCE_LENGTH) /*35720 */
GL_CONSTANT(SHADER_TYPE) /* 35663 */
GL_CONSTANT(SHADING_LANGUAGE_VERSION) /* 35724 */
GL_CONSTANT(SHORT) /* 5122 */
GL_CONSTANT(SIGNALED) /* 37145 */
GL_CONSTANT(SIGNED_NORMALIZED) /* 36764 */
GL_CONSTANT(SRC_ALPHA) /* 770 */
GL_CONSTANT(SRC_ALPHA_SATURATE) /* 776 */
GL_CONSTANT(SRC_COLOR) /* 768 */
GL_CONSTANT(SRGB) /* 35904 */
GL_CONSTANT(SRGB8) /* 35905 */
GL_CONSTANT(SRGB8_ALPHA8) /* 35907 */
GL_CONSTANT(STATIC_COPY) /* 35046 */
GL_CONSTANT(STATIC_DRAW) /* 35044 */
GL_CONSTANT(STATIC_READ) /* 35045 */
GL_CONSTANT(STENCIL) /* 6146 */
GL_CONSTANT(STENCIL_ATTACHMENT) /* 36128 */
GL_CONSTANT(STENCIL_BACK_FAIL) /* 34817 */
GL_CONSTANT(STENCIL_BACK_FUNC) /* 34816 */
GL_CONSTANT(STENCIL_BACK_PASS_DEPTH_FAIL) /* 34818 */
GL_CONSTANT(STENCIL_BACK_PASS_DEPTH_PASS) /* 34819 */
GL_CONSTANT(STENCIL_BACK_REF) /* 36003 */
GL_CONSTANT(STENCIL_BACK_VALUE_MASK) /* 36004 */
GL_CONSTANT(STENCIL_BACK_WRITEMASK) /* 36005 */
GL_CONSTANT(STENCIL_BITS) /* 3415 */
GL_CONSTANT(STENCIL_BUFFER_BIT) /* 1024 */
GL_CONSTANT(STENCIL_CLEAR_VALUE) /* 2961 */
GL_CONSTANT(STENCIL_FAIL) /* 2964 */
GL_CONSTANT(STENCIL_FUNC) /* 2962 */
GL_CONSTANT(STENCIL_INDEX) /* 6401 */
GL_CONSTANT(STENCIL_INDEX8) /* 36168 */
GL_CONSTANT(STENCIL_PASS_DEPTH_FAIL) /* 2965 */
GL_CONSTANT(STENCIL_PASS_DEPTH_PASS) /* 2966 */
GL_CONSTANT(STENCIL_REF) /* 2967 */
GL_CONSTANT(STENCIL_TEST) /* 2960 */
GL_CONSTANT(STENCIL_VALUE_MASK) /* 2963 */
GL_CONSTANT(STENCIL_WRITEMASK) /* 2968 */
GL_CONSTANT(STREAM_COPY) /* 35042 */
GL_CONSTANT(STREAM_DRAW) /* 35040 */
GL_CONSTANT(STREAM_READ) /* 35041 */
GL_CONSTANT(SUBPIXEL_BITS) /* 3408 */
GL_CONSTANT(SYNC_CONDITION) /* 37139 */
GL_CONSTANT(SYNC_FENCE) /* 37142 */
GL_CONSTANT(SYNC_FLAGS) /* 37141 */
GL_CONSTANT(SYNC_FLUSH_COMMANDS_BIT) /* 1 */
GL_CONSTANT(SYNC_GPU_COMMANDS_COMPLETE) /* 37143 */
GL_CONSTANT(SYNC_STATUS) /* 37140 */
GL_CONSTANT(TEXTURE) /* 5890 */
GL_CONSTANT(TEXTURE0) /* 33984 */
GL_CONSTANT(TEXTURE1) /* 33985 */
GL_CONSTANT(TEXTURE2) /* 33986 */
GL_CONSTANT(TEXTURE3) /* 33987 */
GL_CONSTANT(TEXTURE4) /* 33988 */
GL_CONSTANT(TEXTURE5) /* 33989 */
GL_CONSTANT(TEXTURE6) /* 33990 */
GL_CONSTANT(TEXTURE7) /* 33991 */
GL_CONSTANT(TEXTURE8) /* 33992 */
GL_CONSTANT(TEXTURE9) /* 33993 */
GL_CONSTANT(TEXTURE10) /* 33994 */
GL_CONSTANT(TEXTURE11) /* 33995 */
GL_CONSTANT(TEXTURE12) /* 33996 */
GL_CONSTANT(TEXTURE13) /* 33997 */
GL_CONSTANT(TEXTURE14) /* 33998 */
GL_CONSTANT(TEXTURE15) /* 33999 */
GL_CONSTANT(TEXTURE16) /* 34000 */
GL_CONSTANT(TEXTURE17) /* 34001 */
GL_CONSTANT(TEXTURE18) /* 34002 */
GL_CONSTANT(TEXTURE19) /* 34003 */
GL_CONSTANT(TEXTURE20) /* 34004 */
GL_CONSTANT(TEXTURE21) /* 34005 */
GL_CONSTANT(TEXTURE22) /* 34006 */
GL_CONSTANT(TEXTURE23) /* 34007 */
GL_CONSTANT(TEXTURE24) /* 34008 */
GL_CONSTANT(TEXTURE25) /* 34009 */
GL_CONSTANT(TEXTURE26) /* 34010 */
GL_CONSTANT(TEXTURE27) /* 34011 */
GL_CONSTANT(TEXTURE28) /* 34012 */
GL_CONSTANT(TEXTURE29) /* 34013 */
GL_CONSTANT(TEXTURE30) /* 34014 */
GL_CONSTANT(TEXTURE31) /* 34015 */
GL_CONSTANT(TEXTURE_2D) /* 3553 */
GL_CONSTANT(TEXTURE_2D_ARRAY) /* 35866 */
GL_CONSTANT(TEXTURE_3D) /* 32879 */
GL_CONSTANT(TEXTURE_BASE_LEVEL) /* 33084 */
GL_CONSTANT(TEXTURE_BINDING_2D) /* 32873 */
GL_CONSTANT(TEXTURE_BINDING_2D_ARRAY) /* 35869 */
GL_CONSTANT(TEXTURE_BINDING_3D) /* 32874 */
GL_CONSTANT(TEXTURE_BINDING_CUBE_MAP) /* 34068 */
GL_CONSTANT(TEXTURE_COMPARE_FUNC) /* 34893 */
GL_CONSTANT(TEXTURE_COMPARE_MODE) /* 34892 */
GL_CONSTANT(TEXTURE_CUBE_MAP) /* 34067 */
GL_CONSTANT(TEXTURE_CUBE_MAP_NEGATIVE_X) /* 34070 */
GL_CONSTANT(TEXTURE_CUBE_MAP_NEGATIVE_Y) /* 34072 */
GL_CONSTANT(TEXTURE_CUBE_MAP_NEGATIVE_Z) /* 34074 */
GL_CONSTANT(TEXTURE_CUBE_MAP_POSITIVE_X) /* 34069 */
GL_CONSTANT(TEXTURE_CUBE_MAP_POSITIVE_Y) /* 34071 */
GL_CONSTANT(TEXTURE_CUBE_MAP_POSITIVE_Z) /* 34073 */
GL_CONSTANT(TEXTURE_IMMUTABLE_FORMAT) /* 37167 */
GL_CONSTANT(TEXTURE_IMMUTABLE_LEVELS) /* 33503 */
GL_CONSTANT(TEXTURE_MAG_FILTER) /* 10240 */
GL_CONSTANT(TEXTURE_MAX_LEVEL) /* 33085 */
GL_CONSTANT(TEXTURE_MAX_LOD) /* 33083 */
GL_CONSTANT(TEXTURE_MIN_FILTER) /* 10241 */
GL_CONSTANT(TEXTURE_MIN_LOD) /* 33082 */
GL_CONSTANT(TEXTURE_WRAP_R) /* 32882 */
GL_CONSTANT(TEXTURE_WRAP_S) /* 10242 */
GL_CONSTANT(TEXTURE_WRAP_T) /* 10243 */
GL_CONSTANT(TIMEOUT_EXPIRED) /* 37147 */
GL_CONSTANT(TIMEOUT_IGNORED) /*-1 */
GL_CONSTANT(TRANSFORM_FEEDBACK) /* 36386 */
GL_CONSTANT(TRANSFORM_FEEDBACK_ACTIVE) /* 36388 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BINDING) /* 36389 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BUFFER) /* 35982 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BUFFER_BINDING) /* 35983 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BUFFER_MODE) /* 35967 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BUFFER_SIZE) /* 35973 */
GL_CONSTANT(TRANSFORM_FEEDBACK_BUFFER_START) /* 35972 */
GL_CONSTANT(TRANSFORM_FEEDBACK_PAUSED) /* 36387 */
GL_CONSTANT(TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN) /* 35976 */
GL_CONSTANT(TRANSFORM_FEEDBACK_VARYINGS) /* 35971 */
GL_CONSTANT(TRIANGLES) /* 4 */
GL_CONSTANT(TRIANGLE_FAN) /* 6 */
GL_CONSTANT(TRIANGLE_STRIP) /* 5 */
// GL_CONSTANT(TRUE) /*1 */
GL_CONSTANT(UNIFORM_ARRAY_STRIDE) /* 35388 */
GL_CONSTANT(UNIFORM_BLOCK_ACTIVE_UNIFORMS) /* 35394 */
GL_CONSTANT(UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES) /* 35395 */
GL_CONSTANT(UNIFORM_BLOCK_BINDING) /* 35391 */
GL_CONSTANT(UNIFORM_BLOCK_DATA_SIZE) /* 35392 */
GL_CONSTANT(UNIFORM_BLOCK_INDEX) /* 35386 */
GL_CONSTANT(UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER) /* 35397 */
GL_CONSTANT(UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER) /* 35396 */
GL_CONSTANT(UNIFORM_BUFFER) /* 35345 */
GL_CONSTANT(UNIFORM_BUFFER_BINDING) /* 35368 */
GL_CONSTANT(UNIFORM_BUFFER_OFFSET_ALIGNMENT) /* 35380 */
GL_CONSTANT(UNIFORM_BUFFER_SIZE) /* 35370 */
GL_CONSTANT(UNIFORM_BUFFER_START) /* 35369 */
GL_CONSTANT(UNIFORM_IS_ROW_MAJOR) /* 35390 */
GL_CONSTANT(UNIFORM_MATRIX_STRIDE) /* 35389 */
GL_CONSTANT(UNIFORM_OFFSET) /* 35387 */
GL_CONSTANT(UNIFORM_SIZE) /* 35384 */
GL_CONSTANT(UNIFORM_TYPE) /* 35383 */
GL_CONSTANT(UNPACK_ALIGNMENT) /* 3317 */
GL_CONSTANT(UNPACK_COLORSPACE_CONVERSION_WEBGL) /* 37443 */
GL_CONSTANT(UNPACK_FLIP_Y_WEBGL) /* 37440 */
GL_CONSTANT(UNPACK_IMAGE_HEIGHT) /* 32878 */
GL_CONSTANT(UNPACK_PREMULTIPLY_ALPHA_WEBGL) /* 37441 */
GL_CONSTANT(UNPACK_ROW_LENGTH) /* 3314 */
GL_CONSTANT(UNPACK_SKIP_IMAGES) /* 32877 */
GL_CONSTANT(UNPACK_SKIP_PIXELS) /* 3316 */
GL_CONSTANT(UNPACK_SKIP_ROWS) /* 3315 */
GL_CONSTANT(UNSIGNALED) /* 37144 */
GL_CONSTANT(UNSIGNED_BYTE) /* 5121 */
GL_CONSTANT(UNSIGNED_INT) /* 5125 */
GL_CONSTANT(UNSIGNED_INT_10F_11F_11F_REV) /* 35899 */
GL_CONSTANT(UNSIGNED_INT_24_8) /* 34042 */
GL_CONSTANT(UNSIGNED_INT_2_10_10_10_REV) /* 33640 */
GL_CONSTANT(UNSIGNED_INT_5_9_9_9_REV) /* 35902 */
GL_CONSTANT(UNSIGNED_INT_SAMPLER_2D) /* 36306 */
GL_CONSTANT(UNSIGNED_INT_SAMPLER_2D_ARRAY) /* 36311 */
GL_CONSTANT(UNSIGNED_INT_SAMPLER_3D) /* 36307 */
GL_CONSTANT(UNSIGNED_INT_SAMPLER_CUBE) /* 36308 */
GL_CONSTANT(UNSIGNED_INT_VEC2) /* 36294 */
GL_CONSTANT(UNSIGNED_INT_VEC3) /* 36295 */
GL_CONSTANT(UNSIGNED_INT_VEC4) /* 36296 */
GL_CONSTANT(UNSIGNED_NORMALIZED) /* 35863 */
GL_CONSTANT(UNSIGNED_SHORT) /* 5123 */
GL_CONSTANT(UNSIGNED_SHORT_4_4_4_4) /* 32819 */
GL_CONSTANT(UNSIGNED_SHORT_5_5_5_1) /* 32820 */
GL_CONSTANT(UNSIGNED_SHORT_5_6_5) /* 33635 */
GL_CONSTANT(VALIDATE_STATUS) /* 35715 */
GL_CONSTANT(VENDOR) /* 7936 */
GL_CONSTANT(VERSION) /* 7938 */
GL_CONSTANT(VERTEX_ARRAY_BINDING) /* 34229 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_BUFFER_BINDING) /* 34975 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_DIVISOR) /* 35070 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_ENABLED) /* 34338 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_INTEGER) /* 35069 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_NORMALIZED) /* 34922 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_POINTER) /* 34373 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_SIZE) /* 34339 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_STRIDE) /* 34340 */
GL_CONSTANT(VERTEX_ATTRIB_ARRAY_TYPE) /* 34341 */
GL_CONSTANT(VERTEX_SHADER) /* 35633 */
GL_CONSTANT(VIEWPORT) /* 2978 */
GL_CONSTANT(WAIT_FAILED) /* 37149 */
GL_CONSTANT(ZERO) /* 0 */
