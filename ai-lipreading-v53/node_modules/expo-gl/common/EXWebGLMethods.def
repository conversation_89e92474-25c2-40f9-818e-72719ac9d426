
// This listing follows the order in
// https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext

// The WebGL context
NATIVE_METHOD(getContextAttributes)
NATIVE_METHOD(isContextLost)

// Viewing and clipping
NATIVE_METHOD(scissor)
NATIVE_METHOD(viewport)

// State information
NATIVE_METHOD(activeTexture)
NATIVE_METHOD(blendColor)
NATIVE_METHOD(blendEquation)
NATIVE_METHOD(blendEquationSeparate)
NATIVE_METHOD(blendFunc)
NATIVE_METHOD(blendFuncSeparate)
NATIVE_METHOD(clearColor)
NATIVE_METHOD(clearDepth)
NATIVE_METHOD(clearStencil)
NATIVE_METHOD(colorMask)
NATIVE_METHOD(cullFace)
NATIVE_METHOD(depthFunc)
NATIVE_METHOD(depthMask)
NATIVE_METHOD(depthRange)
NATIVE_METHOD(disable)
NATIVE_METHOD(enable)
NATIVE_METHOD(frontFace)
NATIVE_METHOD(getParameter)
NATIVE_METHOD(getError)
NATIVE_METHOD(hint)
NATIVE_METHOD(isEnabled)
NATIVE_METHOD(lineWidth)
NATIVE_METHOD(pixelStorei)
NATIVE_METHOD(polygonOffset)
NATIVE_METHOD(sampleCoverage)
NATIVE_METHOD(stencilFunc)
NATIVE_METHOD(stencilFuncSeparate)
NATIVE_METHOD(stencilMask)
NATIVE_METHOD(stencilMaskSeparate)
NATIVE_METHOD(stencilOp)
NATIVE_METHOD(stencilOpSeparate)

// Buffers
NATIVE_METHOD(bindBuffer)
NATIVE_METHOD(bufferData)
NATIVE_METHOD(bufferSubData)
NATIVE_METHOD(createBuffer)
NATIVE_METHOD(deleteBuffer)
NATIVE_METHOD(getBufferParameter)
NATIVE_METHOD(isBuffer)

// Buffers (WebGL2)
NATIVE_WEBGL2_METHOD(copyBufferSubData)
NATIVE_WEBGL2_METHOD(getBufferSubData)

// Framebuffers
NATIVE_METHOD(bindFramebuffer)
NATIVE_METHOD(checkFramebufferStatus)
NATIVE_METHOD(createFramebuffer)
NATIVE_METHOD(deleteFramebuffer)
NATIVE_METHOD(framebufferRenderbuffer)
NATIVE_METHOD(framebufferTexture2D)
NATIVE_METHOD(getFramebufferAttachmentParameter)
NATIVE_METHOD(isFramebuffer)
NATIVE_METHOD(readPixels)

// Framebuffers (WebGL2)
NATIVE_WEBGL2_METHOD(blitFramebuffer)
NATIVE_WEBGL2_METHOD(framebufferTextureLayer)
NATIVE_WEBGL2_METHOD(invalidateFramebuffer)
NATIVE_WEBGL2_METHOD(invalidateSubFramebuffer)
NATIVE_WEBGL2_METHOD(readBuffer)

// Renderbuffers
NATIVE_METHOD(bindRenderbuffer)
NATIVE_METHOD(createRenderbuffer)
NATIVE_METHOD(deleteRenderbuffer)
NATIVE_METHOD(getRenderbufferParameter)
NATIVE_METHOD(isRenderbuffer)
NATIVE_METHOD(renderbufferStorage)

// Renderbuffers (WebGL2)
NATIVE_WEBGL2_METHOD(getInternalformatParameter)
NATIVE_WEBGL2_METHOD(renderbufferStorageMultisample)

// Textures
NATIVE_METHOD(bindTexture)
NATIVE_METHOD(compressedTexImage2D)
NATIVE_METHOD(compressedTexSubImage2D)
NATIVE_METHOD(copyTexImage2D)
NATIVE_METHOD(copyTexSubImage2D)
NATIVE_METHOD(createTexture)
NATIVE_METHOD(deleteTexture)
NATIVE_METHOD(generateMipmap)
NATIVE_METHOD(getTexParameter)
NATIVE_METHOD(isTexture)
NATIVE_METHOD(texImage2D)
NATIVE_METHOD(texSubImage2D)
NATIVE_METHOD(texParameterf)
NATIVE_METHOD(texParameteri)

// Textures (WebGL2)
NATIVE_WEBGL2_METHOD(texStorage2D)
NATIVE_WEBGL2_METHOD(texStorage3D)
NATIVE_WEBGL2_METHOD(texImage3D)
NATIVE_WEBGL2_METHOD(texSubImage3D)
NATIVE_WEBGL2_METHOD(copyTexSubImage3D)
NATIVE_WEBGL2_METHOD(compressedTexImage3D)
NATIVE_WEBGL2_METHOD(compressedTexSubImage3D)

// Programs and shaders
NATIVE_METHOD(attachShader)
NATIVE_METHOD(bindAttribLocation)
NATIVE_METHOD(compileShader)
NATIVE_METHOD(createProgram)
NATIVE_METHOD(createShader)
NATIVE_METHOD(deleteProgram)
NATIVE_METHOD(deleteShader)
NATIVE_METHOD(detachShader)
NATIVE_METHOD(getAttachedShaders)
NATIVE_METHOD(getProgramParameter)
NATIVE_METHOD(getProgramInfoLog)
NATIVE_METHOD(getShaderParameter)
NATIVE_METHOD(getShaderPrecisionFormat)
NATIVE_METHOD(getShaderInfoLog)
NATIVE_METHOD(getShaderSource)
NATIVE_METHOD(isProgram)
NATIVE_METHOD(isShader)
NATIVE_METHOD(linkProgram)
NATIVE_METHOD(shaderSource)
NATIVE_METHOD(useProgram)
NATIVE_METHOD(validateProgram)

// Programs and shaders (WebGL2)
NATIVE_WEBGL2_METHOD(getFragDataLocation)

// Uniforms and attributes
NATIVE_METHOD(disableVertexAttribArray)
NATIVE_METHOD(enableVertexAttribArray)
NATIVE_METHOD(getActiveAttrib)
NATIVE_METHOD(getActiveUniform)
NATIVE_METHOD(getAttribLocation)
NATIVE_METHOD(getUniform)
NATIVE_METHOD(getUniformLocation)
NATIVE_METHOD(getVertexAttrib)
NATIVE_METHOD(getVertexAttribOffset)
NATIVE_METHOD(uniform1f)
NATIVE_METHOD(uniform1fv)
NATIVE_METHOD(uniform1i)
NATIVE_METHOD(uniform1iv)
NATIVE_METHOD(uniform2f)
NATIVE_METHOD(uniform2fv)
NATIVE_METHOD(uniform2i)
NATIVE_METHOD(uniform2iv)
NATIVE_METHOD(uniform3f)
NATIVE_METHOD(uniform3fv)
NATIVE_METHOD(uniform3i)
NATIVE_METHOD(uniform3iv)
NATIVE_METHOD(uniform4f)
NATIVE_METHOD(uniform4fv)
NATIVE_METHOD(uniform4i)
NATIVE_METHOD(uniform4iv)
NATIVE_METHOD(uniformMatrix2fv)
NATIVE_METHOD(uniformMatrix3fv)
NATIVE_METHOD(uniformMatrix4fv)
NATIVE_METHOD(vertexAttrib1f)
NATIVE_METHOD(vertexAttrib1fv)
NATIVE_METHOD(vertexAttrib2f)
NATIVE_METHOD(vertexAttrib2fv)
NATIVE_METHOD(vertexAttrib3f)
NATIVE_METHOD(vertexAttrib3fv)
NATIVE_METHOD(vertexAttrib4f)
NATIVE_METHOD(vertexAttrib4fv)
NATIVE_METHOD(vertexAttribPointer)

// Uniforms and attributes (WebGL2)
NATIVE_WEBGL2_METHOD(uniform1ui)
NATIVE_WEBGL2_METHOD(uniform2ui)
NATIVE_WEBGL2_METHOD(uniform3ui)
NATIVE_WEBGL2_METHOD(uniform4ui)
NATIVE_WEBGL2_METHOD(uniform1uiv)
NATIVE_WEBGL2_METHOD(uniform2uiv)
NATIVE_WEBGL2_METHOD(uniform3uiv)
NATIVE_WEBGL2_METHOD(uniform4uiv)
NATIVE_WEBGL2_METHOD(uniformMatrix3x2fv)
NATIVE_WEBGL2_METHOD(uniformMatrix4x2fv)
NATIVE_WEBGL2_METHOD(uniformMatrix2x3fv)
NATIVE_WEBGL2_METHOD(uniformMatrix4x3fv)
NATIVE_WEBGL2_METHOD(uniformMatrix2x4fv)
NATIVE_WEBGL2_METHOD(uniformMatrix3x4fv)
NATIVE_WEBGL2_METHOD(vertexAttribI4i)
NATIVE_WEBGL2_METHOD(vertexAttribI4ui)
NATIVE_WEBGL2_METHOD(vertexAttribI4iv)
NATIVE_WEBGL2_METHOD(vertexAttribI4uiv)
NATIVE_WEBGL2_METHOD(vertexAttribIPointer)

// Drawing buffers
NATIVE_METHOD(clear)
NATIVE_METHOD(drawArrays)
NATIVE_METHOD(drawElements)
NATIVE_METHOD(finish)
NATIVE_METHOD(flush)

// Drawing buffers (WebGL2)
NATIVE_WEBGL2_METHOD(vertexAttribDivisor)
NATIVE_WEBGL2_METHOD(drawArraysInstanced)
NATIVE_WEBGL2_METHOD(drawElementsInstanced)
NATIVE_WEBGL2_METHOD(drawRangeElements)
NATIVE_WEBGL2_METHOD(drawBuffers)
NATIVE_WEBGL2_METHOD(clearBufferfv)
NATIVE_WEBGL2_METHOD(clearBufferiv)
NATIVE_WEBGL2_METHOD(clearBufferuiv)
NATIVE_WEBGL2_METHOD(clearBufferfi)

// Query_objects (WebGL2)
NATIVE_WEBGL2_METHOD(createQuery)
NATIVE_WEBGL2_METHOD(deleteQuery)
NATIVE_WEBGL2_METHOD(isQuery)
NATIVE_WEBGL2_METHOD(beginQuery)
NATIVE_WEBGL2_METHOD(endQuery)
NATIVE_WEBGL2_METHOD(getQuery)
NATIVE_WEBGL2_METHOD(getQueryParameter)

// Samplers (WebGL2)
NATIVE_WEBGL2_METHOD(createSampler)
NATIVE_WEBGL2_METHOD(deleteSampler)
NATIVE_WEBGL2_METHOD(bindSampler)
NATIVE_WEBGL2_METHOD(isSampler)
NATIVE_WEBGL2_METHOD(samplerParameteri)
NATIVE_WEBGL2_METHOD(samplerParameterf)
NATIVE_WEBGL2_METHOD(getSamplerParameter)

// Sync objects (WebGL2)
NATIVE_WEBGL2_METHOD(fenceSync)
NATIVE_WEBGL2_METHOD(isSync)
NATIVE_WEBGL2_METHOD(deleteSync)
NATIVE_WEBGL2_METHOD(clientWaitSync)
NATIVE_WEBGL2_METHOD(waitSync)
NATIVE_WEBGL2_METHOD(getSyncParameter)

// Transform feedback (WebGL2)
NATIVE_WEBGL2_METHOD(createTransformFeedback)
NATIVE_WEBGL2_METHOD(deleteTransformFeedback)
NATIVE_WEBGL2_METHOD(isTransformFeedback)
NATIVE_WEBGL2_METHOD(bindTransformFeedback)
NATIVE_WEBGL2_METHOD(beginTransformFeedback)
NATIVE_WEBGL2_METHOD(endTransformFeedback)
NATIVE_WEBGL2_METHOD(transformFeedbackVaryings)
NATIVE_WEBGL2_METHOD(getTransformFeedbackVarying)
NATIVE_WEBGL2_METHOD(pauseTransformFeedback)
NATIVE_WEBGL2_METHOD(resumeTransformFeedback)

// Uniform buffer objects (WebGL2)
NATIVE_WEBGL2_METHOD(bindBufferBase)
NATIVE_WEBGL2_METHOD(bindBufferRange)
NATIVE_WEBGL2_METHOD(getUniformIndices)
NATIVE_WEBGL2_METHOD(getActiveUniforms)
NATIVE_WEBGL2_METHOD(getUniformBlockIndex)
NATIVE_WEBGL2_METHOD(getActiveUniformBlockParameter)
NATIVE_WEBGL2_METHOD(getActiveUniformBlockName)
NATIVE_WEBGL2_METHOD(uniformBlockBinding)

// Verte_WEBGL2x Array Object (WebGL2)
NATIVE_WEBGL2_METHOD(createVertexArray)
NATIVE_WEBGL2_METHOD(deleteVertexArray)
NATIVE_WEBGL2_METHOD(isVertexArray)
NATIVE_WEBGL2_METHOD(bindVertexArray)

// Extensions
NATIVE_METHOD(getSupportedExtensions)
NATIVE_METHOD(getExtension)

// Exponent extensions
NATIVE_METHOD(endFrameEXP)
NATIVE_METHOD(flushEXP)
