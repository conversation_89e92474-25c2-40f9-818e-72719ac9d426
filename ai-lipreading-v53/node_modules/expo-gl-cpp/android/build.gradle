import java.nio.file.Paths

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'maven-publish'

group = 'host.exp.exponent'
version = '11.4.0'

def REACT_NATIVE_DIR = new File(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim()).parent
def RN_BUILD_FROM_SOURCE = findProject(":ReactAndroid") != null
def RN_SO_DIR = RN_BUILD_FROM_SOURCE
    ? Paths.get(findProject(":ReactAndroid").getProjectDir().toString(), "build", "intermediates", "library_*", "*", "jni")
    : "${buildDir}/react-native-0*/jni"
def RN_AAR_DIR = "${REACT_NATIVE_DIR}/android"
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

buildscript {
  def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
  if (expoModulesCorePlugin.exists()) {
    apply from: expoModulesCorePlugin
    applyKotlinExpoModulesCorePlugin()
  }

  // Simple helper that allows the root project to override versions declared by this library.
  ext.safeExtGet = { prop, fallback ->
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
  }

  // Ensures backward compatibility
  ext.getKotlinVersion = {
    if (ext.has("kotlinVersion")) {
      ext.kotlinVersion()
    } else {
      ext.safeExtGet("kotlinVersion", "1.6.10")
    }
  }

  repositories {
    mavenCentral()
  }

  dependencies {
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${getKotlinVersion()}")
  }
}

// Creating sources with comments
task androidSourcesJar(type: Jar) {
  classifier = 'sources'
  from android.sourceSets.main.java.srcDirs
}

afterEvaluate {
  publishing {
    publications {
      release(MavenPublication) {
        from components.release
        // Add additional sourcesJar to artifacts
        artifact(androidSourcesJar)
      }
    }
    repositories {
      maven {
        url = mavenLocal().url
      }
    }
  }
}

android {
  compileSdkVersion safeExtGet("compileSdkVersion", 31)

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_11
    targetCompatibility JavaVersion.VERSION_11
  }

  kotlinOptions {
    jvmTarget = JavaVersion.VERSION_11.majorVersion
  }

  defaultConfig {
    minSdkVersion safeExtGet("minSdkVersion", 21)
    targetSdkVersion safeExtGet("targetSdkVersion", 31)
    versionCode 31
    versionName "11.4.0"

    externalNativeBuild {
      cmake {
        abiFilters (*reactNativeArchitectures())
        arguments "-DREACT_NATIVE_DIR=${REACT_NATIVE_DIR}", "-DRN_SO_DIR=${RN_SO_DIR}"
      }
    }
  }

  externalNativeBuild {
    cmake {
      path "../cpp/CMakeLists.txt"
    }
  }

  buildFeatures {
    // prefab true
  }

  packagingOptions {
    // Gradle will add cmake target dependencies into packaging.
    excludes = [
        "**/libc++_shared.so",
        "**/libjsi.so",
    ]
  }

  configurations {
    extractHeaders
    extractJNI
  }

  lintOptions {
    abortOnError false
  }
}

repositories {
  mavenCentral()
}


dependencies {
  compileOnly 'com.facebook.soloader:soloader:0.8.2'

  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${getKotlinVersion()}"
}

def extractReactNativeAAR = { buildType ->
  def suffix = buildType == 'Debug' ? '-debug' : '-release'
  def rnAARs = fileTree(RN_AAR_DIR).matching { include "**/react-native/**/*${suffix}.aar" }
  if (rnAARs.isEmpty()) {
    rnAARs = fileTree(RN_AAR_DIR).matching { include "**/react-native/**/*.aar" }
  }
  if (rnAARs.any()) {
    // node_modules/react-native has a .aar, extract headers
    if (rnAARs.size() > 1) {
      logger.error("More than one React Native AAR file has been found:")
      rnAARs.each {println(it) }
      throw new GradleException("Multiple React Native AARs found:\n${rnAARs.join("\n")}" +
          "\nRemove the old ones and try again")
    }
  }
  def rnAAR = rnAARs.singleFile
  def file = rnAAR.absoluteFile
  def packageName = file.name.tokenize('-')[0]
  copy {
    from zipTree(file)
    into "$buildDir/$file.name"
    include "jni/**/*"
  }
}

task extractReactNativeAARRelease {
  doLast {
    extractReactNativeAAR('Release')
  }
}

task extractReactNativeAARDebug {
  doLast {
    extractReactNativeAAR('Debug')
  }
}

task extractJNIFiles {
  doLast {
    configurations.extractJNI.files.each {
      def file = it.absoluteFile
      copy {
        from zipTree(file)
        into "$buildDir/$file.name"
        include "jni/**/*"
      }
    }
  }
}

tasks.whenTaskAdded { task ->
  if (!task.name.contains("Clean") && (task.name.contains('externalNativeBuild') || task.name.startsWith('configureCMake'))) {
    def buildType = task.name.endsWith('Debug') ? 'Debug' : 'Release'
    task.dependsOn(extractJNIFiles)
    if (RN_BUILD_FROM_SOURCE) {
      task.dependsOn(":ReactAndroid:copy${buildType}JniLibsProjectOnly")
    } else {
      task.dependsOn("extractReactNativeAAR${buildType}")
    }
  } else if (task.name.startsWith('generateJsonModel') && RN_BUILD_FROM_SOURCE) {
    def buildType = task.name.endsWith('Debug') ? 'Debug' : 'Release'
    task.dependsOn(":ReactAndroid:copy${buildType}JniLibsProjectOnly")
  }
}
