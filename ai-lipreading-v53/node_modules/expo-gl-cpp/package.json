{"name": "expo-gl-cpp", "version": "11.4.0", "description": "C++ bindings for WebGL 2.0 used in Expo GL module", "main": "index.js", "keywords": ["react-native", "expo", "gl", "glview", "webgl", "cpp"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-gl-cpp"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/gl-view/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "e893ff2b01e108cf246cec02318c0df9d6bc603c"}