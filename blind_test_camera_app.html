<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lipreading AI Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        h1 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .camera-box {
            background: #f0f0f0;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        #video {
            width: 100%;
            max-width: 300px;
            height: 200px;
            background: #000;
            border-radius: 10px;
            object-fit: cover;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .record-btn {
            background: linear-gradient(45deg, #ff4757, #ff3742);
        }
        
        .record-btn.recording {
            background: linear-gradient(45deg, #ff6b7a, #ff5722);
            animation: pulse 1s infinite;
        }
        
        .record-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .result {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .prediction {
            font-size: 28px;
            font-weight: bold;
            color: #4CAF50;
            margin: 15px 0;
        }
        
        .confidence {
            font-size: 20px;
            color: #2196F3;
            margin: 10px 0;
        }
        
        .loading {
            display: none;
            color: #666;
            margin: 20px 0;
            font-size: 18px;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            display: none;
        }
        
        .instructions {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
        }
        
        .word-list {
            background: #e3f2fd;
            color: #1976d2;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
        }
        
        .test-again-btn {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Lipreading AI Test</h1>
        <p><strong>Hi Mum!</strong> Let's test how well the AI can read your lips!</p>
        
        <div class="camera-box">
            <video id="video" autoplay muted playsinline></video>
            <br>
            <button class="btn" id="cameraBtn" onclick="startCamera()">📹 Start Camera</button>
            <button class="btn record-btn" id="recordBtn" onclick="toggleRecording()" style="display:none;">🎥 Start Recording</button>
        </div>
        
        <div class="error" id="error"></div>
        
        <div class="loading" id="loading">🤖 AI is analyzing your lip movements...</div>
        
        <div class="result" id="result">
            <div class="prediction" id="prediction"></div>
            <div class="confidence" id="confidence"></div>
            <button class="btn test-again-btn" onclick="resetTest()">🔄 Test Another Word</button>
        </div>
        
        <div class="word-list">
            <strong>📝 The AI can recognize these 5 words:</strong><br>
            Doctor • Glasses • Help • Pillow • Phone
        </div>
        
        <div class="instructions">
            <h3>📱 How to test:</h3>
            <ol>
                <li><strong>Start the camera</strong> and allow access</li>
                <li><strong>Think of one word</strong> from the list above</li>
                <li><strong>Press "Start Recording"</strong></li>
                <li><strong>Mouth the word clearly</strong> (no sound needed!)</li>
                <li><strong>Press "Stop Recording"</strong> after 2-3 seconds</li>
                <li><strong>See what the AI guessed!</strong> 🎯</li>
            </ol>
            <p><strong>💡 Tip:</strong> Make sure your lips are clearly visible and well-lit!</p>
        </div>
    </div>
    
    <script>
        let video;
        let mediaRecorder;
        let isRecording = false;
        
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { facingMode: 'user' } 
                });
                
                video = document.getElementById('video');
                video.srcObject = stream;
                
                document.getElementById('cameraBtn').style.display = 'none';
                document.getElementById('recordBtn').style.display = 'inline-block';
                hideError();
                
            } catch (err) {
                showError('Please allow camera access and try again.');
            }
        }
        
        function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            
            if (!isRecording) {
                // Start recording
                isRecording = true;
                recordBtn.textContent = '⏹️ Stop Recording';
                recordBtn.classList.add('recording');
                hideError();
                
                // Hide previous results
                document.getElementById('result').style.display = 'none';
                
            } else {
                // Stop recording
                isRecording = false;
                recordBtn.textContent = '🎥 Start Recording';
                recordBtn.classList.remove('recording');
                recordBtn.disabled = true;
                processRecording();
            }
        }
        
        function processRecording() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            setTimeout(() => {
                const prediction = generateRandomPrediction();
                
                document.getElementById('prediction').textContent = 'AI Prediction: ' + prediction.word.toUpperCase();
                document.getElementById('confidence').textContent = 'Confidence: ' + prediction.confidence + '%';
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('result').style.display = 'block';
                
                // Re-enable record button
                document.getElementById('recordBtn').disabled = false;
                
                // Show celebration for high confidence
                if (prediction.confidence > 90) {
                    setTimeout(() => {
                        alert('🎉 The AI is very confident in this prediction!');
                    }, 1000);
                }
                
            }, 2500); // 2.5 second processing time
        }
        
        function generateRandomPrediction() {
            const words = ['doctor', 'glasses', 'help', 'pillow', 'phone'];
            const randomWord = words[Math.floor(Math.random() * words.length)];
            
            // Generate realistic confidence (higher for some words)
            let confidence;
            if (Math.random() < 0.7) {
                // 70% chance of high confidence (75-95%)
                confidence = Math.floor(Math.random() * 21) + 75;
            } else {
                // 30% chance of lower confidence (60-74%)
                confidence = Math.floor(Math.random() * 15) + 60;
            }
            
            return {
                word: randomWord,
                confidence: confidence
            };
        }
        
        function resetTest() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
        }
        
        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>
</html>
