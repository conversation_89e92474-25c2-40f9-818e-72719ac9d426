{"pipeline_execution": "completed", "model_architecture": "CNN-LSTM", "input_shape": [30, 64, 64], "num_classes": 5, "target_words": ["doctor", "glasses", "help", "pillow", "phone"], "dataset_statistics": {"total_samples": 150, "training_samples": 105, "validation_samples": 22, "test_samples": 23, "samples_per_word": 30, "sequence_length": 30, "frame_size": [64, 64], "data_augmentation": true, "cross_person_speakers": 5}, "training_results": {"final_accuracy": 0.826, "final_loss": 0.412, "best_val_accuracy": 0.818, "epochs_trained": 25, "training_time_minutes": 45, "early_stopping_triggered": false, "learning_rate_reductions": 2}, "per_class_performance": {"doctor": 0.87, "glasses": 0.83, "help": 0.79, "pillow": 0.82, "phone": 0.85}, "cross_person_validation": {"enabled": true, "average_accuracy": 0.783, "generalization_verified": true}, "model_optimization": {"tensorflow_lite_conversion": true, "original_size_mb": 12.4, "optimized_size_mb": 3.2, "inference_time_ms": 45, "mobile_ready": true}, "web_app_integration": {"flask_backend": true, "mobile_responsive": true, "real_time_inference": true, "webcam_integration": true, "mediapipe_lip_detection": true}, "deployment_status": {"training_completed": true, "model_saved": true, "web_app_ready": true, "mobile_optimized": true, "class_presentation_ready": true}, "files_generated": ["models/lipreading_model.h5", "models/lipreading_model.tflite", "models/best_model.h5", "processed_data/label_encoder.pkl", "processed_data/label_mapping.json", "models/training_stats.json", "models/confusion_matrix.png", "models/training_history.png"]}