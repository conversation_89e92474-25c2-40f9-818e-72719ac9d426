{"model_architecture": "CNN-LSTM", "input_shape": [30, 64, 64], "num_classes": 5, "target_words": ["doctor", "glasses", "help", "pillow", "phone"], "training_samples": 105, "validation_samples": 22, "test_samples": 23, "final_accuracy": 0.826, "final_loss": 0.412, "epochs_trained": 20, "best_val_accuracy": 0.818, "training_completed": true, "cross_person_validation": true, "data_augmentation_applied": true, "model_size_mb": 12.4, "inference_time_ms": 45}