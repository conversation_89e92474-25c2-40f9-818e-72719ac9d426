load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "fb_xplat_cxx_test",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_target",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "androidswitch",
    srcs = glob(
        ["androidswitch/react/renderer/components/androidswitch/*.cpp"],
        exclude = glob(["tests/**/*.cpp"]),
    ),
    headers = glob(
        ["androidswitch/react/renderer/components/androidswitch/*.h"],
        exclude = glob(["tests/**/*.h"]),
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
            ("androidswitch/react/renderer/components/androidswitch", "*.h"),
        ],
        prefix = "react/renderer/components/androidswitch",
    ),
    compiler_flags_pedantic = True,
    cxx_tests = [":tests"],
    fbandroid_deps = [
        react_native_target("jni/react/jni:jni"),
    ],
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("react/renderer/uimanager:uimanager"),
        react_native_xplat_target("react/renderer/componentregistry:componentregistry"),
        "//xplat/js/react-native-github:generated_components-rncore",
    ],
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["tests/**/*.cpp"]),
    headers = glob(["tests/**/*.h"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    contacts = ["<EMAIL>"],
    platforms = (
        # `Apple` and `Android` flavors are disabled because the module depends on `textlayoutmanager` which requires real an Emulator/Simulator to run.
        #  At the same time, the code of tests does not rely on the simulator capabilities and it would be wasteful to add `fbandroid_use_instrumentation_test = True`.
        # (Beware of this option though.)
        # ANDROID,
        # APPLE,
        CXX
    ),
    deps = [
        ":androidswitch",
        "//xplat/third-party/gmock:gtest",
    ],
)
