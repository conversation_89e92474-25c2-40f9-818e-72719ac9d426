load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "APPLE",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "legacyviewmanagerinterop",
    srcs = glob(
        [
            "**/*.cpp",
            "**/*.mm",
        ],
    ),
    headers = [],
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/renderer/components/legacyviewmanagerinterop",
    ),
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = APPLE,
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = ["PUBLIC"],
    deps = [
        "//xplat/folly:dynamic",
        "//xplat/folly:json",
        react_native_xplat_target("react/renderer/core:core"),
        react_native_xplat_target("react/renderer/components/view:view"),
        "//xplat/js/react-native-github:generated_components-rncore",
    ],
)
