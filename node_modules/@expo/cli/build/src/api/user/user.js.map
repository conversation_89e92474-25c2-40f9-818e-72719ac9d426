{"version": 3, "sources": ["../../../../src/api/user/user.ts"], "sourcesContent": ["import { promises as fs } from 'fs';\nimport gql from 'graphql-tag';\n\nimport { CurrentUserQuery } from '../../graphql/generated';\nimport * as Log from '../../log';\nimport * as Analytics from '../../utils/analytics/rudderstackClient';\nimport { getDevelopmentCodeSigningDirectory } from '../../utils/codesigning';\nimport { env } from '../../utils/env';\nimport { graphqlClient } from '../graphql/client';\nimport { UserQuery } from '../graphql/queries/UserQuery';\nimport { fetchAsync } from '../rest/client';\nimport UserSettings from './UserSettings';\n\nexport type Actor = NonNullable<CurrentUserQuery['meActor']>;\n\nlet currentUser: Actor | undefined;\n\nexport const ANONYMOUS_USERNAME = 'anonymous';\n\n/**\n * Resolve the name of the actor, either normal user or robot user.\n * This should be used whenever the \"current user\" needs to be displayed.\n * The display name CANNOT be used as project owner.\n */\nexport function getActorDisplayName(user?: Actor): string {\n  switch (user?.__typename) {\n    case 'User':\n      return user.username;\n    case 'Robot':\n      return user.firstName ? `${user.firstName} (robot)` : 'robot';\n    default:\n      return ANONYMOUS_USERNAME;\n  }\n}\n\nexport async function getUserAsync(): Promise<Actor | undefined> {\n  const hasCredentials = UserSettings.getAccessToken() || UserSettings.getSession()?.sessionSecret;\n  if (!env.EXPO_OFFLINE && !currentUser && hasCredentials) {\n    const user = await UserQuery.currentUserAsync();\n    currentUser = user ?? undefined;\n    if (user) {\n      await Analytics.setUserDataAsync(user.id, {\n        username: getActorDisplayName(user),\n        user_id: user.id,\n        user_type: user.__typename,\n      });\n    }\n  }\n  return currentUser;\n}\n\nexport async function loginAsync(json: {\n  username: string;\n  password: string;\n  otp?: string;\n}): Promise<void> {\n  const res = await fetchAsync('auth/loginAsync', {\n    method: 'POST',\n    body: JSON.stringify(json),\n  });\n  const {\n    data: { sessionSecret },\n  } = await res.json();\n  const result = await graphqlClient\n    .query(\n      gql`\n        query UserQuery {\n          viewer {\n            id\n            username\n          }\n        }\n      `,\n      {},\n      {\n        fetchOptions: {\n          headers: {\n            'expo-session': sessionSecret,\n          },\n        },\n        additionalTypenames: [] /* UserQuery has immutable fields */,\n      }\n    )\n    .toPromise();\n  const {\n    data: { viewer },\n  } = result;\n  await UserSettings.setSessionAsync({\n    sessionSecret,\n    userId: viewer.id,\n    username: viewer.username,\n    currentConnection: 'Username-Password-Authentication',\n  });\n}\n\nexport async function logoutAsync(): Promise<void> {\n  currentUser = undefined;\n  await Promise.all([\n    fs.rm(getDevelopmentCodeSigningDirectory(), { recursive: true, force: true }),\n    UserSettings.setSessionAsync(undefined),\n  ]);\n  Log.log('Logged out');\n}\n"], "names": ["getActorDisplayName", "getUserAsync", "loginAsync", "logoutAsync", "Log", "Analytics", "currentUser", "ANONYMOUS_USERNAME", "user", "__typename", "username", "firstName", "UserSettings", "hasCredentials", "getAccessToken", "getSession", "sessionSecret", "env", "EXPO_OFFLINE", "UserQuery", "currentUserAsync", "undefined", "setUserDataAsync", "id", "user_id", "user_type", "json", "res", "fetchAsync", "method", "body", "JSON", "stringify", "data", "result", "graphqlClient", "query", "gql", "fetchOptions", "headers", "additionalTypenames", "to<PERSON>romise", "viewer", "setSessionAsync", "userId", "currentConnection", "Promise", "all", "fs", "rm", "getDevelopmentCodeSigningDirectory", "recursive", "force", "log"], "mappings": "AAAA;;;;QAwBgBA,mBAAmB,GAAnBA,mBAAmB;QAWbC,YAAY,GAAZA,YAAY;QAgBZC,UAAU,GAAVA,UAAU;QA4CVC,WAAW,GAAXA,WAAW;;AA/FF,IAAA,GAAI,WAAJ,IAAI,CAAA;AACnB,IAAA,WAAa,kCAAb,aAAa,EAAA;AAGjBC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACHC,IAAAA,SAAS,mCAAM,yCAAyC,EAA/C;AAC8B,IAAA,YAAyB,WAAzB,yBAAyB,CAAA;AACxD,IAAA,IAAiB,WAAjB,iBAAiB,CAAA;AACP,IAAA,OAAmB,WAAnB,mBAAmB,CAAA;AACvB,IAAA,UAA8B,WAA9B,8BAA8B,CAAA;AAC7B,IAAA,QAAgB,WAAhB,gBAAgB,CAAA;AAClB,IAAA,aAAgB,kCAAhB,gBAAgB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIzC,IAAIC,WAAW,AAAmB,AAAC;AAE5B,MAAMC,kBAAkB,GAAG,WAAW,AAAC;QAAjCA,kBAAkB,GAAlBA,kBAAkB;AAOxB,SAASP,mBAAmB,CAACQ,IAAY,EAAU;IACxD,OAAQA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEC,UAAU;QACtB,KAAK,MAAM;YACT,OAAOD,IAAI,CAACE,QAAQ,CAAC;QACvB,KAAK,OAAO;YACV,OAAOF,IAAI,CAACG,SAAS,GAAG,CAAC,EAAEH,IAAI,CAACG,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;QAChE;YACE,OAAOJ,kBAAkB,CAAC;KAC7B;CACF;AAEM,eAAeN,YAAY,GAA+B;QACPW,GAAyB;IAAjF,MAAMC,cAAc,GAAGD,aAAY,QAAA,CAACE,cAAc,EAAE,IAAIF,CAAAA,CAAAA,GAAyB,GAAzBA,aAAY,QAAA,CAACG,UAAU,EAAE,SAAe,GAAxCH,KAAAA,CAAwC,GAAxCA,GAAyB,CAAEI,aAAa,CAAA,AAAC;IACjG,IAAI,CAACC,IAAG,IAAA,CAACC,YAAY,IAAI,CAACZ,WAAW,IAAIO,cAAc,EAAE;QACvD,MAAML,IAAI,GAAG,MAAMW,UAAS,UAAA,CAACC,gBAAgB,EAAE,AAAC;QAChDd,WAAW,GAAGE,IAAI,WAAJA,IAAI,GAAIa,SAAS,CAAC;QAChC,IAAIb,IAAI,EAAE;YACR,MAAMH,SAAS,CAACiB,gBAAgB,CAACd,IAAI,CAACe,EAAE,EAAE;gBACxCb,QAAQ,EAAEV,mBAAmB,CAACQ,IAAI,CAAC;gBACnCgB,OAAO,EAAEhB,IAAI,CAACe,EAAE;gBAChBE,SAAS,EAAEjB,IAAI,CAACC,UAAU;aAC3B,CAAC,CAAC;SACJ;KACF;IACD,OAAOH,WAAW,CAAC;CACpB;AAEM,eAAeJ,UAAU,CAACwB,IAIhC,EAAiB;IAChB,MAAMC,GAAG,GAAG,MAAMC,CAAAA,GAAAA,QAAU,AAG1B,CAAA,WAH0B,CAAC,iBAAiB,EAAE;QAC9CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC;KAC3B,CAAC,AAAC;IACH,MAAM,EACJO,IAAI,EAAE,EAAEjB,aAAa,CAAA,EAAE,CAAA,IACxB,GAAG,MAAMW,GAAG,CAACD,IAAI,EAAE,AAAC;IACrB,MAAMQ,MAAM,GAAG,MAAMC,OAAa,cAAA,CAC/BC,KAAK,CACJC,WAAG,QAAA,CAAC;;;;;;;MAOJ,CAAC,EACD,EAAE,EACF;QACEC,YAAY,EAAE;YACZC,OAAO,EAAE;gBACP,cAAc,EAAEvB,aAAa;aAC9B;SACF;QACDwB,mBAAmB,EAAE,EAAE;KACxB,CACF,CACAC,SAAS,EAAE,AAAC;IACf,MAAM,EACJR,IAAI,EAAE,EAAES,MAAM,CAAA,EAAE,CAAA,IACjB,GAAGR,MAAM,AAAC;IACX,MAAMtB,aAAY,QAAA,CAAC+B,eAAe,CAAC;QACjC3B,aAAa;QACb4B,MAAM,EAAEF,MAAM,CAACnB,EAAE;QACjBb,QAAQ,EAAEgC,MAAM,CAAChC,QAAQ;QACzBmC,iBAAiB,EAAE,kCAAkC;KACtD,CAAC,CAAC;CACJ;AAEM,eAAe1C,WAAW,GAAkB;IACjDG,WAAW,GAAGe,SAAS,CAAC;IACxB,MAAMyB,OAAO,CAACC,GAAG,CAAC;QAChBC,GAAE,SAAA,CAACC,EAAE,CAACC,CAAAA,GAAAA,YAAkC,AAAE,CAAA,mCAAF,EAAE,EAAE;YAAEC,SAAS,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;SAAE,CAAC;QAC7ExC,aAAY,QAAA,CAAC+B,eAAe,CAACtB,SAAS,CAAC;KACxC,CAAC,CAAC;IACHjB,GAAG,CAACiD,GAAG,CAAC,YAAY,CAAC,CAAC;CACvB"}