{"version": 3, "sources": ["../../../../src/api/user/otp.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport * as Log from '../../log';\nimport { AbortCommandError, CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport { promptAsync, selectAsync } from '../../utils/prompts';\nimport { fetchAsync } from '../rest/client';\nimport { loginAsync } from './user';\n\nexport enum UserSecondFactorDeviceMethod {\n  AUTHENTICATOR = 'authenticator',\n  SMS = 'sms',\n}\n\n/** Device properties for 2FA */\nexport type SecondFactorDevice = {\n  id: string;\n  method: UserSecondFactorDeviceMethod;\n  sms_phone_number: string | null;\n  is_primary: boolean;\n};\n\nconst nonInteractiveHelp = `Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n  'https://docs.expo.dev/accounts/programmatic-access/'\n)})`;\n\n/**\n * Prompt for an OTP with the option to cancel the question by answering empty (pressing return key).\n */\nasync function promptForOTPAsync(cancelBehavior: 'cancel' | 'menu'): Promise<string | null> {\n  const enterMessage =\n    cancelBehavior === 'cancel'\n      ? chalk`press {bold Enter} to cancel`\n      : chalk`press {bold Enter} for more options`;\n  const { otp } = await promptAsync(\n    {\n      type: 'text',\n      name: 'otp',\n      message: `One-time password or backup code (${enterMessage}):`,\n    },\n    { nonInteractiveHelp }\n  );\n  return otp || null;\n}\n\n/**\n * Prompt for user to choose a backup OTP method. If selected method is SMS, a request\n * for a new OTP will be sent to that method. Then, prompt for the OTP, and retry the user login.\n */\nasync function promptForBackupOTPAsync(\n  username: string,\n  password: string,\n  secondFactorDevices: SecondFactorDevice[]\n): Promise<string | null> {\n  const nonPrimarySecondFactorDevices = secondFactorDevices.filter((device) => !device.is_primary);\n\n  if (nonPrimarySecondFactorDevices.length === 0) {\n    throw new CommandError(\n      'No other second-factor devices set up. Ensure you have set up and certified a backup device.'\n    );\n  }\n\n  const hasAuthenticatorSecondFactorDevice = nonPrimarySecondFactorDevices.find(\n    (device) => device.method === UserSecondFactorDeviceMethod.AUTHENTICATOR\n  );\n\n  const smsNonPrimarySecondFactorDevices = nonPrimarySecondFactorDevices.filter(\n    (device) => device.method === UserSecondFactorDeviceMethod.SMS\n  );\n\n  const authenticatorChoiceSentinel = -1;\n  const cancelChoiceSentinel = -2;\n\n  const deviceChoices = smsNonPrimarySecondFactorDevices.map((device, idx) => ({\n    title: device.sms_phone_number!,\n    value: idx,\n  }));\n\n  if (hasAuthenticatorSecondFactorDevice) {\n    deviceChoices.push({\n      title: 'Authenticator',\n      value: authenticatorChoiceSentinel,\n    });\n  }\n\n  deviceChoices.push({\n    title: 'Cancel',\n    value: cancelChoiceSentinel,\n  });\n\n  const selectedValue = await selectAsync('Select a second-factor device:', deviceChoices, {\n    nonInteractiveHelp,\n  });\n  if (selectedValue === cancelChoiceSentinel) {\n    return null;\n  } else if (selectedValue === authenticatorChoiceSentinel) {\n    return await promptForOTPAsync('cancel');\n  }\n\n  const device = smsNonPrimarySecondFactorDevices[selectedValue];\n\n  await fetchAsync('auth/send-sms-otp', {\n    method: 'POST',\n    body: JSON.stringify({\n      username,\n      password,\n      secondFactorDeviceID: device.id,\n    }),\n  });\n\n  return await promptForOTPAsync('cancel');\n}\n\n/**\n * Handle the special case error indicating that a second-factor is required for\n * authentication.\n *\n * There are three cases we need to handle:\n * 1. User's primary second-factor device was SMS, OTP was automatically sent by the server to that\n *    device already. In this case we should just prompt for the SMS OTP (or backup code), which the\n *    user should be receiving shortly. We should give the user a way to cancel and the prompt and move\n *    to case 3 below.\n * 2. User's primary second-factor device is authenticator. In this case we should prompt for authenticator\n *    OTP (or backup code) and also give the user a way to cancel and move to case 3 below.\n * 3. User doesn't have a primary device or doesn't have access to their primary device. In this case\n *    we should show a picker of the SMS devices that they can have an OTP code sent to, and when\n *    the user picks one we show a prompt() for the sent OTP.\n */\nexport async function retryUsernamePasswordAuthWithOTPAsync(\n  username: string,\n  password: string,\n  metadata: {\n    secondFactorDevices?: SecondFactorDevice[];\n    smsAutomaticallySent?: boolean;\n  }\n): Promise<void> {\n  const { secondFactorDevices, smsAutomaticallySent } = metadata;\n  assert(\n    secondFactorDevices !== undefined && smsAutomaticallySent !== undefined,\n    `Malformed OTP error metadata: ${metadata}`\n  );\n\n  const primaryDevice = secondFactorDevices.find((device) => device.is_primary);\n  let otp: string | null = null;\n\n  if (smsAutomaticallySent) {\n    assert(primaryDevice, 'OTP should only automatically be sent when there is a primary device');\n    Log.log(\n      `One-time password was sent to the phone number ending in ${primaryDevice.sms_phone_number}.`\n    );\n    otp = await promptForOTPAsync('menu');\n  }\n\n  if (primaryDevice?.method === UserSecondFactorDeviceMethod.AUTHENTICATOR) {\n    Log.log('One-time password from authenticator required.');\n    otp = await promptForOTPAsync('menu');\n  }\n\n  // user bailed on case 1 or 2, wants to move to case 3\n  if (!otp) {\n    otp = await promptForBackupOTPAsync(username, password, secondFactorDevices);\n  }\n\n  if (!otp) {\n    throw new AbortCommandError();\n  }\n\n  await loginAsync({\n    username,\n    password,\n    otp,\n  });\n}\n"], "names": ["retryUsernamePasswordAuthWithOTPAsync", "Log", "UserSecondFactorDeviceMethod", "AUTHENTICATOR", "SMS", "nonInteractiveHelp", "learnMore", "promptForOTPAsync", "cancelBehavior", "enterMessage", "chalk", "otp", "promptAsync", "type", "name", "message", "promptForBackupOTPAsync", "username", "password", "secondFactorDevices", "nonPrimarySecondFactorDevices", "filter", "device", "is_primary", "length", "CommandError", "hasAuthenticatorSecondFactorDevice", "find", "method", "smsNonPrimarySecondFactorDevices", "authenticatorChoiceSentinel", "cancelChoiceSentinel", "deviceChoices", "map", "idx", "title", "sms_phone_number", "value", "push", "selected<PERSON><PERSON><PERSON>", "selectAsync", "fetchAsync", "body", "JSON", "stringify", "secondFactorDeviceID", "id", "metadata", "smsAutomaticallySent", "assert", "undefined", "primaryDevice", "log", "AbortCommandError", "loginAsync"], "mappings": "AAAA;;;;QAiIsBA,qCAAqC,GAArCA,qCAAqC;;AAjIxC,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACiC,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AAC1C,IAAA,KAAkB,WAAlB,kBAAkB,CAAA;AACH,IAAA,QAAqB,WAArB,qBAAqB,CAAA;AACnC,IAAA,OAAgB,WAAhB,gBAAgB,CAAA;AAChB,IAAA,KAAQ,WAAR,QAAQ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IAE5B,4BAGN;;UAHWC,4BAA4B;IAA5BA,4BAA4B,CACtCC,eAAa,IAAG,eAAe;IADrBD,4BAA4B,CAEtCE,KAAG,IAAG,KAAK;GAFDF,4BAA4B,4CAA5BA,4BAA4B;AAaxC,MAAMG,kBAAkB,GAAG,CAAC,+DAA+D,EAAEC,CAAAA,GAAAA,KAAS,AAErG,CAAA,UAFqG,CACpG,qDAAqD,CACtD,CAAC,CAAC,CAAC,AAAC;AAEL;;GAEG,CACH,eAAeC,iBAAiB,CAACC,cAAiC,EAA0B;IAC1F,MAAMC,YAAY,GAChBD,cAAc,KAAK,QAAQ,GACvBE,MAAK,QAAA,CAAC,4BAA4B,CAAC,GACnCA,MAAK,QAAA,CAAC,mCAAmC,CAAC,AAAC;IACjD,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,QAAW,AAOhC,CAAA,YAPgC,CAC/B;QACEC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,CAAC,kCAAkC,EAAEN,YAAY,CAAC,EAAE,CAAC;KAC/D,EACD;QAAEJ,kBAAkB;KAAE,CACvB,AAAC;IACF,OAAOM,GAAG,IAAI,IAAI,CAAC;CACpB;AAED;;;GAGG,CACH,eAAeK,uBAAuB,CACpCC,QAAgB,EAChBC,QAAgB,EAChBC,mBAAyC,EACjB;IACxB,MAAMC,6BAA6B,GAAGD,mBAAmB,CAACE,MAAM,CAAC,CAACC,MAAM,GAAK,CAACA,MAAM,CAACC,UAAU;IAAA,CAAC,AAAC;IAEjG,IAAIH,6BAA6B,CAACI,MAAM,KAAK,CAAC,EAAE;QAC9C,MAAM,IAAIC,OAAY,aAAA,CACpB,8FAA8F,CAC/F,CAAC;KACH;IAED,MAAMC,kCAAkC,GAAGN,6BAA6B,CAACO,IAAI,CAC3E,CAACL,MAAM,GAAKA,MAAM,CAACM,MAAM,KArDX,eAAe,AAqD2C;IAAA,CACzE,AAAC;IAEF,MAAMC,gCAAgC,GAAGT,6BAA6B,CAACC,MAAM,CAC3E,CAACC,MAAM,GAAKA,MAAM,CAACM,MAAM,KAxDrB,KAAK,AAwDqD;IAAA,CAC/D,AAAC;IAEF,MAAME,2BAA2B,GAAG,CAAC,CAAC,AAAC;IACvC,MAAMC,oBAAoB,GAAG,CAAC,CAAC,AAAC;IAEhC,MAAMC,aAAa,GAAGH,gCAAgC,CAACI,GAAG,CAAC,CAACX,MAAM,EAAEY,GAAG,GAAK,CAAC;YAC3EC,KAAK,EAAEb,MAAM,CAACc,gBAAgB;YAC9BC,KAAK,EAAEH,GAAG;SACX,CAAC;IAAA,CAAC,AAAC;IAEJ,IAAIR,kCAAkC,EAAE;QACtCM,aAAa,CAACM,IAAI,CAAC;YACjBH,KAAK,EAAE,eAAe;YACtBE,KAAK,EAAEP,2BAA2B;SACnC,CAAC,CAAC;KACJ;IAEDE,aAAa,CAACM,IAAI,CAAC;QACjBH,KAAK,EAAE,QAAQ;QACfE,KAAK,EAAEN,oBAAoB;KAC5B,CAAC,CAAC;IAEH,MAAMQ,aAAa,GAAG,MAAMC,CAAAA,GAAAA,QAAW,AAErC,CAAA,YAFqC,CAAC,gCAAgC,EAAER,aAAa,EAAE;QACvF3B,kBAAkB;KACnB,CAAC,AAAC;IACH,IAAIkC,aAAa,KAAKR,oBAAoB,EAAE;QAC1C,OAAO,IAAI,CAAC;KACb,MAAM,IAAIQ,aAAa,KAAKT,2BAA2B,EAAE;QACxD,OAAO,MAAMvB,iBAAiB,CAAC,QAAQ,CAAC,CAAC;KAC1C;IAED,MAAMe,OAAM,GAAGO,gCAAgC,CAACU,aAAa,CAAC,AAAC;IAE/D,MAAME,CAAAA,GAAAA,OAAU,AAOd,CAAA,WAPc,CAAC,mBAAmB,EAAE;QACpCb,MAAM,EAAE,MAAM;QACdc,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB3B,QAAQ;YACRC,QAAQ;YACR2B,oBAAoB,EAAEvB,OAAM,CAACwB,EAAE;SAChC,CAAC;KACH,CAAC,CAAC;IAEH,OAAO,MAAMvC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;CAC1C;AAiBM,eAAeP,qCAAqC,CACzDiB,QAAgB,EAChBC,QAAgB,EAChB6B,QAGC,EACc;IACf,MAAM,EAAE5B,mBAAmB,CAAA,EAAE6B,oBAAoB,CAAA,EAAE,GAAGD,QAAQ,AAAC;IAC/DE,CAAAA,GAAAA,OAAM,AAGL,CAAA,QAHK,CACJ9B,mBAAmB,KAAK+B,SAAS,IAAIF,oBAAoB,KAAKE,SAAS,EACvE,CAAC,8BAA8B,EAAEH,QAAQ,CAAC,CAAC,CAC5C,CAAC;IAEF,MAAMI,aAAa,GAAGhC,mBAAmB,CAACQ,IAAI,CAAC,CAACL,MAAM,GAAKA,MAAM,CAACC,UAAU;IAAA,CAAC,AAAC;IAC9E,IAAIZ,GAAG,GAAkB,IAAI,AAAC;IAE9B,IAAIqC,oBAAoB,EAAE;QACxBC,CAAAA,GAAAA,OAAM,AAAuF,CAAA,QAAvF,CAACE,aAAa,EAAE,sEAAsE,CAAC,CAAC;QAC9FlD,GAAG,CAACmD,GAAG,CACL,CAAC,yDAAyD,EAAED,aAAa,CAACf,gBAAgB,CAAC,CAAC,CAAC,CAC9F,CAAC;QACFzB,GAAG,GAAG,MAAMJ,iBAAiB,CAAC,MAAM,CAAC,CAAC;KACvC;IAED,IAAI4C,CAAAA,aAAa,QAAQ,GAArBA,KAAAA,CAAqB,GAArBA,aAAa,CAAEvB,MAAM,CAAA,KA/IT,eAAe,AA+IyC,EAAE;QACxE3B,GAAG,CAACmD,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC1DzC,GAAG,GAAG,MAAMJ,iBAAiB,CAAC,MAAM,CAAC,CAAC;KACvC;IAED,sDAAsD;IACtD,IAAI,CAACI,GAAG,EAAE;QACRA,GAAG,GAAG,MAAMK,uBAAuB,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,mBAAmB,CAAC,CAAC;KAC9E;IAED,IAAI,CAACR,GAAG,EAAE;QACR,MAAM,IAAI0C,OAAiB,kBAAA,EAAE,CAAC;KAC/B;IAED,MAAMC,CAAAA,GAAAA,KAAU,AAId,CAAA,WAJc,CAAC;QACfrC,QAAQ;QACRC,QAAQ;QACRP,GAAG;KACJ,CAAC,CAAC;CACJ"}