{"version": 3, "sources": ["../../../../src/api/user/UserSettings.ts"], "sourcesContent": ["import { getExpoHomeDirectory, getUserStatePath } from '@expo/config/build/getUserState';\nimport JsonFile from '@expo/json-file';\nimport crypto from 'crypto';\n\ntype SessionData = {\n  sessionSecret: string;\n  // These fields are potentially used by Expo CLI.\n  userId: string;\n  username: string;\n  currentConnection: 'Username-Password-Authentication';\n};\n\nexport type UserSettingsData = {\n  auth?: SessionData | null;\n  ignoreBundledBinaries?: string[];\n  PATH?: string;\n  /** Last development code signing ID used for `npx expo run:ios`. */\n  developmentCodeSigningId?: string;\n  /** Unique user ID which is generated anonymously and can be cleared locally. */\n  uuid?: string;\n};\n\n/** Return the user cache directory. */\nfunction getDirectory() {\n  return getExpoHomeDirectory();\n}\n\nfunction getFilePath(): string {\n  return getUserStatePath();\n}\n\nfunction userSettingsJsonFile(): JsonFile<UserSettingsData> {\n  return new JsonFile<UserSettingsData>(getFilePath(), {\n    ensureDir: true,\n    jsonParseErrorDefault: {},\n    cantReadFileDefault: {},\n  });\n}\n\nasync function setSessionAsync(sessionData?: SessionData): Promise<void> {\n  await UserSettings.setAsync('auth', sessionData, {\n    default: {},\n    ensureDir: true,\n  });\n}\n\nfunction getSession(): SessionData | null {\n  try {\n    return JsonFile.read<UserSettingsData>(getUserStatePath())?.auth ?? null;\n  } catch (error: any) {\n    if (error.code === 'ENOENT') {\n      return null;\n    }\n    throw error;\n  }\n}\n\nfunction getAccessToken(): string | null {\n  return process.env.EXPO_TOKEN ?? null;\n}\n\n// returns an anonymous, unique identifier for a user on the current computer\nasync function getAnonymousIdentifierAsync(): Promise<string> {\n  const settings = await userSettingsJsonFile();\n  let id = await settings.getAsync('uuid', null);\n\n  if (!id) {\n    id = crypto.randomUUID();\n    await settings.setAsync('uuid', id);\n  }\n\n  return id;\n}\n\nconst UserSettings = Object.assign(userSettingsJsonFile(), {\n  getSession,\n  setSessionAsync,\n  getAccessToken,\n  getDirectory,\n  getFilePath,\n  userSettingsJsonFile,\n  getAnonymousIdentifierAsync,\n});\n\nexport default UserSettings;\n"], "names": ["getDirectory", "getExpoHomeDirectory", "getFilePath", "getUserStatePath", "userSettingsJsonFile", "JsonFile", "ensureDir", "jsonParseE<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantReadFileDefault", "setSessionAsync", "sessionData", "UserSettings", "setAsync", "default", "getSession", "read", "auth", "error", "code", "getAccessToken", "process", "env", "EXPO_TOKEN", "getAnonymousIdentifierAsync", "settings", "id", "getAsync", "crypto", "randomUUID", "Object", "assign"], "mappings": "AAAA;;;;;AAAuD,IAAA,aAAiC,WAAjC,iCAAiC,CAAA;AACnE,IAAA,SAAiB,kCAAjB,iBAAiB,EAAA;AACnB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;;;;;;AAoB3B,uCAAuC,CACvC,SAASA,YAAY,GAAG;IACtB,OAAOC,CAAAA,GAAAA,aAAoB,AAAE,CAAA,qBAAF,EAAE,CAAC;CAC/B;AAED,SAASC,WAAW,GAAW;IAC7B,OAAOC,CAAAA,GAAAA,aAAgB,AAAE,CAAA,iBAAF,EAAE,CAAC;CAC3B;AAED,SAASC,oBAAoB,GAA+B;IAC1D,OAAO,IAAIC,SAAQ,QAAA,CAAmBH,WAAW,EAAE,EAAE;QACnDI,SAAS,EAAE,IAAI;QACfC,qBAAqB,EAAE,EAAE;QACzBC,mBAAmB,EAAE,EAAE;KACxB,CAAC,CAAC;CACJ;AAED,eAAeC,eAAe,CAACC,WAAyB,EAAiB;IACvE,MAAMC,YAAY,CAACC,QAAQ,CAAC,MAAM,EAAEF,WAAW,EAAE;QAC/CG,OAAO,EAAE,EAAE;QACXP,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;CACJ;AAED,SAASQ,UAAU,GAAuB;IACxC,IAAI;YACKT,GAAmD;YAAnDA,IAAyD;QAAhE,OAAOA,CAAAA,IAAyD,GAAzDA,CAAAA,GAAmD,GAAnDA,SAAQ,QAAA,CAACU,IAAI,CAAmBZ,CAAAA,GAAAA,aAAgB,AAAE,CAAA,iBAAF,EAAE,CAAC,SAAM,GAAzDE,KAAAA,CAAyD,GAAzDA,GAAmD,CAAEW,IAAI,YAAzDX,IAAyD,GAAI,IAAI,CAAC;KAC1E,CAAC,OAAOY,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC;SACb;QACD,MAAMD,KAAK,CAAC;KACb;CACF;AAED,SAASE,cAAc,GAAkB;QAChCC,WAAsB;IAA7B,OAAOA,CAAAA,WAAsB,GAAtBA,OAAO,CAACC,GAAG,CAACC,UAAU,YAAtBF,WAAsB,GAAI,IAAI,CAAC;CACvC;AAED,6EAA6E;AAC7E,eAAeG,2BAA2B,GAAoB;IAC5D,MAAMC,QAAQ,GAAG,MAAMpB,oBAAoB,EAAE,AAAC;IAC9C,IAAIqB,EAAE,GAAG,MAAMD,QAAQ,CAACE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,AAAC;IAE/C,IAAI,CAACD,EAAE,EAAE;QACPA,EAAE,GAAGE,OAAM,QAAA,CAACC,UAAU,EAAE,CAAC;QACzB,MAAMJ,QAAQ,CAACZ,QAAQ,CAAC,MAAM,EAAEa,EAAE,CAAC,CAAC;KACrC;IAED,OAAOA,EAAE,CAAC;CACX;AAED,MAAMd,YAAY,GAAGkB,MAAM,CAACC,MAAM,CAAC1B,oBAAoB,EAAE,EAAE;IACzDU,UAAU;IACVL,eAAe;IACfU,cAAc;IACdnB,YAAY;IACZE,WAAW;IACXE,oBAAoB;IACpBmB,2BAA2B;CAC5B,CAAC,AAAC;eAEYZ,YAAY"}