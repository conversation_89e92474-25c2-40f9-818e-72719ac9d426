{"version": 3, "sources": ["../../../src/login/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoLogin: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--username': String,\n      '--password': String,\n      '--otp': String,\n      // Aliases\n      '-h': '--help',\n      '-u': '--username',\n      '-p': '--password',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Log in to an Expo account`,\n      `npx expo login`,\n      [\n        `-u, --username <string>  Username`,\n        `-p, --password <string>  Password`,\n        `--otp <string>           One-time password from your 2FA device`,\n        `-h, --help               Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const { showLoginPromptAsync } = await import('../api/user/actions');\n  return showLoginPromptAsync({\n    // Parsed options\n    username: args['--username'],\n    password: args['--password'],\n    otp: args['--otp'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoLogin", "argv", "args", "assertArgs", "Boolean", "String", "printHelp", "join", "showLoginPromptAsync", "username", "password", "otp", "catch", "logCmdError"], "mappings": "AAAA;;;;;;AAEsC,IAAA,KAAe,WAAf,eAAe,CAAA;AACzB,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMA,SAAS,GAAY,OAAOC,IAAI,GAAK;IAChD,MAAMC,IAAI,GAAGC,CAAAA,GAAAA,KAAU,AAatB,CAAA,WAbsB,CACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,YAAY,EAAEC,MAAM;QACpB,YAAY,EAAEA,MAAM;QACpB,OAAO,EAAEA,MAAM;QACf,UAAU;QACV,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;KACnB,EACDJ,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBI,CAAAA,GAAAA,KAAS,AASR,CAAA,UATQ,CACP,CAAC,yBAAyB,CAAC,EAC3B,CAAC,cAAc,CAAC,EAChB;YACE,CAAC,iCAAiC,CAAC;YACnC,CAAC,iCAAiC,CAAC;YACnC,CAAC,+DAA+D,CAAC;YACjE,CAAC,mCAAmC,CAAC;SACtC,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;KACH;IAED,MAAM,EAAEC,oBAAoB,CAAA,EAAE,GAAG,MAAM;+CAAO,qBAAqB;MAAC,AAAC;IACrE,OAAOA,oBAAoB,CAAC;QAC1B,iBAAiB;QACjBC,QAAQ,EAAEP,IAAI,CAAC,YAAY,CAAC;QAC5BQ,QAAQ,EAAER,IAAI,CAAC,YAAY,CAAC;QAC5BS,GAAG,EAAET,IAAI,CAAC,OAAO,CAAC;KACnB,CAAC,CAACU,KAAK,CAACC,OAAW,YAAA,CAAC,CAAC;CACvB,AAAC;QApCWb,SAAS,GAATA,SAAS"}