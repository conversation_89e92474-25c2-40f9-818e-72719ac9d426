{"version": 3, "sources": ["../../../src/start/startAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport getDevClientProperties from '../utils/analytics/getDevClientProperties';\nimport { logEventAsync } from '../utils/analytics/rudderstackClient';\nimport { installExitHooks } from '../utils/exit';\nimport { isInteractive } from '../utils/interactive';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { profile } from '../utils/profile';\nimport { validateDependenciesVersionsAsync } from './doctor/dependencies/validateDependenciesVersions';\nimport { WebSupportProjectPrerequisite } from './doctor/web/WebSupportProjectPrerequisite';\nimport { startInterfaceAsync } from './interface/startInterface';\nimport { Options, resolvePortsAsync } from './resolveOptions';\nimport { BundlerStartOptions } from './server/BundlerDevServer';\nimport { DevServerManager, MultiBundlerStartOptions } from './server/DevServerManager';\nimport { openPlatformsAsync } from './server/openPlatforms';\nimport { getPlatformBundlers, PlatformBundlers } from './server/platformBundlers';\n\nasync function getMultiBundlerStartOptions(\n  projectRoot: string,\n  { forceManifestType, ...options }: Options,\n  settings: { webOnly?: boolean },\n  platformBundlers: PlatformBundlers\n): Promise<[BundlerStartOptions, MultiBundlerStartOptions]> {\n  const commonOptions: BundlerStartOptions = {\n    mode: options.dev ? 'development' : 'production',\n    devClient: options.devClient,\n    forceManifestType,\n    privateKeyPath: options.privateKeyPath ?? undefined,\n    https: options.https,\n    maxWorkers: options.maxWorkers,\n    resetDevServer: options.clear,\n    minify: options.minify,\n    location: {\n      hostType: options.host,\n      scheme: options.scheme,\n    },\n  };\n  const multiBundlerSettings = await resolvePortsAsync(projectRoot, options, settings);\n\n  const optionalBundlers: Partial<PlatformBundlers> = { ...platformBundlers };\n  // In the default case, we don't want to start multiple bundlers since this is\n  // a bit slower. Our priority (for legacy) is native platforms.\n  if (!options.web) {\n    delete optionalBundlers['web'];\n  }\n\n  const bundlers = [...new Set(Object.values(optionalBundlers))];\n  const multiBundlerStartOptions = bundlers.map((bundler) => {\n    const port =\n      bundler === 'webpack' ? multiBundlerSettings.webpackPort : multiBundlerSettings.metroPort;\n    return {\n      type: bundler,\n      options: {\n        ...commonOptions,\n        port,\n      },\n    };\n  });\n\n  return [commonOptions, multiBundlerStartOptions];\n}\n\nexport async function startAsync(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean }\n) {\n  Log.log(chalk.gray(`Starting project at ${projectRoot}`));\n\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n  const { exp, pkg } = profile(getConfig)(projectRoot);\n\n  const platformBundlers = getPlatformBundlers(exp);\n\n  if (!options.forceManifestType) {\n    if (exp.updates?.useClassicUpdates) {\n      options.forceManifestType = 'classic';\n    } else {\n      const classicUpdatesUrlRegex = /^(staging\\.)?exp\\.host/;\n      let parsedUpdatesUrl: { hostname: string | null } = { hostname: null };\n      if (exp.updates?.url) {\n        try {\n          parsedUpdatesUrl = new URL(exp.updates.url);\n        } catch {\n          Log.error(\n            `Failed to parse \\`updates.url\\` in this project's app config. ${exp.updates.url} is not a valid URL.`\n          );\n        }\n      }\n      const isClassicUpdatesUrl = parsedUpdatesUrl.hostname\n        ? classicUpdatesUrlRegex.test(parsedUpdatesUrl.hostname)\n        : false;\n      options.forceManifestType = isClassicUpdatesUrl ? 'classic' : 'expo-updates';\n    }\n  }\n\n  const [defaultOptions, startOptions] = await getMultiBundlerStartOptions(\n    projectRoot,\n    options,\n    settings,\n    platformBundlers\n  );\n\n  const devServerManager = new DevServerManager(projectRoot, defaultOptions);\n\n  // Validations\n\n  if (options.web || settings.webOnly) {\n    await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n  }\n\n  // Start the server as soon as possible.\n  await profile(devServerManager.startAsync.bind(devServerManager))(startOptions);\n\n  if (!settings.webOnly) {\n    await devServerManager.watchEnvironmentVariables();\n\n    // After the server starts, we can start attempting to bootstrap TypeScript.\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  if (!settings.webOnly && !options.devClient) {\n    await profile(validateDependenciesVersionsAsync)(projectRoot, exp, pkg);\n  }\n\n  // Some tracking thing\n\n  if (options.devClient) {\n    await trackAsync(projectRoot, exp);\n  }\n\n  // Open project on devices.\n  await profile(openPlatformsAsync)(devServerManager, options);\n\n  // Present the Terminal UI.\n  if (isInteractive()) {\n    await profile(startInterfaceAsync)(devServerManager, {\n      platforms: exp.platforms ?? ['ios', 'android', 'web'],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n    if (url) {\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  // Final note about closing the server.\n  const logLocation = settings.webOnly ? 'in the browser console' : 'below';\n  Log.log(\n    chalk`Logs for your project will appear ${logLocation}.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n\nasync function trackAsync(projectRoot: string, exp: ExpoConfig): Promise<void> {\n  await logEventAsync('dev client start command', {\n    status: 'started',\n    ...getDevClientProperties(projectRoot, exp),\n  });\n  installExitHooks(async () => {\n    await logEventAsync('dev client start command', {\n      status: 'finished',\n      ...getDevClientProperties(projectRoot, exp),\n    });\n    // UnifiedAnalytics.flush();\n  });\n}\n"], "names": ["startAsync", "Log", "getMultiBundlerStartOptions", "projectRoot", "forceManifestType", "options", "settings", "platformBundlers", "commonOptions", "mode", "dev", "devClient", "privateKeyPath", "undefined", "https", "maxWorkers", "resetDevServer", "clear", "minify", "location", "hostType", "host", "scheme", "multiBundlerSettings", "resolvePortsAsync", "optionalBundlers", "web", "bundlers", "Set", "Object", "values", "multiBundlerStartOptions", "map", "bundler", "port", "webpackPort", "metroPort", "type", "log", "chalk", "gray", "setNodeEnv", "require", "load", "exp", "pkg", "profile", "getConfig", "getPlatformBundlers", "updates", "useClassicUpdates", "classicUpdatesUrlRegex", "parsedUpdatesUrl", "hostname", "url", "URL", "error", "isClassicUpdatesUrl", "test", "defaultOptions", "startOptions", "devServerManager", "DevServerManager", "webOnly", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "bind", "watchEnvironmentVariables", "bootstrapTypeScriptAsync", "validateDependenciesVersionsAsync", "trackAsync", "openPlatformsAsync", "isInteractive", "startInterfaceAsync", "platforms", "getDefaultDevServer", "getDevServerUrl", "logLocation", "dim", "logEventAsync", "status", "getDevClientProperties", "installExitHooks"], "mappings": "AAAA;;;;QAgEsBA,UAAU,GAAVA,UAAU;AAhEM,IAAA,OAAc,WAAd,cAAc,CAAA;AAClC,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEbC,IAAAA,GAAG,mCAAM,QAAQ,EAAd;AACoB,IAAA,uBAA2C,kCAA3C,2CAA2C,EAAA;AAChD,IAAA,kBAAsC,WAAtC,sCAAsC,CAAA;AACnC,IAAA,KAAe,WAAf,eAAe,CAAA;AAClB,IAAA,YAAsB,WAAtB,sBAAsB,CAAA;AACzB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACrB,IAAA,QAAkB,WAAlB,kBAAkB,CAAA;AACQ,IAAA,6BAAoD,WAApD,oDAAoD,CAAA;AACxD,IAAA,8BAA4C,WAA5C,4CAA4C,CAAA;AACtD,IAAA,eAA4B,WAA5B,4BAA4B,CAAA;AACrB,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AAEF,IAAA,iBAA2B,WAA3B,2BAA2B,CAAA;AACnD,IAAA,cAAwB,WAAxB,wBAAwB,CAAA;AACL,IAAA,iBAA2B,WAA3B,2BAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjF,eAAeC,2BAA2B,CACxCC,WAAmB,EACnB,EAAEC,iBAAiB,CAAA,EAAE,GAAGC,OAAO,EAAW,EAC1CC,QAA+B,EAC/BC,gBAAkC,EACwB;QAKxCF,eAAsB;IAJxC,MAAMG,aAAa,GAAwB;QACzCC,IAAI,EAAEJ,OAAO,CAACK,GAAG,GAAG,aAAa,GAAG,YAAY;QAChDC,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5BP,iBAAiB;QACjBQ,cAAc,EAAEP,CAAAA,eAAsB,GAAtBA,OAAO,CAACO,cAAc,YAAtBP,eAAsB,GAAIQ,SAAS;QACnDC,KAAK,EAAET,OAAO,CAACS,KAAK;QACpBC,UAAU,EAAEV,OAAO,CAACU,UAAU;QAC9BC,cAAc,EAAEX,OAAO,CAACY,KAAK;QAC7BC,MAAM,EAAEb,OAAO,CAACa,MAAM;QACtBC,QAAQ,EAAE;YACRC,QAAQ,EAAEf,OAAO,CAACgB,IAAI;YACtBC,MAAM,EAAEjB,OAAO,CAACiB,MAAM;SACvB;KACF,AAAC;IACF,MAAMC,oBAAoB,GAAG,MAAMC,CAAAA,GAAAA,eAAiB,AAAgC,CAAA,kBAAhC,CAACrB,WAAW,EAAEE,OAAO,EAAEC,QAAQ,CAAC,AAAC;IAErF,MAAMmB,gBAAgB,GAA8B;QAAE,GAAGlB,gBAAgB;KAAE,AAAC;IAC5E,8EAA8E;IAC9E,+DAA+D;IAC/D,IAAI,CAACF,OAAO,CAACqB,GAAG,EAAE;QAChB,OAAOD,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;IAED,MAAME,QAAQ,GAAG;WAAI,IAAIC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACL,gBAAgB,CAAC,CAAC;KAAC,AAAC;IAC/D,MAAMM,wBAAwB,GAAGJ,QAAQ,CAACK,GAAG,CAAC,CAACC,OAAO,GAAK;QACzD,MAAMC,IAAI,GACRD,OAAO,KAAK,SAAS,GAAGV,oBAAoB,CAACY,WAAW,GAAGZ,oBAAoB,CAACa,SAAS,AAAC;QAC5F,OAAO;YACLC,IAAI,EAAEJ,OAAO;YACb5B,OAAO,EAAE;gBACP,GAAGG,aAAa;gBAChB0B,IAAI;aACL;SACF,CAAC;KACH,CAAC,AAAC;IAEH,OAAO;QAAC1B,aAAa;QAAEuB,wBAAwB;KAAC,CAAC;CAClD;AAEM,eAAe/B,UAAU,CAC9BG,WAAmB,EACnBE,OAAgB,EAChBC,QAA+B,EAC/B;IACAL,GAAG,CAACqC,GAAG,CAACC,MAAK,QAAA,CAACC,IAAI,CAAC,CAAC,oBAAoB,EAAErC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1DsC,CAAAA,GAAAA,QAAU,AAA4C,CAAA,WAA5C,CAACpC,OAAO,CAACK,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC;IACvDgC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACxC,WAAW,CAAC,CAAC;IACvC,MAAM,EAAEyC,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAE,GAAGC,CAAAA,GAAAA,QAAO,AAAW,CAAA,QAAX,CAACC,OAAS,UAAA,CAAC,CAAC5C,WAAW,CAAC,AAAC;IAErD,MAAMI,gBAAgB,GAAGyC,CAAAA,GAAAA,iBAAmB,AAAK,CAAA,oBAAL,CAACJ,GAAG,CAAC,AAAC;IAElD,IAAI,CAACvC,OAAO,CAACD,iBAAiB,EAAE;YAC1BwC,GAAW;QAAf,IAAIA,CAAAA,GAAW,GAAXA,GAAG,CAACK,OAAO,SAAmB,GAA9BL,KAAAA,CAA8B,GAA9BA,GAAW,CAAEM,iBAAiB,EAAE;YAClC7C,OAAO,CAACD,iBAAiB,GAAG,SAAS,CAAC;SACvC,MAAM;gBAGDwC,IAAW;YAFf,MAAMO,sBAAsB,2BAA2B,AAAC;YACxD,IAAIC,gBAAgB,GAAgC;gBAAEC,QAAQ,EAAE,IAAI;aAAE,AAAC;YACvE,IAAIT,CAAAA,IAAW,GAAXA,GAAG,CAACK,OAAO,SAAK,GAAhBL,KAAAA,CAAgB,GAAhBA,IAAW,CAAEU,GAAG,EAAE;gBACpB,IAAI;oBACFF,gBAAgB,GAAG,IAAIG,GAAG,CAACX,GAAG,CAACK,OAAO,CAACK,GAAG,CAAC,CAAC;iBAC7C,CAAC,OAAM;oBACNrD,GAAG,CAACuD,KAAK,CACP,CAAC,8DAA8D,EAAEZ,GAAG,CAACK,OAAO,CAACK,GAAG,CAAC,oBAAoB,CAAC,CACvG,CAAC;iBACH;aACF;YACD,MAAMG,mBAAmB,GAAGL,gBAAgB,CAACC,QAAQ,GACjDF,sBAAsB,CAACO,IAAI,CAACN,gBAAgB,CAACC,QAAQ,CAAC,GACtD,KAAK,AAAC;YACVhD,OAAO,CAACD,iBAAiB,GAAGqD,mBAAmB,GAAG,SAAS,GAAG,cAAc,CAAC;SAC9E;KACF;IAED,MAAM,CAACE,cAAc,EAAEC,YAAY,CAAC,GAAG,MAAM1D,2BAA2B,CACtEC,WAAW,EACXE,OAAO,EACPC,QAAQ,EACRC,gBAAgB,CACjB,AAAC;IAEF,MAAMsD,gBAAgB,GAAG,IAAIC,iBAAgB,iBAAA,CAAC3D,WAAW,EAAEwD,cAAc,CAAC,AAAC;IAE3E,cAAc;IAEd,IAAItD,OAAO,CAACqB,GAAG,IAAIpB,QAAQ,CAACyD,OAAO,EAAE;QACnC,MAAMF,gBAAgB,CAACG,8BAA8B,CAACC,8BAA6B,8BAAA,CAAC,CAAC;KACtF;IAED,wCAAwC;IACxC,MAAMnB,CAAAA,GAAAA,QAAO,AAAoD,CAAA,QAApD,CAACe,gBAAgB,CAAC7D,UAAU,CAACkE,IAAI,CAACL,gBAAgB,CAAC,CAAC,CAACD,YAAY,CAAC,CAAC;IAEhF,IAAI,CAACtD,QAAQ,CAACyD,OAAO,EAAE;QACrB,MAAMF,gBAAgB,CAACM,yBAAyB,EAAE,CAAC;QAEnD,4EAA4E;QAC5E,MAAMN,gBAAgB,CAACO,wBAAwB,EAAE,CAAC;KACnD;IAED,IAAI,CAAC9D,QAAQ,CAACyD,OAAO,IAAI,CAAC1D,OAAO,CAACM,SAAS,EAAE;QAC3C,MAAMmC,CAAAA,GAAAA,QAAO,AAAmC,CAAA,QAAnC,CAACuB,6BAAiC,kCAAA,CAAC,CAAClE,WAAW,EAAEyC,GAAG,EAAEC,GAAG,CAAC,CAAC;KACzE;IAED,sBAAsB;IAEtB,IAAIxC,OAAO,CAACM,SAAS,EAAE;QACrB,MAAM2D,UAAU,CAACnE,WAAW,EAAEyC,GAAG,CAAC,CAAC;KACpC;IAED,2BAA2B;IAC3B,MAAME,CAAAA,GAAAA,QAAO,AAAoB,CAAA,QAApB,CAACyB,cAAkB,mBAAA,CAAC,CAACV,gBAAgB,EAAExD,OAAO,CAAC,CAAC;IAE7D,2BAA2B;IAC3B,IAAImE,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,EAAE;YAEN5B,UAAa;QAD1B,MAAME,CAAAA,GAAAA,QAAO,AAAqB,CAAA,QAArB,CAAC2B,eAAmB,oBAAA,CAAC,CAACZ,gBAAgB,EAAE;YACnDa,SAAS,EAAE9B,CAAAA,UAAa,GAAbA,GAAG,CAAC8B,SAAS,YAAb9B,UAAa,GAAI;gBAAC,KAAK;gBAAE,SAAS;gBAAE,KAAK;aAAC;SACtD,CAAC,CAAC;KACJ,MAAM;YAEOiB,IAAsC;QADlD,uCAAuC;QACvC,MAAMP,GAAG,GAAGO,CAAAA,IAAsC,GAAtCA,gBAAgB,CAACc,mBAAmB,EAAE,SAAiB,GAAvDd,KAAAA,CAAuD,GAAvDA,IAAsC,CAAEe,eAAe,EAAE,AAAC;QACtE,IAAItB,GAAG,EAAE;YACPrD,GAAG,CAACqC,GAAG,CAACC,MAAK,QAAA,CAAC,sBAAsB,EAAEe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C;KACF;IAED,uCAAuC;IACvC,MAAMuB,WAAW,GAAGvE,QAAQ,CAACyD,OAAO,GAAG,wBAAwB,GAAG,OAAO,AAAC;IAC1E9D,GAAG,CAACqC,GAAG,CACLC,MAAK,QAAA,CAAC,kCAAkC,EAAEsC,WAAW,CAAC,CAAC,EACrDL,CAAAA,GAAAA,YAAa,AAAE,CAAA,cAAF,EAAE,GAAGjC,MAAK,QAAA,CAACuC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAG,EAAE,CAC3D,CAAC,CACH,CAAC;CACH;AAED,eAAeR,UAAU,CAACnE,WAAmB,EAAEyC,GAAe,EAAiB;IAC7E,MAAMmC,CAAAA,GAAAA,kBAAa,AAGjB,CAAA,cAHiB,CAAC,0BAA0B,EAAE;QAC9CC,MAAM,EAAE,SAAS;QACjB,GAAGC,CAAAA,GAAAA,uBAAsB,AAAkB,CAAA,QAAlB,CAAC9E,WAAW,EAAEyC,GAAG,CAAC;KAC5C,CAAC,CAAC;IACHsC,CAAAA,GAAAA,KAAgB,AAMd,CAAA,iBANc,CAAC,UAAY;QAC3B,MAAMH,CAAAA,GAAAA,kBAAa,AAGjB,CAAA,cAHiB,CAAC,0BAA0B,EAAE;YAC9CC,MAAM,EAAE,UAAU;YAClB,GAAGC,CAAAA,GAAAA,uBAAsB,AAAkB,CAAA,QAAlB,CAAC9E,WAAW,EAAEyC,GAAG,CAAC;SAC5C,CAAC,CAAC;IACH,4BAA4B;KAC7B,CAAC,CAAC;CACJ"}