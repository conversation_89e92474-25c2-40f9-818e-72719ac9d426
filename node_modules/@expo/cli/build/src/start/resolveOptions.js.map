{"version": 3, "sources": ["../../../src/start/resolveOptions.ts"], "sourcesContent": ["import assert from 'assert';\n\nimport { hasDirectDevClientDependency } from '../utils/analytics/getDevClientProperties';\nimport { AbortCommandError, CommandError } from '../utils/errors';\nimport { resolvePortAsync } from '../utils/port';\n\nexport type Options = {\n  forceManifestType: 'classic' | 'expo-updates';\n  privateKeyPath: string | null;\n  android: boolean;\n  web: boolean;\n  ios: boolean;\n  offline: boolean;\n  clear: boolean;\n  dev: boolean;\n  https: boolean;\n  maxWorkers: number;\n  port: number;\n  /** Should instruct the bundler to create minified bundles. */\n  minify: boolean;\n  devClient: boolean;\n  scheme: string | null;\n  host: 'localhost' | 'lan' | 'tunnel';\n};\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  const forceManifestType = args['--force-manifest-type'];\n  if (forceManifestType) {\n    assert.match(forceManifestType, /^(classic|expo-updates)$/);\n  }\n  if (args['--dev-client'] && args['--go']) {\n    throw new CommandError('BAD_ARGS', 'Cannot use both --dev-client and --go together.');\n  }\n  const host = resolveHostType({\n    host: args['--host'],\n    offline: args['--offline'],\n    lan: args['--lan'],\n    localhost: args['--localhost'],\n    tunnel: args['--tunnel'],\n  });\n\n  // User can force the default target by passing either `--dev-client` or `--go`. They can also\n  // swap between them during development by pressing `s`.\n  const isUserDefinedDevClient =\n    !!args['--dev-client'] || (args['--go'] == null ? false : !args['--go']);\n\n  // If the user didn't specify `--dev-client` or `--go` we check if they have the dev client package\n  // in their package.json.\n  const isAutoDevClient =\n    args['--dev-client'] == null &&\n    args['--go'] == null &&\n    hasDirectDevClientDependency(projectRoot);\n\n  const isDevClient = isAutoDevClient || isUserDefinedDevClient;\n\n  const scheme = await resolveSchemeAsync(projectRoot, {\n    scheme: args['--scheme'],\n    devClient: isDevClient,\n  });\n\n  return {\n    forceManifestType,\n    privateKeyPath: args['--private-key-path'] ?? null,\n\n    android: !!args['--android'],\n    web: !!args['--web'],\n    ios: !!args['--ios'],\n    offline: !!args['--offline'],\n\n    clear: !!args['--clear'],\n    dev: !args['--no-dev'],\n    https: !!args['--https'],\n    maxWorkers: args['--max-workers'],\n    port: args['--port'],\n    minify: !!args['--minify'],\n\n    devClient: isDevClient,\n\n    scheme,\n    host,\n  };\n}\n\nexport async function resolveSchemeAsync(\n  projectRoot: string,\n  options: { scheme?: string; devClient?: boolean }\n): Promise<string | null> {\n  const resolveFrom = require('resolve-from') as typeof import('resolve-from');\n\n  const isDevClientPackageInstalled = (() => {\n    try {\n      // we check if `expo-dev-launcher` is installed instead of `expo-dev-client`\n      // because someone could install only launcher.\n      resolveFrom(projectRoot, 'expo-dev-launcher');\n      return true;\n    } catch {\n      return false;\n    }\n  })();\n\n  if (typeof options.scheme === 'string') {\n    // Use the custom scheme\n    return options.scheme ?? null;\n  } else if (options.devClient || isDevClientPackageInstalled) {\n    const { getOptionalDevClientSchemeAsync } =\n      require('../utils/scheme') as typeof import('../utils/scheme');\n    // Attempt to find the scheme or warn the user how to setup a custom scheme\n    return await getOptionalDevClientSchemeAsync(projectRoot);\n  } else {\n    // Ensure this is reset when users don't use `--scheme`, `--dev-client` and don't have the `expo-dev-client` package installed.\n    return null;\n  }\n}\n\n/** Resolve and assert host type options. */\nexport function resolveHostType(options: {\n  host?: string;\n  offline?: boolean;\n  lan?: boolean;\n  localhost?: boolean;\n  tunnel?: boolean;\n}): 'lan' | 'tunnel' | 'localhost' {\n  if (\n    [options.offline, options.host, options.lan, options.localhost, options.tunnel].filter((i) => i)\n      .length > 1\n  ) {\n    throw new CommandError(\n      'BAD_ARGS',\n      'Specify at most one of: --offline, --host, --tunnel, --lan, --localhost'\n    );\n  }\n\n  if (options.offline) {\n    // Force `lan` in offline mode.\n    return 'lan';\n  } else if (options.host) {\n    assert.match(options.host, /^(lan|tunnel|localhost)$/);\n    return options.host as 'lan' | 'tunnel' | 'localhost';\n  } else if (options.tunnel) {\n    return 'tunnel';\n  } else if (options.lan) {\n    return 'lan';\n  } else if (options.localhost) {\n    return 'localhost';\n  }\n  return 'lan';\n}\n\n/** Resolve the port options for all supported bundlers. */\nexport async function resolvePortsAsync(\n  projectRoot: string,\n  options: Partial<Pick<Options, 'port' | 'devClient'>>,\n  settings: { webOnly?: boolean }\n) {\n  const multiBundlerSettings: { webpackPort?: number; metroPort?: number } = {};\n\n  if (settings.webOnly) {\n    const webpackPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      // Default web port\n      fallbackPort: 19006,\n    });\n    if (!webpackPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.webpackPort = webpackPort;\n  } else {\n    const fallbackPort = process.env.RCT_METRO_PORT\n      ? parseInt(process.env.RCT_METRO_PORT, 10)\n      : 8081;\n    const metroPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      fallbackPort,\n    });\n    if (!metroPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.metroPort = metroPort;\n  }\n\n  return multiBundlerSettings;\n}\n"], "names": ["resolveOptionsAsync", "resolveSchemeAsync", "resolveHostType", "resolvePortsAsync", "projectRoot", "args", "forceManifestType", "assert", "match", "CommandError", "host", "offline", "lan", "localhost", "tunnel", "isUserDefinedDevClient", "isAutoDevClient", "hasDirectDevClientDependency", "isDevClient", "scheme", "devClient", "privateKeyPath", "android", "web", "ios", "clear", "dev", "https", "maxWorkers", "port", "minify", "options", "resolveFrom", "require", "isDevClientPackageInstalled", "getOptionalDevClientSchemeAsync", "filter", "i", "length", "settings", "multiBundlerSettings", "webOnly", "webpackPort", "resolvePortAsync", "defaultPort", "fallback<PERSON>ort", "AbortCommandError", "process", "env", "RCT_METRO_PORT", "parseInt", "metroPort"], "mappings": "AAAA;;;;QAyBsBA,mBAAmB,GAAnBA,mBAAmB;QA0DnBC,kBAAkB,GAAlBA,kBAAkB;QAgCxBC,eAAe,GAAfA,eAAe;QAkCTC,iBAAiB,GAAjBA,iBAAiB;AArJpB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AAEkB,IAAA,uBAA2C,WAA3C,2CAA2C,CAAA;AACxC,IAAA,OAAiB,WAAjB,iBAAiB,CAAA;AAChC,IAAA,KAAe,WAAf,eAAe,CAAA;;;;;;AAqBzC,eAAeH,mBAAmB,CAACI,WAAmB,EAAEC,IAAS,EAAoB;IAC1F,MAAMC,iBAAiB,GAAGD,IAAI,CAAC,uBAAuB,CAAC,AAAC;IACxD,IAAIC,iBAAiB,EAAE;QACrBC,OAAM,QAAA,CAACC,KAAK,CAACF,iBAAiB,6BAA6B,CAAC;KAC7D;IACD,IAAID,IAAI,CAAC,cAAc,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;QACxC,MAAM,IAAII,OAAY,aAAA,CAAC,UAAU,EAAE,iDAAiD,CAAC,CAAC;KACvF;IACD,MAAMC,IAAI,GAAGR,eAAe,CAAC;QAC3BQ,IAAI,EAAEL,IAAI,CAAC,QAAQ,CAAC;QACpBM,OAAO,EAAEN,IAAI,CAAC,WAAW,CAAC;QAC1BO,GAAG,EAAEP,IAAI,CAAC,OAAO,CAAC;QAClBQ,SAAS,EAAER,IAAI,CAAC,aAAa,CAAC;QAC9BS,MAAM,EAAET,IAAI,CAAC,UAAU,CAAC;KACzB,CAAC,AAAC;IAEH,8FAA8F;IAC9F,wDAAwD;IACxD,MAAMU,sBAAsB,GAC1B,CAAC,CAACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,CAACA,IAAI,CAAC,MAAM,CAAC,CAAC,AAAC;IAE3E,mGAAmG;IACnG,yBAAyB;IACzB,MAAMW,eAAe,GACnBX,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAC5BA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IACpBY,CAAAA,GAAAA,uBAA4B,AAAa,CAAA,6BAAb,CAACb,WAAW,CAAC,AAAC;IAE5C,MAAMc,WAAW,GAAGF,eAAe,IAAID,sBAAsB,AAAC;IAE9D,MAAMI,MAAM,GAAG,MAAMlB,kBAAkB,CAACG,WAAW,EAAE;QACnDe,MAAM,EAAEd,IAAI,CAAC,UAAU,CAAC;QACxBe,SAAS,EAAEF,WAAW;KACvB,CAAC,AAAC;QAIeb,GAA0B;IAF5C,OAAO;QACLC,iBAAiB;QACjBe,cAAc,EAAEhB,CAAAA,GAA0B,GAA1BA,IAAI,CAAC,oBAAoB,CAAC,YAA1BA,GAA0B,GAAI,IAAI;QAElDiB,OAAO,EAAE,CAAC,CAACjB,IAAI,CAAC,WAAW,CAAC;QAC5BkB,GAAG,EAAE,CAAC,CAAClB,IAAI,CAAC,OAAO,CAAC;QACpBmB,GAAG,EAAE,CAAC,CAACnB,IAAI,CAAC,OAAO,CAAC;QACpBM,OAAO,EAAE,CAAC,CAACN,IAAI,CAAC,WAAW,CAAC;QAE5BoB,KAAK,EAAE,CAAC,CAACpB,IAAI,CAAC,SAAS,CAAC;QACxBqB,GAAG,EAAE,CAACrB,IAAI,CAAC,UAAU,CAAC;QACtBsB,KAAK,EAAE,CAAC,CAACtB,IAAI,CAAC,SAAS,CAAC;QACxBuB,UAAU,EAAEvB,IAAI,CAAC,eAAe,CAAC;QACjCwB,IAAI,EAAExB,IAAI,CAAC,QAAQ,CAAC;QACpByB,MAAM,EAAE,CAAC,CAACzB,IAAI,CAAC,UAAU,CAAC;QAE1Be,SAAS,EAAEF,WAAW;QAEtBC,MAAM;QACNT,IAAI;KACL,CAAC;CACH;AAEM,eAAeT,kBAAkB,CACtCG,WAAmB,EACnB2B,OAAiD,EACzB;IACxB,MAAMC,WAAW,GAAGC,OAAO,CAAC,cAAc,CAAC,AAAiC,AAAC;IAE7E,MAAMC,2BAA2B,GAAG,CAAC,IAAM;QACzC,IAAI;YACF,4EAA4E;YAC5E,+CAA+C;YAC/CF,WAAW,CAAC5B,WAAW,EAAE,mBAAmB,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;SACb,CAAC,OAAM;YACN,OAAO,KAAK,CAAC;SACd;KACF,CAAC,EAAE,AAAC;IAEL,IAAI,OAAO2B,OAAO,CAACZ,MAAM,KAAK,QAAQ,EAAE;YAE/BY,OAAc;QADrB,wBAAwB;QACxB,OAAOA,CAAAA,OAAc,GAAdA,OAAO,CAACZ,MAAM,YAAdY,OAAc,GAAI,IAAI,CAAC;KAC/B,MAAM,IAAIA,OAAO,CAACX,SAAS,IAAIc,2BAA2B,EAAE;QAC3D,MAAM,EAAEC,+BAA+B,CAAA,EAAE,GACvCF,OAAO,CAAC,iBAAiB,CAAC,AAAoC,AAAC;QACjE,2EAA2E;QAC3E,OAAO,MAAME,+BAA+B,CAAC/B,WAAW,CAAC,CAAC;KAC3D,MAAM;QACL,+HAA+H;QAC/H,OAAO,IAAI,CAAC;KACb;CACF;AAGM,SAASF,eAAe,CAAC6B,OAM/B,EAAkC;IACjC,IACE;QAACA,OAAO,CAACpB,OAAO;QAAEoB,OAAO,CAACrB,IAAI;QAAEqB,OAAO,CAACnB,GAAG;QAAEmB,OAAO,CAAClB,SAAS;QAAEkB,OAAO,CAACjB,MAAM;KAAC,CAACsB,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC;IAAA,CAAC,CAC7FC,MAAM,GAAG,CAAC,EACb;QACA,MAAM,IAAI7B,OAAY,aAAA,CACpB,UAAU,EACV,yEAAyE,CAC1E,CAAC;KACH;IAED,IAAIsB,OAAO,CAACpB,OAAO,EAAE;QACnB,+BAA+B;QAC/B,OAAO,KAAK,CAAC;KACd,MAAM,IAAIoB,OAAO,CAACrB,IAAI,EAAE;QACvBH,OAAM,QAAA,CAACC,KAAK,CAACuB,OAAO,CAACrB,IAAI,6BAA6B,CAAC;QACvD,OAAOqB,OAAO,CAACrB,IAAI,CAAmC;KACvD,MAAM,IAAIqB,OAAO,CAACjB,MAAM,EAAE;QACzB,OAAO,QAAQ,CAAC;KACjB,MAAM,IAAIiB,OAAO,CAACnB,GAAG,EAAE;QACtB,OAAO,KAAK,CAAC;KACd,MAAM,IAAImB,OAAO,CAAClB,SAAS,EAAE;QAC5B,OAAO,WAAW,CAAC;KACpB;IACD,OAAO,KAAK,CAAC;CACd;AAGM,eAAeV,iBAAiB,CACrCC,WAAmB,EACnB2B,OAAqD,EACrDQ,QAA+B,EAC/B;IACA,MAAMC,oBAAoB,GAAiD,EAAE,AAAC;IAE9E,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,WAAW,GAAG,MAAMC,CAAAA,GAAAA,KAAgB,AAIxC,CAAA,iBAJwC,CAACvC,WAAW,EAAE;YACtDwC,WAAW,EAAEb,OAAO,CAACF,IAAI;YACzB,mBAAmB;YACnBgB,YAAY,EAAE,KAAK;SACpB,CAAC,AAAC;QACH,IAAI,CAACH,WAAW,EAAE;YAChB,MAAM,IAAII,OAAiB,kBAAA,EAAE,CAAC;SAC/B;QACDN,oBAAoB,CAACE,WAAW,GAAGA,WAAW,CAAC;KAChD,MAAM;QACL,MAAMG,YAAY,GAAGE,OAAO,CAACC,GAAG,CAACC,cAAc,GAC3CC,QAAQ,CAACH,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE,EAAE,CAAC,GACxC,IAAI,AAAC;QACT,MAAME,SAAS,GAAG,MAAMR,CAAAA,GAAAA,KAAgB,AAGtC,CAAA,iBAHsC,CAACvC,WAAW,EAAE;YACpDwC,WAAW,EAAEb,OAAO,CAACF,IAAI;YACzBgB,YAAY;SACb,CAAC,AAAC;QACH,IAAI,CAACM,SAAS,EAAE;YACd,MAAM,IAAIL,OAAiB,kBAAA,EAAE,CAAC;SAC/B;QACDN,oBAAoB,CAACW,SAAS,GAAGA,SAAS,CAAC;KAC5C;IAED,OAAOX,oBAAoB,CAAC;CAC7B"}