{"version": 3, "sources": ["../../../../src/start/doctor/SecurityBinPrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\n\nimport { Prerequisite, PrerequisiteCommandError } from './Prerequisite';\n\nexport class SecurityBinPrerequisite extends Prerequisite {\n  static instance = new SecurityBinPrerequisite();\n\n  async assertImplementation(): Promise<void> {\n    try {\n      // make sure we can run security\n      await spawnAsync('which', ['security']);\n    } catch {\n      throw new PrerequisiteCommandError(\n        'SECURITY_BIN',\n        \"Cannot code sign project because the CLI `security` is not available on your computer.\\nPlease ensure it's installed and try again.\"\n      );\n    }\n  }\n}\n"], "names": ["SecurityBinPrerequisite", "Prerequisite", "instance", "assertImplementation", "spawnAsync", "PrerequisiteCommandError"], "mappings": "AAAA;;;;AAAuB,IAAA,WAAmB,kCAAnB,mBAAmB,EAAA;AAEa,IAAA,aAAgB,WAAhB,gBAAgB,CAAA;;;;;;AAEhE,MAAMA,uBAAuB,SAASC,aAAY,aAAA;IACvD,OAAOC,QAAQ,GAAG,IAAIF,uBAAuB,EAAE,CAAC;IAEhD,MAAMG,oBAAoB,GAAkB;QAC1C,IAAI;YACF,gCAAgC;YAChC,MAAMC,CAAAA,GAAAA,WAAU,AAAuB,CAAA,QAAvB,CAAC,OAAO,EAAE;gBAAC,UAAU;aAAC,CAAC,CAAC;SACzC,CAAC,OAAM;YACN,MAAM,IAAIC,aAAwB,yBAAA,CAChC,cAAc,EACd,qIAAqI,CACtI,CAAC;SACH;KACF;CACF;QAdYL,uBAAuB,GAAvBA,uBAAuB"}