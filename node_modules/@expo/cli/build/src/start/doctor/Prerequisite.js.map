{"version": 3, "sources": ["../../../../src/start/doctor/Prerequisite.ts"], "sourcesContent": ["import { CommandError, UnimplementedError } from '../../utils/errors';\nimport { memoize } from '../../utils/fn';\n\n/** An error that is memoized and asserted whenever a Prerequisite.assertAsync is subsequently called. */\nexport class PrerequisiteCommandError extends CommandError {\n  constructor(code: string, message: string = '') {\n    super(message ? 'VALIDATE_' + code : code, message);\n  }\n}\n\nexport class Prerequisite<T = void, TProps = void> {\n  /** Memoized results of `assertImplementation` */\n  private _assertAsync: (props: TProps) => Promise<T>;\n\n  constructor() {\n    this._assertAsync = memoize(this.assertImplementation.bind(this));\n  }\n\n  /** An optional warning to call before running the memoized assertion.  */\n  protected cachedError?: PrerequisiteCommandError;\n\n  /** Reset the assertion memo and warning message. */\n  public resetAssertion(props: TProps) {\n    this.cachedError = undefined;\n    this._assertAsync = memoize(this.assertImplementation.bind(this));\n  }\n\n  async assertAsync(props: TProps): Promise<T> {\n    if (this.cachedError) {\n      throw this.cachedError;\n    }\n    try {\n      return await this._assertAsync(props);\n    } catch (error) {\n      if (error instanceof PrerequisiteCommandError) {\n        this.cachedError = error;\n      }\n      throw error;\n    }\n  }\n\n  /** Exposed for testing. */\n  async assertImplementation(props: TProps): Promise<T> {\n    throw new UnimplementedError();\n  }\n}\n\n/** A prerequisite that is project specific. */\nexport class ProjectPrerequisite<T = void, TProps = void> extends Prerequisite<T, TProps> {\n  constructor(protected projectRoot: string) {\n    super();\n  }\n}\n"], "names": ["PrerequisiteCommandError", "CommandError", "constructor", "code", "message", "Prerequisite", "_assertAsync", "memoize", "assertImplementation", "bind", "resetAssertion", "props", "cachedError", "undefined", "assertAsync", "error", "UnimplementedError", "ProjectPrerequisite", "projectRoot"], "mappings": "AAAA;;;;AAAiD,IAAA,OAAoB,WAApB,oBAAoB,CAAA;AAC7C,IAAA,GAAgB,WAAhB,gBAAgB,CAAA;AAGjC,MAAMA,wBAAwB,SAASC,OAAY,aAAA;IACxDC,YAAYC,IAAY,EAAEC,OAAe,GAAG,EAAE,CAAE;QAC9C,KAAK,CAACA,OAAO,GAAG,WAAW,GAAGD,IAAI,GAAGA,IAAI,EAAEC,OAAO,CAAC,CAAC;KACrD;CACF;QAJYJ,wBAAwB,GAAxBA,wBAAwB;AAM9B,MAAMK,YAAY;IAIvBH,aAAc;QACZ,IAAI,CAACI,YAAY,GAAGC,CAAAA,GAAAA,GAAO,AAAsC,CAAA,QAAtC,CAAC,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KACnE;IAKD,oDAAoD,CACpD,AAAOC,cAAc,CAACC,KAAa,EAAE;QACnC,IAAI,CAACC,WAAW,GAAGC,SAAS,CAAC;QAC7B,IAAI,CAACP,YAAY,GAAGC,CAAAA,GAAAA,GAAO,AAAsC,CAAA,QAAtC,CAAC,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KACnE;IAED,MAAMK,WAAW,CAACH,KAAa,EAAc;QAC3C,IAAI,IAAI,CAACC,WAAW,EAAE;YACpB,MAAM,IAAI,CAACA,WAAW,CAAC;SACxB;QACD,IAAI;YACF,OAAO,MAAM,IAAI,CAACN,YAAY,CAACK,KAAK,CAAC,CAAC;SACvC,CAAC,OAAOI,KAAK,EAAE;YACd,IAAIA,KAAK,YAAYf,wBAAwB,EAAE;gBAC7C,IAAI,CAACY,WAAW,GAAGG,KAAK,CAAC;aAC1B;YACD,MAAMA,KAAK,CAAC;SACb;KACF;IAED,2BAA2B,CAC3B,MAAMP,oBAAoB,CAACG,KAAa,EAAc;QACpD,MAAM,IAAIK,OAAkB,mBAAA,EAAE,CAAC;KAChC;CACF;QAnCYX,YAAY,GAAZA,YAAY;AAsClB,MAAMY,mBAAmB,SAAkCZ,YAAY;IAC5EH,YAAsBgB,WAAmB,CAAE;QACzC,KAAK,EAAE,CAAC;aADYA,WAAmB,GAAnBA,WAAmB;KAExC;CACF;QAJYD,mBAAmB,GAAnBA,mBAAmB"}