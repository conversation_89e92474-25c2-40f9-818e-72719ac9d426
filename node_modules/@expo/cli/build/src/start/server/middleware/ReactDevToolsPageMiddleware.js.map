{"version": 3, "sources": ["../../../../../src/start/server/middleware/ReactDevToolsPageMiddleware.ts"], "sourcesContent": ["import { readFile } from 'fs/promises';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\n\nexport const ReactDevToolsEndpoint = '/_expo/react-devtools';\n\nexport class ReactDevToolsPageMiddleware extends ExpoMiddleware {\n  constructor(projectRoot: string) {\n    super(projectRoot, [ReactDevToolsEndpoint]);\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    const templatePath =\n      // Production: This will resolve when installed in the project.\n      resolveFrom.silent(this.projectRoot, 'expo/static/react-devtools-page/index.html') ??\n      // Development: This will resolve when testing locally.\n      path.resolve(__dirname, '../../../../../static/react-devtools-page/index.html');\n    const content = (await readFile(templatePath)).toString('utf-8');\n\n    res.setHeader('Content-Type', 'text/html');\n    res.end(content);\n  }\n}\n"], "names": ["ReactDevToolsEndpoint", "ReactDevToolsPageMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "handleRequestAsync", "req", "res", "resolveFrom", "templatePath", "silent", "path", "resolve", "__dirname", "content", "readFile", "toString", "<PERSON><PERSON><PERSON><PERSON>", "end"], "mappings": "AAAA;;;;;AAAyB,IAAA,SAAa,WAAb,aAAa,CAAA;AACrB,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,YAAc,kCAAd,cAAc,EAAA;AAEP,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;;;;;;AAG1C,MAAMA,qBAAqB,GAAG,uBAAuB,AAAC;QAAhDA,qBAAqB,GAArBA,qBAAqB;AAE3B,MAAMC,2BAA2B,SAASC,eAAc,eAAA;IAC7DC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,WAAW,EAAE;YAACJ,qBAAqB;SAAC,CAAC,CAAC;KAC7C;IAED,MAAMK,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;YAE7E,+DAA+D;QAC/DC,GAAkF;QAFpF,MAAMC,YAAY,GAEhBD,CAAAA,GAAkF,GAAlFA,YAAW,QAAA,CAACE,MAAM,CAAC,IAAI,CAACN,WAAW,EAAE,4CAA4C,CAAC,YAAlFI,GAAkF,GAClF,uDAAuD;QACvDG,KAAI,QAAA,CAACC,OAAO,CAACC,SAAS,EAAE,sDAAsD,CAAC,AAAC;QAClF,MAAMC,OAAO,GAAG,CAAC,MAAMC,CAAAA,GAAAA,SAAQ,AAAc,CAAA,SAAd,CAACN,YAAY,CAAC,CAAC,CAACO,QAAQ,CAAC,OAAO,CAAC,AAAC;QAEjET,GAAG,CAACU,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC3CV,GAAG,CAACW,GAAG,CAACJ,OAAO,CAAC,CAAC;KAClB;CACF;QAhBYb,2BAA2B,GAA3BA,2BAA2B"}