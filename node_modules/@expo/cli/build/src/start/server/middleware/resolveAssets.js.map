{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveAssets.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { BundleAssetWithFileHashes } from '@expo/dev-server';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { getAssetSchemasAsync } from '../../../api/getExpoSchema';\nimport * as Log from '../../../log';\nimport { fileExistsAsync } from '../../../utils/dir';\nimport { CommandError } from '../../../utils/errors';\nimport { get, set } from '../../../utils/obj';\nimport { validateUrl } from '../../../utils/url';\n\ntype ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\ntype ManifestResolutionError = Error & {\n  localAssetPath?: string;\n  manifestField?: string;\n};\n\n/** Inline the contents of each platform's `googleServicesFile` so runtimes can access them. */\nexport async function resolveGoogleServicesFile(\n  projectRoot: string,\n  manifest: Pick<ExpoConfig, 'android' | 'ios'>\n) {\n  if (manifest.android?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.android.googleServicesFile),\n        'utf8'\n      );\n      manifest.android.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: android.googleServicesFile: \"${manifest.android.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.android.googleServicesFile;\n    }\n  }\n  if (manifest.ios?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.ios.googleServicesFile),\n        'base64'\n      );\n      manifest.ios.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: ios.googleServicesFile: \"${manifest.ios.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.ios.googleServicesFile;\n    }\n  }\n  return manifest;\n}\n\n/**\n * Get all fields in the manifest that match assets, then filter the ones that aren't set.\n *\n * @param manifest\n * @returns Asset fields that the user has set like [\"icon\", \"splash.image\", ...]\n */\nexport async function getAssetFieldPathsForManifestAsync(manifest: ExpoConfig): Promise<string[]> {\n  // String array like [\"icon\", \"notification.icon\", \"loading.icon\", \"loading.backgroundImage\", \"ios.icon\", ...]\n  const sdkAssetFieldPaths = await getAssetSchemasAsync(manifest.sdkVersion);\n  return sdkAssetFieldPaths.filter((assetSchema) => get(manifest, assetSchema));\n}\n\n/** Resolve all assets in the app.json inline. */\nexport async function resolveManifestAssets(\n  projectRoot: string,\n  {\n    manifest,\n    resolver,\n    strict,\n  }: {\n    manifest: ExpoConfig;\n    resolver: (assetPath: string) => Promise<string>;\n    strict?: boolean;\n  }\n) {\n  try {\n    // Asset fields that the user has set like [\"icon\", \"splash.image\"]\n    const assetSchemas = await getAssetFieldPathsForManifestAsync(manifest);\n    // Get the URLs\n    const urls = await Promise.all(\n      assetSchemas.map(async (manifestField) => {\n        const pathOrURL = get(manifest, manifestField);\n        // URL\n        if (validateUrl(pathOrURL, { requireProtocol: true })) {\n          return pathOrURL;\n        }\n\n        // File path\n        if (await fileExistsAsync(path.resolve(projectRoot, pathOrURL))) {\n          return await resolver(pathOrURL);\n        }\n\n        // Unknown\n        const err: ManifestResolutionError = new CommandError(\n          'MANIFEST_ASSET',\n          'Could not resolve local asset: ' + pathOrURL\n        );\n        err.localAssetPath = pathOrURL;\n        err.manifestField = manifestField;\n        throw err;\n      })\n    );\n\n    // Set the corresponding URL fields\n    assetSchemas.forEach((manifestField, index: number) =>\n      set(manifest, `${manifestField}Url`, urls[index])\n    );\n  } catch (error: any) {\n    if (error.localAssetPath) {\n      Log.warn(\n        `Unable to resolve asset \"${error.localAssetPath}\" from \"${error.manifestField}\" in your app.json or app.config.js`\n      );\n    } else {\n      Log.warn(\n        `Warning: Unable to resolve manifest assets. Icons and fonts might not work. ${error.message}.`\n      );\n    }\n\n    if (strict) {\n      throw new CommandError(\n        'MANIFEST_ASSET',\n        'Failed to export manifest assets: ' + error.message\n      );\n    }\n  }\n}\n"], "names": ["resolveGoogleServicesFile", "getAssetFieldPathsForManifestAsync", "resolveManifestAssets", "Log", "projectRoot", "manifest", "android", "googleServicesFile", "contents", "fs", "readFile", "path", "resolve", "warn", "ios", "sdkAssetFieldPaths", "getAssetSchemasAsync", "sdkVersion", "filter", "assetSchema", "get", "resolver", "strict", "assetSchemas", "urls", "Promise", "all", "map", "manifestField", "pathOrURL", "validateUrl", "requireProtocol", "fileExistsAsync", "err", "CommandError", "localAssetPath", "for<PERSON>ach", "index", "set", "error", "message"], "mappings": "AAAA;;;;QAsBsBA,yBAAyB,GAAzBA,yBAAyB;QA2CzBC,kCAAkC,GAAlCA,kCAAkC;QAOlCC,qBAAqB,GAArBA,qBAAqB;AAtE5B,IAAA,SAAa,kCAAb,aAAa,EAAA;AACX,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEc,IAAA,cAA4B,WAA5B,4BAA4B,CAAA;AACrDC,IAAAA,GAAG,mCAAM,cAAc,EAApB;AACiB,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACvB,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;AAC3B,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACjB,IAAA,IAAoB,WAApB,oBAAoB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC,eAAeH,yBAAyB,CAC7CI,WAAmB,EACnBC,QAA6C,EAC7C;QACIA,GAAgB,EAehBA,IAAY;IAfhB,IAAIA,CAAAA,GAAgB,GAAhBA,QAAQ,CAACC,OAAO,SAAoB,GAApCD,KAAAA,CAAoC,GAApCA,GAAgB,CAAEE,kBAAkB,EAAE;QACxC,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMC,SAAE,QAAA,CAACC,QAAQ,CAChCC,KAAI,QAAA,CAACC,OAAO,CAACR,WAAW,EAAEC,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC,EAC9D,MAAM,CACP,AAAC;YACFF,QAAQ,CAACC,OAAO,CAACC,kBAAkB,GAAGC,QAAQ,CAAC;SAChD,CAAC,OAAM;YACNL,GAAG,CAACU,IAAI,CACN,CAAC,0DAA0D,EAAER,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC,CAAC,CACpG,CAAC;YACF,0DAA0D;YAC1D,OAAOF,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC;SAC5C;KACF;IACD,IAAIF,CAAAA,IAAY,GAAZA,QAAQ,CAACS,GAAG,SAAoB,GAAhCT,KAAAA,CAAgC,GAAhCA,IAAY,CAAEE,kBAAkB,EAAE;QACpC,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMC,SAAE,QAAA,CAACC,QAAQ,CAChCC,KAAI,QAAA,CAACC,OAAO,CAACR,WAAW,EAAEC,QAAQ,CAACS,GAAG,CAACP,kBAAkB,CAAC,EAC1D,QAAQ,CACT,AAAC;YACFF,QAAQ,CAACS,GAAG,CAACP,kBAAkB,GAAGC,QAAQ,CAAC;SAC5C,CAAC,OAAM;YACNL,GAAG,CAACU,IAAI,CACN,CAAC,sDAAsD,EAAER,QAAQ,CAACS,GAAG,CAACP,kBAAkB,CAAC,CAAC,CAAC,CAC5F,CAAC;YACF,0DAA0D;YAC1D,OAAOF,QAAQ,CAACS,GAAG,CAACP,kBAAkB,CAAC;SACxC;KACF;IACD,OAAOF,QAAQ,CAAC;CACjB;AAQM,eAAeJ,kCAAkC,CAACI,QAAoB,EAAqB;IAChG,8GAA8G;IAC9G,MAAMU,kBAAkB,GAAG,MAAMC,CAAAA,GAAAA,cAAoB,AAAqB,CAAA,qBAArB,CAACX,QAAQ,CAACY,UAAU,CAAC,AAAC;IAC3E,OAAOF,kBAAkB,CAACG,MAAM,CAAC,CAACC,WAAW,GAAKC,CAAAA,GAAAA,IAAG,AAAuB,CAAA,IAAvB,CAACf,QAAQ,EAAEc,WAAW,CAAC;IAAA,CAAC,CAAC;CAC/E;AAGM,eAAejB,qBAAqB,CACzCE,WAAmB,EACnB,EACEC,QAAQ,CAAA,EACRgB,QAAQ,CAAA,EACRC,MAAM,CAAA,EAKP,EACD;IACA,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,GAAG,MAAMtB,kCAAkC,CAACI,QAAQ,CAAC,AAAC;QACxE,eAAe;QACf,MAAMmB,IAAI,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC5BH,YAAY,CAACI,GAAG,CAAC,OAAOC,aAAa,GAAK;YACxC,MAAMC,SAAS,GAAGT,CAAAA,GAAAA,IAAG,AAAyB,CAAA,IAAzB,CAACf,QAAQ,EAAEuB,aAAa,CAAC,AAAC;YAC/C,MAAM;YACN,IAAIE,CAAAA,GAAAA,IAAW,AAAsC,CAAA,YAAtC,CAACD,SAAS,EAAE;gBAAEE,eAAe,EAAE,IAAI;aAAE,CAAC,EAAE;gBACrD,OAAOF,SAAS,CAAC;aAClB;YAED,YAAY;YACZ,IAAI,MAAMG,CAAAA,GAAAA,IAAe,AAAsC,CAAA,gBAAtC,CAACrB,KAAI,QAAA,CAACC,OAAO,CAACR,WAAW,EAAEyB,SAAS,CAAC,CAAC,EAAE;gBAC/D,OAAO,MAAMR,QAAQ,CAACQ,SAAS,CAAC,CAAC;aAClC;YAED,UAAU;YACV,MAAMI,GAAG,GAA4B,IAAIC,OAAY,aAAA,CACnD,gBAAgB,EAChB,iCAAiC,GAAGL,SAAS,CAC9C,AAAC;YACFI,GAAG,CAACE,cAAc,GAAGN,SAAS,CAAC;YAC/BI,GAAG,CAACL,aAAa,GAAGA,aAAa,CAAC;YAClC,MAAMK,GAAG,CAAC;SACX,CAAC,CACH,AAAC;QAEF,mCAAmC;QACnCV,YAAY,CAACa,OAAO,CAAC,CAACR,aAAa,EAAES,KAAa,GAChDC,CAAAA,GAAAA,IAAG,AAA8C,CAAA,IAA9C,CAACjC,QAAQ,EAAE,CAAC,EAAEuB,aAAa,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAACa,KAAK,CAAC,CAAC;QAAA,CAClD,CAAC;KACH,CAAC,OAAOE,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACJ,cAAc,EAAE;YACxBhC,GAAG,CAACU,IAAI,CACN,CAAC,yBAAyB,EAAE0B,KAAK,CAACJ,cAAc,CAAC,QAAQ,EAAEI,KAAK,CAACX,aAAa,CAAC,mCAAmC,CAAC,CACpH,CAAC;SACH,MAAM;YACLzB,GAAG,CAACU,IAAI,CACN,CAAC,4EAA4E,EAAE0B,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAChG,CAAC;SACH;QAED,IAAIlB,MAAM,EAAE;YACV,MAAM,IAAIY,OAAY,aAAA,CACpB,gBAAgB,EAChB,oCAAoC,GAAGK,KAAK,CAACC,OAAO,CACrD,CAAC;SACH;KACF;CACF"}