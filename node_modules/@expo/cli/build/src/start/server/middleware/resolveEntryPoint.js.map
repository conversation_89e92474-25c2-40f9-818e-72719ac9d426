{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveEntryPoint.ts"], "sourcesContent": ["import { ProjectConfig } from '@expo/config';\nimport { getEntryPoint } from '@expo/config/paths';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { CommandError } from '../../../utils/errors';\n\nconst supportedPlatforms = ['ios', 'android', 'web', 'none'];\n\n/** @returns the relative entry file for the project.  */\nexport function resolveEntryPoint(\n  projectRoot: string,\n  platform?: string,\n  projectConfig?: ProjectConfig\n): string {\n  return path.relative(\n    projectRoot,\n    resolveAbsoluteEntryPoint(projectRoot, platform, projectConfig)\n  );\n}\n\n/** @returns the absolute entry file for the project.  */\nexport function resolveAbsoluteEntryPoint(\n  projectRoot: string,\n  platform?: string,\n  projectConfig?: ProjectConfig\n): string {\n  if (platform && !supportedPlatforms.includes(platform)) {\n    throw new CommandError(\n      `Failed to resolve the project's entry file: The platform \"${platform}\" is not supported.`\n    );\n  }\n  // TODO(Bacon): support platform extension resolution like .ios, .native\n  // const platforms = [platform, 'native'].filter(Boolean) as string[];\n  const platforms: string[] = [];\n\n  const entry = getEntryPoint(projectRoot, ['./index'], platforms, projectConfig);\n  if (!entry) {\n    // NOTE(Bacon): I purposefully don't mention all possible resolutions here since the package.json is the most standard and users should opt towards that.\n    throw new CommandError(\n      chalk`The project entry file could not be resolved. Please define it in the {bold package.json} \"main\" field.`\n    );\n  }\n\n  return entry;\n}\n"], "names": ["resolveEntryPoint", "resolveAbsoluteEntryPoint", "supportedPlatforms", "projectRoot", "platform", "projectConfig", "path", "relative", "includes", "CommandError", "platforms", "entry", "getEntryPoint", "chalk"], "mappings": "AAAA;;;;QAUgBA,iBAAiB,GAAjBA,iBAAiB;QAYjBC,yBAAyB,GAAzBA,yBAAyB;AArBX,IAAA,MAAoB,WAApB,oBAAoB,CAAA;AAChC,IAAA,MAAO,kCAAP,OAAO,EAAA;AACR,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEM,IAAA,OAAuB,WAAvB,uBAAuB,CAAA;;;;;;AAEpD,MAAMC,kBAAkB,GAAG;IAAC,KAAK;IAAE,SAAS;IAAE,KAAK;IAAE,MAAM;CAAC,AAAC;AAGtD,SAASF,iBAAiB,CAC/BG,WAAmB,EACnBC,QAAiB,EACjBC,aAA6B,EACrB;IACR,OAAOC,KAAI,QAAA,CAACC,QAAQ,CAClBJ,WAAW,EACXF,yBAAyB,CAACE,WAAW,EAAEC,QAAQ,EAAEC,aAAa,CAAC,CAChE,CAAC;CACH;AAGM,SAASJ,yBAAyB,CACvCE,WAAmB,EACnBC,QAAiB,EACjBC,aAA6B,EACrB;IACR,IAAID,QAAQ,IAAI,CAACF,kBAAkB,CAACM,QAAQ,CAACJ,QAAQ,CAAC,EAAE;QACtD,MAAM,IAAIK,OAAY,aAAA,CACpB,CAAC,0DAA0D,EAAEL,QAAQ,CAAC,mBAAmB,CAAC,CAC3F,CAAC;KACH;IACD,wEAAwE;IACxE,sEAAsE;IACtE,MAAMM,SAAS,GAAa,EAAE,AAAC;IAE/B,MAAMC,KAAK,GAAGC,CAAAA,GAAAA,MAAa,AAAoD,CAAA,cAApD,CAACT,WAAW,EAAE;QAAC,SAAS;KAAC,EAAEO,SAAS,EAAEL,aAAa,CAAC,AAAC;IAChF,IAAI,CAACM,KAAK,EAAE;QACV,yJAAyJ;QACzJ,MAAM,IAAIF,OAAY,aAAA,CACpBI,MAAK,QAAA,CAAC,uGAAuG,CAAC,CAC/G,CAAC;KACH;IAED,OAAOF,KAAK,CAAC;CACd"}