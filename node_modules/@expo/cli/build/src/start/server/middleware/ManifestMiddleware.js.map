{"version": 3, "sources": ["../../../../../src/start/server/middleware/ManifestMiddleware.ts"], "sourcesContent": ["import { ExpoConfig, ExpoGoConfig, getConfig, ProjectConfig } from '@expo/config';\nimport findWorkspaceRoot from 'find-yarn-workspace-root';\nimport path from 'path';\nimport { resolve } from 'url';\n\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { stripExtension } from '../../../utils/url';\nimport * as ProjectDevices from '../../project/devices';\nimport { UrlCreator } from '../UrlCreator';\nimport { getPlatformBundlers } from '../platformBundlers';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../webTemplate';\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { resolveGoogleServicesFile, resolveManifestAssets } from './resolveAssets';\nimport { resolveAbsoluteEntryPoint } from './resolveEntryPoint';\nimport { parsePlatformHeader, RuntimePlatform } from './resolvePlatform';\nimport { ServerHeaders, ServerNext, ServerRequest, ServerResponse } from './server.types';\n\nconst debug = require('debug')('expo:start:server:middleware:manifest') as typeof console.log;\n\n/** Wraps `findWorkspaceRoot` and guards against having an empty `package.json` file in an upper directory. */\nexport function getWorkspaceRoot(projectRoot: string): string | null {\n  try {\n    return findWorkspaceRoot(projectRoot);\n  } catch (error: any) {\n    if (error.message.includes('Unexpected end of JSON input')) {\n      return null;\n    }\n    throw error;\n  }\n}\n\nexport function getEntryWithServerRoot(\n  projectRoot: string,\n  projectConfig: ProjectConfig,\n  platform: string\n) {\n  return path.relative(\n    getMetroServerRoot(projectRoot),\n    resolveAbsoluteEntryPoint(projectRoot, platform, projectConfig)\n  );\n}\n\nexport function getMetroServerRoot(projectRoot: string) {\n  if (env.EXPO_USE_METRO_WORKSPACE_ROOT) {\n    return getWorkspaceRoot(projectRoot) ?? projectRoot;\n  }\n\n  return projectRoot;\n}\n\n/** Get the main entry module ID (file) relative to the project root. */\nexport function resolveMainModuleName(\n  projectRoot: string,\n  projectConfig: ProjectConfig,\n  platform: string\n): string {\n  const entryPoint = getEntryWithServerRoot(projectRoot, projectConfig, platform);\n\n  debug(`Resolved entry point: ${entryPoint} (project root: ${projectRoot})`);\n\n  return stripExtension(entryPoint, 'js');\n}\n\nexport function createBundleUrlPath({\n  platform,\n  mainModuleName,\n  mode,\n  minify = mode === 'production',\n  environment,\n  serializerOutput,\n}: {\n  platform: string;\n  mainModuleName: string;\n  mode: string;\n  minify?: boolean;\n  environment?: string;\n  serializerOutput?: 'static';\n}): string {\n  const queryParams = new URLSearchParams({\n    platform: encodeURIComponent(platform),\n    dev: String(mode !== 'production'),\n    // TODO: Is this still needed?\n    hot: String(false),\n    lazy: String(!env.EXPO_NO_METRO_LAZY),\n  });\n\n  if (minify) {\n    queryParams.append('minify', String(minify));\n  }\n  if (environment) {\n    queryParams.append('resolver.environment', environment);\n    queryParams.append('transform.environment', environment);\n  }\n  if (serializerOutput) {\n    queryParams.append('serializer.output', serializerOutput);\n  }\n\n  return `/${encodeURI(mainModuleName)}.bundle?${queryParams.toString()}`;\n}\n\n/** Info about the computer hosting the dev server. */\nexport interface HostInfo {\n  host: string;\n  server: 'expo';\n  serverVersion: string;\n  serverDriver: string | null;\n  serverOS: NodeJS.Platform;\n  serverOSVersion: string;\n}\n\n/** Parsed values from the supported request headers. */\nexport interface ManifestRequestInfo {\n  /** Platform to serve. */\n  platform: RuntimePlatform;\n  /** Requested host name. */\n  hostname?: string | null;\n}\n\n/** Project related info. */\nexport type ResponseProjectSettings = {\n  expoGoConfig: ExpoGoConfig;\n  hostUri: string;\n  bundleUrl: string;\n  exp: ExpoConfig;\n};\n\nexport const DEVELOPER_TOOL = 'expo-cli';\n\nexport type ManifestMiddlewareOptions = {\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n  constructUrl: UrlCreator['constructUrl'];\n  isNativeWebpack?: boolean;\n  privateKeyPath?: string;\n};\n\n/** Base middleware creator for serving the Expo manifest (like the index.html but for native runtimes). */\nexport abstract class ManifestMiddleware<\n  TManifestRequestInfo extends ManifestRequestInfo\n> extends ExpoMiddleware {\n  private initialProjectConfig: ProjectConfig;\n\n  constructor(protected projectRoot: string, protected options: ManifestMiddlewareOptions) {\n    super(\n      projectRoot,\n      /**\n       * Only support `/`, `/manifest`, `/index.exp` for the manifest middleware.\n       */\n      ['/', '/manifest', '/index.exp']\n    );\n    this.initialProjectConfig = getConfig(projectRoot);\n  }\n\n  /** Exposed for testing. */\n  public async _resolveProjectSettingsAsync({\n    platform,\n    hostname,\n  }: Pick<TManifestRequestInfo, 'hostname' | 'platform'>): Promise<ResponseProjectSettings> {\n    // Read the config\n    const projectConfig = getConfig(this.projectRoot);\n\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName(projectConfig, platform);\n\n    // Create the manifest and set fields within it\n    const expoGoConfig = this.getExpoGoConfig({\n      mainModuleName,\n      hostname,\n    });\n\n    const hostUri = this.options.constructUrl({ scheme: '', hostname });\n\n    const bundleUrl = this._getBundleUrl({\n      platform,\n      mainModuleName,\n      hostname,\n    });\n\n    // Resolve all assets and set them on the manifest as URLs\n    await this.mutateManifestWithAssetsAsync(projectConfig.exp, bundleUrl);\n\n    return {\n      expoGoConfig,\n      hostUri,\n      bundleUrl,\n      exp: projectConfig.exp,\n    };\n  }\n\n  /** Get the main entry module ID (file) relative to the project root. */\n  private resolveMainModuleName(projectConfig: ProjectConfig, platform: string): string {\n    let entryPoint = getEntryWithServerRoot(this.projectRoot, projectConfig, platform);\n\n    debug(`Resolved entry point: ${entryPoint} (project root: ${this.projectRoot})`);\n\n    // NOTE(Bacon): Webpack is currently hardcoded to index.bundle on native\n    // in the future (TODO) we should move this logic into a Webpack plugin and use\n    // a generated file name like we do on web.\n    // const server = getDefaultDevServer();\n    // // TODO: Move this into BundlerDevServer and read this info from self.\n    // const isNativeWebpack = server instanceof WebpackBundlerDevServer && server.isTargetingNative();\n    if (this.options.isNativeWebpack) {\n      entryPoint = 'index.js';\n    }\n\n    return stripExtension(entryPoint, 'js');\n  }\n\n  /** Parse request headers into options. */\n  public abstract getParsedHeaders(req: ServerRequest): TManifestRequestInfo;\n\n  /** Store device IDs that were sent in the request headers. */\n  private async saveDevicesAsync(req: ServerRequest) {\n    const deviceIds = req.headers?.['expo-dev-client-id'];\n    if (deviceIds) {\n      await ProjectDevices.saveDevicesAsync(this.projectRoot, deviceIds).catch((e) =>\n        Log.exception(e)\n      );\n    }\n  }\n\n  /** Create the bundle URL (points to the single JS entry file). Exposed for testing. */\n  public _getBundleUrl({\n    platform,\n    mainModuleName,\n    hostname,\n  }: {\n    platform: string;\n    hostname?: string | null;\n    mainModuleName: string;\n  }): string {\n    const path = createBundleUrlPath({\n      mode: this.options.mode ?? 'development',\n      minify: this.options.minify,\n      platform,\n      mainModuleName,\n    });\n\n    return (\n      this.options.constructUrl({\n        scheme: 'http',\n        // hostType: this.options.location.hostType,\n        hostname,\n      }) + path\n    );\n  }\n\n  public _getBundleUrlPath({\n    platform,\n    mainModuleName,\n  }: {\n    platform: string;\n    mainModuleName: string;\n  }): string {\n    const queryParams = new URLSearchParams({\n      platform: encodeURIComponent(platform),\n      dev: String(this.options.mode !== 'production'),\n      // TODO: Is this still needed?\n      hot: String(false),\n      lazy: String(!env.EXPO_NO_METRO_LAZY),\n    });\n\n    if (this.options.minify) {\n      queryParams.append('minify', String(this.options.minify));\n    }\n\n    return `/${encodeURI(mainModuleName)}.bundle?${queryParams.toString()}`;\n  }\n\n  /** Log telemetry. */\n  protected abstract trackManifest(version?: string): void;\n\n  /** Get the manifest response to return to the runtime. This file contains info regarding where the assets can be loaded from. Exposed for testing. */\n  public abstract _getManifestResponseAsync(options: TManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }>;\n\n  private getExpoGoConfig({\n    mainModuleName,\n    hostname,\n  }: {\n    mainModuleName: string;\n    hostname?: string | null;\n  }): ExpoGoConfig {\n    return {\n      // localhost:8081\n      debuggerHost: this.options.constructUrl({ scheme: '', hostname }),\n      // http://localhost:8081/logs -- used to send logs to the CLI for displaying in the terminal.\n      // This is deprecated in favor of the WebSocket connection setup in Metro.\n      logUrl: this.options.constructUrl({ scheme: 'http', hostname }) + '/logs',\n      // Required for Expo Go to function.\n      developer: {\n        tool: DEVELOPER_TOOL,\n        projectRoot: this.projectRoot,\n      },\n      packagerOpts: {\n        // Required for dev client.\n        dev: this.options.mode !== 'production',\n      },\n      // Indicates the name of the main bundle.\n      mainModuleName,\n      // Add this string to make Flipper register React Native / Metro as \"running\".\n      // Can be tested by running:\n      // `METRO_SERVER_PORT=8081 open -a flipper.app`\n      // Where 8081 is the port where the Expo project is being hosted.\n      __flipperHack: 'React Native packager is running',\n    };\n  }\n\n  /** Resolve all assets and set them on the manifest as URLs */\n  private async mutateManifestWithAssetsAsync(manifest: ExpoConfig, bundleUrl: string) {\n    await resolveManifestAssets(this.projectRoot, {\n      manifest,\n      resolver: async (path) => {\n        if (this.options.isNativeWebpack) {\n          // When using our custom dev server, just do assets normally\n          // without the `assets/` subpath redirect.\n          return resolve(bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0], path);\n        }\n        return bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0] + 'assets/' + path;\n      },\n    });\n    // The server normally inserts this but if we're offline we'll do it here\n    await resolveGoogleServicesFile(this.projectRoot, manifest);\n  }\n\n  public getWebBundleUrl() {\n    const platform = 'web';\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName(this.initialProjectConfig, platform);\n    return this._getBundleUrlPath({\n      platform,\n      mainModuleName,\n    });\n  }\n\n  /**\n   * Web platforms should create an index.html response using the same script resolution as native.\n   *\n   * Instead of adding a `bundleUrl` to a `manifest.json` (native) we'll add a `<script src=\"\">`\n   * to an `index.html`, this enables the web platform to load JavaScript from the server.\n   */\n  private async handleWebRequestAsync(req: ServerRequest, res: ServerResponse) {\n    // Read from headers\n    const bundleUrl = this.getWebBundleUrl();\n\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(\n      await createTemplateHtmlFromExpoConfigAsync(this.projectRoot, {\n        exp: this.initialProjectConfig.exp,\n        scripts: [bundleUrl],\n      })\n    );\n  }\n\n  /** Exposed for testing. */\n  async checkBrowserRequestAsync(req: ServerRequest, res: ServerResponse, next: ServerNext) {\n    // Read the config\n    const bundlers = getPlatformBundlers(this.initialProjectConfig.exp);\n    if (bundlers.web === 'metro') {\n      // NOTE(EvanBacon): This effectively disables the safety check we do on custom runtimes to ensure\n      // the `expo-platform` header is included. When `web.bundler=web`, if the user has non-standard Expo\n      // code loading then they'll get a web bundle without a clear assertion of platform support.\n      const platform = parsePlatformHeader(req);\n      // On web, serve the public folder\n      if (!platform || platform === 'web') {\n        if (this.initialProjectConfig.exp.web?.output === 'static') {\n          // Skip the spa-styled index.html when static generation is enabled.\n          next();\n          return true;\n        } else {\n          await this.handleWebRequestAsync(req, res);\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    // First check for standard JavaScript runtimes (aka legacy browsers like Chrome).\n    if (await this.checkBrowserRequestAsync(req, res, next)) {\n      return;\n    }\n\n    // Save device IDs for dev client.\n    await this.saveDevicesAsync(req);\n\n    // Read from headers\n    const options = this.getParsedHeaders(req);\n    const { body, version, headers } = await this._getManifestResponseAsync(options);\n    for (const [headerName, headerValue] of headers) {\n      res.setHeader(headerName, headerValue);\n    }\n    res.end(body);\n\n    // Log analytics\n    this.trackManifest(version ?? null);\n  }\n}\n"], "names": ["getWorkspaceRoot", "getEntryWithServerRoot", "getMetroServerRoot", "resolveMainModuleName", "createBundleUrlPath", "Log", "ProjectDevices", "debug", "require", "projectRoot", "findWorkspaceRoot", "error", "message", "includes", "projectConfig", "platform", "path", "relative", "resolveAbsoluteEntryPoint", "env", "EXPO_USE_METRO_WORKSPACE_ROOT", "entryPoint", "stripExtension", "mainModuleName", "mode", "minify", "environment", "serializerOutput", "queryParams", "URLSearchParams", "encodeURIComponent", "dev", "String", "hot", "lazy", "EXPO_NO_METRO_LAZY", "append", "encodeURI", "toString", "DEVELOPER_TOOL", "ManifestMiddleware", "ExpoMiddleware", "constructor", "options", "initialProjectConfig", "getConfig", "_resolveProjectSettingsAsync", "hostname", "expoGoConfig", "getExpoGoConfig", "hostUri", "constructUrl", "scheme", "bundleUrl", "_getBundleUrl", "mutateManifestWithAssetsAsync", "exp", "isNativeWebpack", "saveDevicesAsync", "req", "deviceIds", "headers", "catch", "e", "exception", "_getBundleUrlPath", "debuggerHost", "logUrl", "developer", "tool", "packagerOpts", "__flipperHack", "manifest", "resolveManifestAssets", "resolver", "resolve", "match", "resolveGoogleServicesFile", "getWebBundleUrl", "handleWebRequestAsync", "res", "<PERSON><PERSON><PERSON><PERSON>", "end", "createTemplateHtmlFromExpoConfigAsync", "scripts", "checkBrowserRequestAsync", "next", "bundlers", "getPlatformBundlers", "web", "parsePlatformHeader", "output", "handleRequestAsync", "getParsedHeaders", "body", "version", "_getManifestResponseAsync", "headerName", "headerValue", "trackManifest"], "mappings": "AAAA;;;;QAqBgBA,gBAAgB,GAAhBA,gBAAgB;QAWhBC,sBAAsB,GAAtBA,sBAAsB;QAWtBC,kBAAkB,GAAlBA,kBAAkB;QASlBC,qBAAqB,GAArBA,qBAAqB;QAYrBC,mBAAmB,GAAnBA,mBAAmB;;AAhEgC,IAAA,OAAc,WAAd,cAAc,CAAA;AACnD,IAAA,sBAA0B,kCAA1B,0BAA0B,EAAA;AACvC,IAAA,KAAM,kCAAN,MAAM,EAAA;AACC,IAAA,IAAK,WAAL,KAAK,CAAA;AAEjBC,IAAAA,GAAG,mCAAM,cAAc,EAApB;AACK,IAAA,IAAoB,WAApB,oBAAoB,CAAA;AACT,IAAA,KAAoB,WAApB,oBAAoB,CAAA;AACvCC,IAAAA,cAAc,mCAAM,uBAAuB,EAA7B;AAEU,IAAA,iBAAqB,WAArB,qBAAqB,CAAA;AACH,IAAA,YAAgB,WAAhB,gBAAgB,CAAA;AACvC,IAAA,eAAkB,WAAlB,kBAAkB,CAAA;AACgB,IAAA,cAAiB,WAAjB,iBAAiB,CAAA;AACxC,IAAA,kBAAqB,WAArB,qBAAqB,CAAA;AACV,IAAA,gBAAmB,WAAnB,mBAAmB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGxE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,uCAAuC,CAAC,AAAsB,AAAC;AAGvF,SAASR,gBAAgB,CAACS,WAAmB,EAAiB;IACnE,IAAI;QACF,OAAOC,CAAAA,GAAAA,sBAAiB,AAAa,CAAA,QAAb,CAACD,WAAW,CAAC,CAAC;KACvC,CAAC,OAAOE,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACb;QACD,MAAMF,KAAK,CAAC;KACb;CACF;AAEM,SAASV,sBAAsB,CACpCQ,WAAmB,EACnBK,aAA4B,EAC5BC,QAAgB,EAChB;IACA,OAAOC,KAAI,QAAA,CAACC,QAAQ,CAClBf,kBAAkB,CAACO,WAAW,CAAC,EAC/BS,CAAAA,GAAAA,kBAAyB,AAAsC,CAAA,0BAAtC,CAACT,WAAW,EAAEM,QAAQ,EAAED,aAAa,CAAC,CAChE,CAAC;CACH;AAEM,SAASZ,kBAAkB,CAACO,WAAmB,EAAE;IACtD,IAAIU,IAAG,IAAA,CAACC,6BAA6B,EAAE;YAC9BpB,GAA6B;QAApC,OAAOA,CAAAA,GAA6B,GAA7BA,gBAAgB,CAACS,WAAW,CAAC,YAA7BT,GAA6B,GAAIS,WAAW,CAAC;KACrD;IAED,OAAOA,WAAW,CAAC;CACpB;AAGM,SAASN,qBAAqB,CACnCM,WAAmB,EACnBK,aAA4B,EAC5BC,QAAgB,EACR;IACR,MAAMM,UAAU,GAAGpB,sBAAsB,CAACQ,WAAW,EAAEK,aAAa,EAAEC,QAAQ,CAAC,AAAC;IAEhFR,KAAK,CAAC,CAAC,sBAAsB,EAAEc,UAAU,CAAC,gBAAgB,EAAEZ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5E,OAAOa,CAAAA,GAAAA,KAAc,AAAkB,CAAA,eAAlB,CAACD,UAAU,EAAE,IAAI,CAAC,CAAC;CACzC;AAEM,SAASjB,mBAAmB,CAAC,EAClCW,QAAQ,CAAA,EACRQ,cAAc,CAAA,EACdC,IAAI,CAAA,EACJC,MAAM,EAAGD,IAAI,KAAK,YAAY,CAAA,EAC9BE,WAAW,CAAA,EACXC,gBAAgB,CAAA,EAQjB,EAAU;IACT,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC;QACtCd,QAAQ,EAAEe,kBAAkB,CAACf,QAAQ,CAAC;QACtCgB,GAAG,EAAEC,MAAM,CAACR,IAAI,KAAK,YAAY,CAAC;QAClC,8BAA8B;QAC9BS,GAAG,EAAED,MAAM,CAAC,KAAK,CAAC;QAClBE,IAAI,EAAEF,MAAM,CAAC,CAACb,IAAG,IAAA,CAACgB,kBAAkB,CAAC;KACtC,CAAC,AAAC;IAEH,IAAIV,MAAM,EAAE;QACVG,WAAW,CAACQ,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACP,MAAM,CAAC,CAAC,CAAC;KAC9C;IACD,IAAIC,WAAW,EAAE;QACfE,WAAW,CAACQ,MAAM,CAAC,sBAAsB,EAAEV,WAAW,CAAC,CAAC;QACxDE,WAAW,CAACQ,MAAM,CAAC,uBAAuB,EAAEV,WAAW,CAAC,CAAC;KAC1D;IACD,IAAIC,gBAAgB,EAAE;QACpBC,WAAW,CAACQ,MAAM,CAAC,mBAAmB,EAAET,gBAAgB,CAAC,CAAC;KAC3D;IAED,OAAO,CAAC,CAAC,EAAEU,SAAS,CAACd,cAAc,CAAC,CAAC,QAAQ,EAAEK,WAAW,CAACU,QAAQ,EAAE,CAAC,CAAC,CAAC;CACzE;AA4BM,MAAMC,cAAc,GAAG,UAAU,AAAC;QAA5BA,cAAc,GAAdA,cAAc;AAapB,MAAeC,kBAAkB,SAE9BC,eAAc,eAAA;IAGtBC,YAAsBjC,WAAmB,EAAYkC,OAAkC,CAAE;QACvF,KAAK,CACHlC,WAAW,EACX;;SAEG,CACH;YAAC,GAAG;YAAE,WAAW;YAAE,YAAY;SAAC,CACjC,CAAC;aAPkBA,WAAmB,GAAnBA,WAAmB;aAAYkC,OAAkC,GAAlCA,OAAkC;QAQrF,IAAI,CAACC,oBAAoB,GAAGC,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAACpC,WAAW,CAAC,CAAC;KACpD;IAED,2BAA2B,CAC3B,MAAaqC,4BAA4B,CAAC,EACxC/B,QAAQ,CAAA,EACRgC,QAAQ,CAAA,EAC4C,EAAoC;QACxF,kBAAkB;QAClB,MAAMjC,aAAa,GAAG+B,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAACpC,WAAW,CAAC,AAAC;QAElD,oBAAoB;QACpB,MAAMc,cAAc,GAAG,IAAI,CAACpB,qBAAqB,CAACW,aAAa,EAAEC,QAAQ,CAAC,AAAC;QAE3E,+CAA+C;QAC/C,MAAMiC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC;YACxC1B,cAAc;YACdwB,QAAQ;SACT,CAAC,AAAC;QAEH,MAAMG,OAAO,GAAG,IAAI,CAACP,OAAO,CAACQ,YAAY,CAAC;YAAEC,MAAM,EAAE,EAAE;YAAEL,QAAQ;SAAE,CAAC,AAAC;QAEpE,MAAMM,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC;YACnCvC,QAAQ;YACRQ,cAAc;YACdwB,QAAQ;SACT,CAAC,AAAC;QAEH,0DAA0D;QAC1D,MAAM,IAAI,CAACQ,6BAA6B,CAACzC,aAAa,CAAC0C,GAAG,EAAEH,SAAS,CAAC,CAAC;QAEvE,OAAO;YACLL,YAAY;YACZE,OAAO;YACPG,SAAS;YACTG,GAAG,EAAE1C,aAAa,CAAC0C,GAAG;SACvB,CAAC;KACH;IAED,wEAAwE,CACxE,AAAQrD,qBAAqB,CAACW,aAA4B,EAAEC,QAAgB,EAAU;QACpF,IAAIM,UAAU,GAAGpB,sBAAsB,CAAC,IAAI,CAACQ,WAAW,EAAEK,aAAa,EAAEC,QAAQ,CAAC,AAAC;QAEnFR,KAAK,CAAC,CAAC,sBAAsB,EAAEc,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,wEAAwE;QACxE,+EAA+E;QAC/E,2CAA2C;QAC3C,wCAAwC;QACxC,yEAAyE;QACzE,mGAAmG;QACnG,IAAI,IAAI,CAACkC,OAAO,CAACc,eAAe,EAAE;YAChCpC,UAAU,GAAG,UAAU,CAAC;SACzB;QAED,OAAOC,CAAAA,GAAAA,KAAc,AAAkB,CAAA,eAAlB,CAACD,UAAU,EAAE,IAAI,CAAC,CAAC;KACzC;IAKD,8DAA8D,CAC9D,MAAcqC,gBAAgB,CAACC,GAAkB,EAAE;YAC/BA,GAAW;QAA7B,MAAMC,SAAS,GAAGD,CAAAA,GAAW,GAAXA,GAAG,CAACE,OAAO,SAAwB,GAAnCF,KAAAA,CAAmC,GAAnCA,GAAW,AAAE,CAAC,oBAAoB,CAAC,AAAC;QACtD,IAAIC,SAAS,EAAE;YACb,MAAMtD,cAAc,CAACoD,gBAAgB,CAAC,IAAI,CAACjD,WAAW,EAAEmD,SAAS,CAAC,CAACE,KAAK,CAAC,CAACC,CAAC,GACzE1D,GAAG,CAAC2D,SAAS,CAACD,CAAC,CAAC;YAAA,CACjB,CAAC;SACH;KACF;IAED,uFAAuF,CACvF,AAAOT,aAAa,CAAC,EACnBvC,QAAQ,CAAA,EACRQ,cAAc,CAAA,EACdwB,QAAQ,CAAA,EAKT,EAAU;YAED,KAAiB;QADzB,MAAM/B,IAAI,GAAGZ,mBAAmB,CAAC;YAC/BoB,IAAI,EAAE,CAAA,KAAiB,GAAjB,IAAI,CAACmB,OAAO,CAACnB,IAAI,YAAjB,KAAiB,GAAI,aAAa;YACxCC,MAAM,EAAE,IAAI,CAACkB,OAAO,CAAClB,MAAM;YAC3BV,QAAQ;YACRQ,cAAc;SACf,CAAC,AAAC;QAEH,OACE,IAAI,CAACoB,OAAO,CAACQ,YAAY,CAAC;YACxBC,MAAM,EAAE,MAAM;YACd,4CAA4C;YAC5CL,QAAQ;SACT,CAAC,GAAG/B,IAAI,CACT;KACH;IAED,AAAOiD,iBAAiB,CAAC,EACvBlD,QAAQ,CAAA,EACRQ,cAAc,CAAA,EAIf,EAAU;QACT,MAAMK,WAAW,GAAG,IAAIC,eAAe,CAAC;YACtCd,QAAQ,EAAEe,kBAAkB,CAACf,QAAQ,CAAC;YACtCgB,GAAG,EAAEC,MAAM,CAAC,IAAI,CAACW,OAAO,CAACnB,IAAI,KAAK,YAAY,CAAC;YAC/C,8BAA8B;YAC9BS,GAAG,EAAED,MAAM,CAAC,KAAK,CAAC;YAClBE,IAAI,EAAEF,MAAM,CAAC,CAACb,IAAG,IAAA,CAACgB,kBAAkB,CAAC;SACtC,CAAC,AAAC;QAEH,IAAI,IAAI,CAACQ,OAAO,CAAClB,MAAM,EAAE;YACvBG,WAAW,CAACQ,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAAC,IAAI,CAACW,OAAO,CAAClB,MAAM,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,CAAC,CAAC,EAAEY,SAAS,CAACd,cAAc,CAAC,CAAC,QAAQ,EAAEK,WAAW,CAACU,QAAQ,EAAE,CAAC,CAAC,CAAC;KACzE;IAYD,AAAQW,eAAe,CAAC,EACtB1B,cAAc,CAAA,EACdwB,QAAQ,CAAA,EAIT,EAAgB;QACf,OAAO;YACL,iBAAiB;YACjBmB,YAAY,EAAE,IAAI,CAACvB,OAAO,CAACQ,YAAY,CAAC;gBAAEC,MAAM,EAAE,EAAE;gBAAEL,QAAQ;aAAE,CAAC;YACjE,6FAA6F;YAC7F,0EAA0E;YAC1EoB,MAAM,EAAE,IAAI,CAACxB,OAAO,CAACQ,YAAY,CAAC;gBAAEC,MAAM,EAAE,MAAM;gBAAEL,QAAQ;aAAE,CAAC,GAAG,OAAO;YACzE,oCAAoC;YACpCqB,SAAS,EAAE;gBACTC,IAAI,EAAE9B,cAAc;gBACpB9B,WAAW,EAAE,IAAI,CAACA,WAAW;aAC9B;YACD6D,YAAY,EAAE;gBACZ,2BAA2B;gBAC3BvC,GAAG,EAAE,IAAI,CAACY,OAAO,CAACnB,IAAI,KAAK,YAAY;aACxC;YACD,yCAAyC;YACzCD,cAAc;YACd,8EAA8E;YAC9E,4BAA4B;YAC5B,+CAA+C;YAC/C,iEAAiE;YACjEgD,aAAa,EAAE,kCAAkC;SAClD,CAAC;KACH;IAED,8DAA8D,CAC9D,MAAchB,6BAA6B,CAACiB,QAAoB,EAAEnB,SAAiB,EAAE;QACnF,MAAMoB,CAAAA,GAAAA,cAAqB,AAUzB,CAAA,sBAVyB,CAAC,IAAI,CAAChE,WAAW,EAAE;YAC5C+D,QAAQ;YACRE,QAAQ,EAAE,OAAO1D,IAAI,GAAK;gBACxB,IAAI,IAAI,CAAC2B,OAAO,CAACc,eAAe,EAAE;oBAChC,4DAA4D;oBAC5D,0CAA0C;oBAC1C,OAAOkB,CAAAA,GAAAA,IAAO,AAAiD,CAAA,QAAjD,CAACtB,SAAS,CAAEuB,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,EAAE5D,IAAI,CAAC,CAAC;iBACjE;gBACD,OAAOqC,SAAS,CAAEuB,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG5D,IAAI,CAAC;aACrE;SACF,CAAC,CAAC;QACH,yEAAyE;QACzE,MAAM6D,CAAAA,GAAAA,cAAyB,AAA4B,CAAA,0BAA5B,CAAC,IAAI,CAACpE,WAAW,EAAE+D,QAAQ,CAAC,CAAC;KAC7D;IAED,AAAOM,eAAe,GAAG;QACvB,MAAM/D,QAAQ,GAAG,KAAK,AAAC;QACvB,oBAAoB;QACpB,MAAMQ,cAAc,GAAG,IAAI,CAACpB,qBAAqB,CAAC,IAAI,CAACyC,oBAAoB,EAAE7B,QAAQ,CAAC,AAAC;QACvF,OAAO,IAAI,CAACkD,iBAAiB,CAAC;YAC5BlD,QAAQ;YACRQ,cAAc;SACf,CAAC,CAAC;KACJ;IAED;;;;;KAKG,CACH,MAAcwD,qBAAqB,CAACpB,GAAkB,EAAEqB,GAAmB,EAAE;QAC3E,oBAAoB;QACpB,MAAM3B,SAAS,GAAG,IAAI,CAACyB,eAAe,EAAE,AAAC;QAEzCE,GAAG,CAACC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE3CD,GAAG,CAACE,GAAG,CACL,MAAMC,CAAAA,GAAAA,YAAqC,AAGzC,CAAA,sCAHyC,CAAC,IAAI,CAAC1E,WAAW,EAAE;YAC5D+C,GAAG,EAAE,IAAI,CAACZ,oBAAoB,CAACY,GAAG;YAClC4B,OAAO,EAAE;gBAAC/B,SAAS;aAAC;SACrB,CAAC,CACH,CAAC;KACH;IAED,2BAA2B,CAC3B,MAAMgC,wBAAwB,CAAC1B,GAAkB,EAAEqB,GAAmB,EAAEM,IAAgB,EAAE;QACxF,kBAAkB;QAClB,MAAMC,QAAQ,GAAGC,CAAAA,GAAAA,iBAAmB,AAA+B,CAAA,oBAA/B,CAAC,IAAI,CAAC5C,oBAAoB,CAACY,GAAG,CAAC,AAAC;QACpE,IAAI+B,QAAQ,CAACE,GAAG,KAAK,OAAO,EAAE;YAC5B,iGAAiG;YACjG,oGAAoG;YACpG,4FAA4F;YAC5F,MAAM1E,QAAQ,GAAG2E,CAAAA,GAAAA,gBAAmB,AAAK,CAAA,oBAAL,CAAC/B,GAAG,CAAC,AAAC;YAC1C,kCAAkC;YAClC,IAAI,CAAC5C,QAAQ,IAAIA,QAAQ,KAAK,KAAK,EAAE;oBAC/B,GAAiC;gBAArC,IAAI,CAAA,CAAA,GAAiC,GAAjC,IAAI,CAAC6B,oBAAoB,CAACY,GAAG,CAACiC,GAAG,SAAQ,GAAzC,KAAA,CAAyC,GAAzC,GAAiC,CAAEE,MAAM,CAAA,KAAK,QAAQ,EAAE;oBAC1D,oEAAoE;oBACpEL,IAAI,EAAE,CAAC;oBACP,OAAO,IAAI,CAAC;iBACb,MAAM;oBACL,MAAM,IAAI,CAACP,qBAAqB,CAACpB,GAAG,EAAEqB,GAAG,CAAC,CAAC;oBAC3C,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QACD,OAAO,KAAK,CAAC;KACd;IAED,MAAMY,kBAAkB,CACtBjC,GAAkB,EAClBqB,GAAmB,EACnBM,IAAgB,EACD;QACf,kFAAkF;QAClF,IAAI,MAAM,IAAI,CAACD,wBAAwB,CAAC1B,GAAG,EAAEqB,GAAG,EAAEM,IAAI,CAAC,EAAE;YACvD,OAAO;SACR;QAED,kCAAkC;QAClC,MAAM,IAAI,CAAC5B,gBAAgB,CAACC,GAAG,CAAC,CAAC;QAEjC,oBAAoB;QACpB,MAAMhB,OAAO,GAAG,IAAI,CAACkD,gBAAgB,CAAClC,GAAG,CAAC,AAAC;QAC3C,MAAM,EAAEmC,IAAI,CAAA,EAAEC,OAAO,CAAA,EAAElC,OAAO,CAAA,EAAE,GAAG,MAAM,IAAI,CAACmC,yBAAyB,CAACrD,OAAO,CAAC,AAAC;QACjF,KAAK,MAAM,CAACsD,UAAU,EAAEC,WAAW,CAAC,IAAIrC,OAAO,CAAE;YAC/CmB,GAAG,CAACC,SAAS,CAACgB,UAAU,EAAEC,WAAW,CAAC,CAAC;SACxC;QACDlB,GAAG,CAACE,GAAG,CAACY,IAAI,CAAC,CAAC;QAEd,gBAAgB;QAChB,IAAI,CAACK,aAAa,CAACJ,OAAO,WAAPA,OAAO,GAAI,IAAI,CAAC,CAAC;KACrC;CACF;QA7QqBvD,kBAAkB,GAAlBA,kBAAkB"}