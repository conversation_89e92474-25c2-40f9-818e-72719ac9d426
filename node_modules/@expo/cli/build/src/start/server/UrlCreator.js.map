{"version": 3, "sources": ["../../../../src/start/server/UrlCreator.ts"], "sourcesContent": ["import assert from 'assert';\nimport { URL } from 'url';\n\nimport * as Log from '../../log';\nimport { getIpAddress } from '../../utils/ip';\n\nconst debug = require('debug')('expo:start:server:urlCreator') as typeof console.log;\n\nexport interface CreateURLOptions {\n  /** URL scheme to use when opening apps in custom runtimes. */\n  scheme?: string | null;\n  /** Type of dev server host to use. */\n  hostType?: 'localhost' | 'lan' | 'tunnel';\n  /** Requested hostname. */\n  hostname?: string | null;\n}\n\ninterface UrlComponents {\n  port: string;\n  hostname: string;\n  protocol: string;\n}\nexport class UrlCreator {\n  constructor(\n    public defaults: CreateURLOptions | undefined,\n    private bundlerInfo: { port: number; getTunnelUrl?: () => string | null }\n  ) {}\n\n  /**\n   * Return a URL for the \"loading\" interstitial page that is used to disambiguate which\n   * native runtime to open the dev server with.\n   *\n   * @param options options for creating the URL\n   * @param platform when opening the URL from the CLI to a connected device we can specify the platform as a query parameter, otherwise it will be inferred from the unsafe user agent sniffing.\n   *\n   * @returns URL like `http://localhost:8081/_expo/loading?platform=ios`\n   * @returns URL like `http://localhost:8081/_expo/loading` when no platform is provided.\n   */\n  public constructLoadingUrl(options: CreateURLOptions, platform: string | null): string {\n    const url = new URL('_expo/loading', this.constructUrl({ scheme: 'http', ...options }));\n    if (platform) {\n      url.search = new URLSearchParams({ platform }).toString();\n    }\n    const loadingUrl = url.toString();\n    debug(`Loading URL: ${loadingUrl}`);\n    return loadingUrl;\n  }\n\n  /** Create a URI for launching in a native dev client. Returns `null` when no `scheme` can be resolved. */\n  public constructDevClientUrl(options?: CreateURLOptions): null | string {\n    const protocol = options?.scheme || this.defaults?.scheme;\n\n    if (\n      !protocol ||\n      // Prohibit the use of http(s) in dev client URIs since they'll never be valid.\n      ['http', 'https'].includes(protocol.toLowerCase())\n    ) {\n      return null;\n    }\n\n    const manifestUrl = this.constructUrl({ ...options, scheme: 'http' });\n    const devClientUrl = `${protocol}://expo-development-client/?url=${encodeURIComponent(\n      manifestUrl\n    )}`;\n    debug(`Dev client URL: ${devClientUrl} -- manifestUrl: ${manifestUrl} -- %O`, options);\n    return devClientUrl;\n  }\n\n  /** Create a generic URL. */\n  public constructUrl(options?: Partial<CreateURLOptions> | null): string {\n    const urlComponents = this.getUrlComponents({\n      ...this.defaults,\n      ...options,\n    });\n    const url = joinUrlComponents(urlComponents);\n    debug(`URL: ${url}`);\n    return url;\n  }\n\n  /** Get the URL components from the Ngrok server URL. */\n  private getTunnelUrlComponents(options: Pick<CreateURLOptions, 'scheme'>): UrlComponents | null {\n    const tunnelUrl = this.bundlerInfo.getTunnelUrl?.();\n    if (!tunnelUrl) {\n      return null;\n    }\n    const parsed = new URL(tunnelUrl);\n    return {\n      port: parsed.port,\n      hostname: parsed.hostname,\n      protocol: options.scheme ?? 'http',\n    };\n  }\n\n  private getUrlComponents(options: CreateURLOptions): UrlComponents {\n    // Proxy comes first.\n    const proxyURL = getProxyUrl();\n    if (proxyURL) {\n      return getUrlComponentsFromProxyUrl(options, proxyURL);\n    }\n\n    // Ngrok.\n    if (options.hostType === 'tunnel') {\n      const components = this.getTunnelUrlComponents(options);\n      if (components) {\n        return components;\n      }\n      Log.warn('Tunnel URL not found (it might not be ready yet), falling back to LAN URL.');\n    } else if (options.hostType === 'localhost' && !options.hostname) {\n      options.hostname = 'localhost';\n    }\n\n    return {\n      hostname: getDefaultHostname(options),\n      port: this.bundlerInfo.port.toString(),\n      protocol: options.scheme ?? 'http',\n    };\n  }\n}\n\nfunction getUrlComponentsFromProxyUrl(\n  options: Pick<CreateURLOptions, 'scheme'>,\n  url: string\n): UrlComponents {\n  const parsedProxyUrl = new URL(url);\n  let protocol = options.scheme ?? 'http';\n  if (parsedProxyUrl.protocol === 'https:') {\n    if (protocol === 'http') {\n      protocol = 'https';\n    }\n    if (!parsedProxyUrl.port) {\n      parsedProxyUrl.port = '443';\n    }\n  }\n  return {\n    port: parsedProxyUrl.port,\n    hostname: parsedProxyUrl.hostname,\n    protocol,\n  };\n}\n\nfunction getDefaultHostname(options: Pick<CreateURLOptions, 'hostname'>) {\n  // TODO: Drop REACT_NATIVE_PACKAGER_HOSTNAME\n  if (process.env.REACT_NATIVE_PACKAGER_HOSTNAME) {\n    return process.env.REACT_NATIVE_PACKAGER_HOSTNAME.trim();\n  } else if (options.hostname === 'localhost') {\n    // Restrict the use of `localhost`\n    // TODO: Note why we do this.\n    return '127.0.0.1';\n  }\n\n  return options.hostname || getIpAddress();\n}\n\nfunction joinUrlComponents({ protocol, hostname, port }: Partial<UrlComponents>): string {\n  assert(hostname, 'hostname cannot be inferred.');\n  const validProtocol = protocol ? `${protocol}://` : '';\n\n  const url = `${validProtocol}${hostname}`;\n\n  if (port) {\n    return url + `:${port}`;\n  }\n\n  return url;\n}\n\n/** @deprecated */\nfunction getProxyUrl(): string | undefined {\n  return process.env.EXPO_PACKAGER_PROXY_URL;\n}\n\n// TODO: Drop the undocumented env variables:\n// REACT_NATIVE_PACKAGER_HOSTNAME\n// EXPO_PACKAGER_PROXY_URL\n"], "names": ["Log", "debug", "require", "UrlCreator", "constructor", "defaults", "bundlerInfo", "constructLoadingUrl", "options", "platform", "url", "URL", "constructUrl", "scheme", "search", "URLSearchParams", "toString", "loadingUrl", "constructDevClientUrl", "protocol", "includes", "toLowerCase", "manifestUrl", "devClientUrl", "encodeURIComponent", "urlComponents", "getUrlComponents", "joinUrlComponents", "getTunnelUrlComponents", "tunnelUrl", "getTunnelUrl", "parsed", "port", "hostname", "proxyURL", "getProxyUrl", "getUrlComponentsFromProxyUrl", "hostType", "components", "warn", "getDefaultHostname", "parsedProxyUrl", "process", "env", "REACT_NATIVE_PACKAGER_HOSTNAME", "trim", "getIpAddress", "assert", "validProtocol", "EXPO_PACKAGER_PROXY_URL"], "mappings": "AAAA;;;;AAAmB,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACP,IAAA,IAAK,WAAL,KAAK,CAAA;AAEbA,IAAAA,GAAG,mCAAM,WAAW,EAAjB;AACc,IAAA,GAAgB,WAAhB,gBAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC,AAAsB,AAAC;AAgB9E,MAAMC,UAAU;IACrBC,YACSC,QAAsC,EACrCC,WAAiE,CACzE;aAFOD,QAAsC,GAAtCA,QAAsC;aACrCC,WAAiE,GAAjEA,WAAiE;KACvE;IAEJ;;;;;;;;;KASG,CACH,AAAOC,mBAAmB,CAACC,OAAyB,EAAEC,QAAuB,EAAU;QACrF,MAAMC,GAAG,GAAG,IAAIC,IAAG,IAAA,CAAC,eAAe,EAAE,IAAI,CAACC,YAAY,CAAC;YAAEC,MAAM,EAAE,MAAM;YAAE,GAAGL,OAAO;SAAE,CAAC,CAAC,AAAC;QACxF,IAAIC,QAAQ,EAAE;YACZC,GAAG,CAACI,MAAM,GAAG,IAAIC,eAAe,CAAC;gBAAEN,QAAQ;aAAE,CAAC,CAACO,QAAQ,EAAE,CAAC;SAC3D;QACD,MAAMC,UAAU,GAAGP,GAAG,CAACM,QAAQ,EAAE,AAAC;QAClCf,KAAK,CAAC,CAAC,aAAa,EAAEgB,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,OAAOA,UAAU,CAAC;KACnB;IAED,0GAA0G,CAC1G,AAAOC,qBAAqB,CAACV,OAA0B,EAAiB;YAClC,GAAa;QAAjD,MAAMW,QAAQ,GAAGX,CAAAA,OAAO,QAAQ,GAAfA,KAAAA,CAAe,GAAfA,OAAO,CAAEK,MAAM,CAAA,IAAI,CAAA,CAAA,GAAa,GAAb,IAAI,CAACR,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEQ,MAAM,CAAA,AAAC;QAE1D,IACE,CAACM,QAAQ,IACT,+EAA+E;QAC/E;YAAC,MAAM;YAAE,OAAO;SAAC,CAACC,QAAQ,CAACD,QAAQ,CAACE,WAAW,EAAE,CAAC,EAClD;YACA,OAAO,IAAI,CAAC;SACb;QAED,MAAMC,WAAW,GAAG,IAAI,CAACV,YAAY,CAAC;YAAE,GAAGJ,OAAO;YAAEK,MAAM,EAAE,MAAM;SAAE,CAAC,AAAC;QACtE,MAAMU,YAAY,GAAG,CAAC,EAAEJ,QAAQ,CAAC,gCAAgC,EAAEK,kBAAkB,CACnFF,WAAW,CACZ,CAAC,CAAC,AAAC;QACJrB,KAAK,CAAC,CAAC,gBAAgB,EAAEsB,YAAY,CAAC,iBAAiB,EAAED,WAAW,CAAC,MAAM,CAAC,EAAEd,OAAO,CAAC,CAAC;QACvF,OAAOe,YAAY,CAAC;KACrB;IAED,4BAA4B,CAC5B,AAAOX,YAAY,CAACJ,OAA0C,EAAU;QACtE,MAAMiB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAC1C,GAAG,IAAI,CAACrB,QAAQ;YAChB,GAAGG,OAAO;SACX,CAAC,AAAC;QACH,MAAME,GAAG,GAAGiB,iBAAiB,CAACF,aAAa,CAAC,AAAC;QAC7CxB,KAAK,CAAC,CAAC,KAAK,EAAES,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAOA,GAAG,CAAC;KACZ;IAED,wDAAwD,CACxD,AAAQkB,sBAAsB,CAACpB,OAAyC,EAAwB;YAC5E,YAAgB,AAAa,EAA7B,GAA6B;QAA/C,MAAMqB,SAAS,GAAG,CAAA,GAA6B,GAA7B,CAAA,YAAgB,GAAhB,IAAI,CAACvB,WAAW,EAACwB,YAAY,SAAI,GAAjC,KAAA,CAAiC,GAAjC,GAA6B,CAA7B,IAAiC,CAAjC,YAAgB,CAAiB,AAAC;QACpD,IAAI,CAACD,SAAS,EAAE;YACd,OAAO,IAAI,CAAC;SACb;QACD,MAAME,MAAM,GAAG,IAAIpB,IAAG,IAAA,CAACkB,SAAS,CAAC,AAAC;YAItBrB,OAAc;QAH1B,OAAO;YACLwB,IAAI,EAAED,MAAM,CAACC,IAAI;YACjBC,QAAQ,EAAEF,MAAM,CAACE,QAAQ;YACzBd,QAAQ,EAAEX,CAAAA,OAAc,GAAdA,OAAO,CAACK,MAAM,YAAdL,OAAc,GAAI,MAAM;SACnC,CAAC;KACH;IAED,AAAQkB,gBAAgB,CAAClB,OAAyB,EAAiB;QACjE,qBAAqB;QACrB,MAAM0B,QAAQ,GAAGC,WAAW,EAAE,AAAC;QAC/B,IAAID,QAAQ,EAAE;YACZ,OAAOE,4BAA4B,CAAC5B,OAAO,EAAE0B,QAAQ,CAAC,CAAC;SACxD;QAED,SAAS;QACT,IAAI1B,OAAO,CAAC6B,QAAQ,KAAK,QAAQ,EAAE;YACjC,MAAMC,UAAU,GAAG,IAAI,CAACV,sBAAsB,CAACpB,OAAO,CAAC,AAAC;YACxD,IAAI8B,UAAU,EAAE;gBACd,OAAOA,UAAU,CAAC;aACnB;YACDtC,GAAG,CAACuC,IAAI,CAAC,4EAA4E,CAAC,CAAC;SACxF,MAAM,IAAI/B,OAAO,CAAC6B,QAAQ,KAAK,WAAW,IAAI,CAAC7B,OAAO,CAACyB,QAAQ,EAAE;YAChEzB,OAAO,CAACyB,QAAQ,GAAG,WAAW,CAAC;SAChC;YAKWzB,OAAc;QAH1B,OAAO;YACLyB,QAAQ,EAAEO,kBAAkB,CAAChC,OAAO,CAAC;YACrCwB,IAAI,EAAE,IAAI,CAAC1B,WAAW,CAAC0B,IAAI,CAAChB,QAAQ,EAAE;YACtCG,QAAQ,EAAEX,CAAAA,OAAc,GAAdA,OAAO,CAACK,MAAM,YAAdL,OAAc,GAAI,MAAM;SACnC,CAAC;KACH;CACF;QA/FYL,UAAU,GAAVA,UAAU;AAiGvB,SAASiC,4BAA4B,CACnC5B,OAAyC,EACzCE,GAAW,EACI;IACf,MAAM+B,cAAc,GAAG,IAAI9B,IAAG,IAAA,CAACD,GAAG,CAAC,AAAC;QACrBF,OAAc;IAA7B,IAAIW,QAAQ,GAAGX,CAAAA,OAAc,GAAdA,OAAO,CAACK,MAAM,YAAdL,OAAc,GAAI,MAAM,AAAC;IACxC,IAAIiC,cAAc,CAACtB,QAAQ,KAAK,QAAQ,EAAE;QACxC,IAAIA,QAAQ,KAAK,MAAM,EAAE;YACvBA,QAAQ,GAAG,OAAO,CAAC;SACpB;QACD,IAAI,CAACsB,cAAc,CAACT,IAAI,EAAE;YACxBS,cAAc,CAACT,IAAI,GAAG,KAAK,CAAC;SAC7B;KACF;IACD,OAAO;QACLA,IAAI,EAAES,cAAc,CAACT,IAAI;QACzBC,QAAQ,EAAEQ,cAAc,CAACR,QAAQ;QACjCd,QAAQ;KACT,CAAC;CACH;AAED,SAASqB,kBAAkB,CAAChC,OAA2C,EAAE;IACvE,4CAA4C;IAC5C,IAAIkC,OAAO,CAACC,GAAG,CAACC,8BAA8B,EAAE;QAC9C,OAAOF,OAAO,CAACC,GAAG,CAACC,8BAA8B,CAACC,IAAI,EAAE,CAAC;KAC1D,MAAM,IAAIrC,OAAO,CAACyB,QAAQ,KAAK,WAAW,EAAE;QAC3C,kCAAkC;QAClC,6BAA6B;QAC7B,OAAO,WAAW,CAAC;KACpB;IAED,OAAOzB,OAAO,CAACyB,QAAQ,IAAIa,CAAAA,GAAAA,GAAY,AAAE,CAAA,aAAF,EAAE,CAAC;CAC3C;AAED,SAASnB,iBAAiB,CAAC,EAAER,QAAQ,CAAA,EAAEc,QAAQ,CAAA,EAAED,IAAI,CAAA,EAA0B,EAAU;IACvFe,CAAAA,GAAAA,OAAM,AAA0C,CAAA,QAA1C,CAACd,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IACjD,MAAMe,aAAa,GAAG7B,QAAQ,GAAG,CAAC,EAAEA,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,AAAC;IAEvD,MAAMT,GAAG,GAAG,CAAC,EAAEsC,aAAa,CAAC,EAAEf,QAAQ,CAAC,CAAC,AAAC;IAE1C,IAAID,IAAI,EAAE;QACR,OAAOtB,GAAG,GAAG,CAAC,CAAC,EAAEsB,IAAI,CAAC,CAAC,CAAC;KACzB;IAED,OAAOtB,GAAG,CAAC;CACZ;AAED,kBAAkB,CAClB,SAASyB,WAAW,GAAuB;IACzC,OAAOO,OAAO,CAACC,GAAG,CAACM,uBAAuB,CAAC;CAC5C,CAED,6CAA6C;CAC7C,iCAAiC;CACjC,0BAA0B"}