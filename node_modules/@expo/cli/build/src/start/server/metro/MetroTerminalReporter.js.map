{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroTerminalReporter.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { Terminal } from 'metro-core';\nimport path from 'path';\n\nimport { learnMore } from '../../../utils/link';\nimport { logWarning, TerminalReporter } from './TerminalReporter';\nimport { BuildPhase, BundleDetails, BundleProgress, SnippetError } from './TerminalReporter.types';\nimport { NODE_STDLIB_MODULES } from './externals';\n\nconst MAX_PROGRESS_BAR_CHAR_WIDTH = 16;\nconst DARK_BLOCK_CHAR = '\\u2593';\nconst LIGHT_BLOCK_CHAR = '\\u2591';\n/**\n * Extends the default Metro logger and adds some additional features.\n * Also removes the giant Metro logo from the output.\n */\nexport class MetroTerminalReporter extends TerminalReporter {\n  constructor(public projectRoot: string, terminal: Terminal) {\n    super(terminal);\n  }\n\n  // Used for testing\n  _getElapsedTime(startTime: number): number {\n    return Date.now() - startTime;\n  }\n  /**\n   * Extends the bundle progress to include the current platform that we're bundling.\n   *\n   * @returns `iOS path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)`\n   */\n  _getBundleStatusMessage(progress: BundleProgress, phase: BuildPhase): string {\n    const platform = getPlatformTagForBuildDetails(progress.bundleDetails);\n    const inProgress = phase === 'in_progress';\n\n    if (!inProgress) {\n      const status = phase === 'done' ? `Bundling complete ` : `Bundling failed `;\n      const color = phase === 'done' ? chalk.green : chalk.red;\n\n      const startTime = this._bundleTimers.get(progress.bundleDetails.buildID!);\n      const time = startTime != null ? chalk.dim(this._getElapsedTime(startTime) + 'ms') : '';\n      // iOS Bundling complete 150ms\n      return color(platform + status) + time;\n    }\n\n    const localPath = path.relative('.', progress.bundleDetails.entryFile);\n    const filledBar = Math.floor(progress.ratio * MAX_PROGRESS_BAR_CHAR_WIDTH);\n\n    const _progress = inProgress\n      ? chalk.green.bgGreen(DARK_BLOCK_CHAR.repeat(filledBar)) +\n        chalk.bgWhite.white(LIGHT_BLOCK_CHAR.repeat(MAX_PROGRESS_BAR_CHAR_WIDTH - filledBar)) +\n        chalk.bold(` ${(100 * progress.ratio).toFixed(1).padStart(4)}% `) +\n        chalk.dim(\n          `(${progress.transformedFileCount\n            .toString()\n            .padStart(progress.totalFileCount.toString().length)}/${progress.totalFileCount})`\n        )\n      : '';\n\n    return (\n      platform +\n      chalk.reset.dim(`${path.dirname(localPath)}/`) +\n      chalk.bold(path.basename(localPath)) +\n      ' ' +\n      _progress\n    );\n  }\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void {\n    // Don't print a giant logo...\n    this.terminal.log('Starting Metro Bundler');\n  }\n\n  shouldFilterClientLog(event: {\n    type: 'client_log';\n    level: 'trace' | 'info' | 'warn' | 'log' | 'group' | 'groupCollapsed' | 'groupEnd' | 'debug';\n    data: unknown[];\n  }): boolean {\n    return isAppRegistryStartupMessage(event.data);\n  }\n\n  /** Print the cache clear message. */\n  transformCacheReset(): void {\n    logWarning(\n      this.terminal,\n      chalk`Bundler cache is empty, rebuilding {dim (this may take a minute)}`\n    );\n  }\n\n  /** One of the first logs that will be printed */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {\n    // this.terminal.log('Dependency graph is loading...');\n    if (hasReducedPerformance) {\n      // Extends https://github.com/facebook/metro/blob/347b1d7ed87995d7951aaa9fd597c04b06013dac/packages/metro/src/lib/TerminalReporter.js#L283-L290\n      this.terminal.log(\n        chalk.red(\n          [\n            'Metro is operating with reduced performance.',\n            'Please fix the problem above and restart Metro.',\n          ].join('\\n')\n        )\n      );\n    }\n  }\n\n  _logBundlingError(error: SnippetError): void {\n    const moduleResolutionError = formatUsingNodeStandardLibraryError(this.projectRoot, error);\n    if (moduleResolutionError) {\n      return this.terminal.log(maybeAppendCodeFrame(moduleResolutionError, error.message));\n    }\n    return super._logBundlingError(error);\n  }\n}\n\n/**\n * Formats an error where the user is attempting to import a module from the Node.js standard library.\n * Exposed for testing.\n *\n * @param error\n * @returns error message or null if not a module resolution error\n */\nexport function formatUsingNodeStandardLibraryError(\n  projectRoot: string,\n  error: SnippetError\n): string | null {\n  if (!error.message) {\n    return null;\n  }\n  const { targetModuleName, originModulePath } = error;\n  if (!targetModuleName || !originModulePath) {\n    return null;\n  }\n  const relativePath = path.relative(projectRoot, originModulePath);\n\n  const DOCS_PAGE_URL =\n    'https://docs.expo.dev/workflow/using-libraries/#using-third-party-libraries';\n\n  if (isNodeStdLibraryModule(targetModuleName)) {\n    if (originModulePath.includes('node_modules')) {\n      return [\n        `The package at \"${chalk.bold(\n          relativePath\n        )}\" attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    } else {\n      return [\n        `You attempted attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\" from \"${chalk.bold(relativePath)}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    }\n  }\n  return `Unable to resolve \"${targetModuleName}\" from \"${relativePath}\"`;\n}\n\nexport function isNodeStdLibraryModule(moduleName: string): boolean {\n  return /^node:/.test(moduleName) || NODE_STDLIB_MODULES.includes(moduleName);\n}\n\n/** If the code frame can be found then append it to the existing message.  */\nfunction maybeAppendCodeFrame(message: string, rawMessage: string): string {\n  const codeFrame = stripMetroInfo(rawMessage);\n  if (codeFrame) {\n    message += '\\n' + codeFrame;\n  }\n  return message;\n}\n\n/**\n * Remove the Metro cache clearing steps if they exist.\n * In future versions we won't need this.\n * Returns the remaining code frame logs.\n */\nexport function stripMetroInfo(errorMessage: string): string | null {\n  // Newer versions of Metro don't include the list.\n  if (!errorMessage.includes('4. Remove the cache')) {\n    return null;\n  }\n  const lines = errorMessage.split('\\n');\n  const index = lines.findIndex((line) => line.includes('4. Remove the cache'));\n  if (index === -1) {\n    return null;\n  }\n  return lines.slice(index + 1).join('\\n');\n}\n\n/** @returns if the message matches the initial startup log */\nfunction isAppRegistryStartupMessage(body: any[]): boolean {\n  return (\n    body.length === 1 &&\n    (/^Running application \"main\" with appParams:/.test(body[0]) ||\n      /^Running \"main\" with \\{/.test(body[0]))\n  );\n}\n\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getPlatformTagForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  const platform = bundleDetails?.platform ?? null;\n  if (platform) {\n    const formatted = { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform;\n    return `${chalk.bold(formatted)} `;\n  }\n\n  return '';\n}\n"], "names": ["formatUsingNodeStandardLibraryError", "isNodeStdLibraryModule", "stripMetroInfo", "MAX_PROGRESS_BAR_CHAR_WIDTH", "DARK_BLOCK_CHAR", "LIGHT_BLOCK_CHAR", "MetroTerminalReporter", "TerminalReporter", "constructor", "projectRoot", "terminal", "_getElapsedTime", "startTime", "Date", "now", "_getBundleStatusMessage", "progress", "phase", "platform", "getPlatformTagForBuildDetails", "bundleDetails", "inProgress", "status", "color", "chalk", "green", "red", "_bundleTimers", "get", "buildID", "time", "dim", "localPath", "path", "relative", "entryFile", "<PERSON><PERSON><PERSON>", "Math", "floor", "ratio", "_progress", "bgGreen", "repeat", "bgWhite", "white", "bold", "toFixed", "padStart", "transformedFileCount", "toString", "totalFileCount", "length", "reset", "dirname", "basename", "_logInitializing", "port", "hasReducedPerformance", "log", "shouldFilterClientLog", "event", "isAppRegistryStartupMessage", "data", "transformCacheReset", "logWarning", "dependencyGraphLoading", "join", "_logBundlingError", "error", "moduleResolutionError", "maybeAppendCodeFrame", "message", "targetModuleName", "originModulePath", "relativePath", "DOCS_PAGE_URL", "includes", "learnMore", "moduleName", "test", "NODE_STDLIB_MODULES", "rawMessage", "codeFrame", "errorMessage", "lines", "split", "index", "findIndex", "line", "slice", "body", "formatted", "ios", "android", "web"], "mappings": "AAAA;;;;QAwHgBA,mCAAmC,GAAnCA,mCAAmC;QAwCnCC,sBAAsB,GAAtBA,sBAAsB;QAkBtBC,cAAc,GAAdA,cAAc;AAlLZ,IAAA,MAAO,kCAAP,OAAO,EAAA;AAER,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEG,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AACF,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AAE7B,IAAA,UAAa,WAAb,aAAa,CAAA;;;;;;AAEjD,MAAMC,2BAA2B,GAAG,EAAE,AAAC;AACvC,MAAMC,eAAe,GAAG,QAAQ,AAAC;AACjC,MAAMC,gBAAgB,GAAG,QAAQ,AAAC;AAK3B,MAAMC,qBAAqB,SAASC,iBAAgB,iBAAA;IACzDC,YAAmBC,WAAmB,EAAEC,QAAkB,CAAE;QAC1D,KAAK,CAACA,QAAQ,CAAC,CAAC;aADCD,WAAmB,GAAnBA,WAAmB;KAErC;IAED,mBAAmB;IACnBE,eAAe,CAACC,SAAiB,EAAU;QACzC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;KAC/B;IACD;;;;KAIG,CACHG,uBAAuB,CAACC,QAAwB,EAAEC,KAAiB,EAAU;QAC3E,MAAMC,QAAQ,GAAGC,6BAA6B,CAACH,QAAQ,CAACI,aAAa,CAAC,AAAC;QACvE,MAAMC,UAAU,GAAGJ,KAAK,KAAK,aAAa,AAAC;QAE3C,IAAI,CAACI,UAAU,EAAE;YACf,MAAMC,MAAM,GAAGL,KAAK,KAAK,MAAM,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,gBAAgB,CAAC,AAAC;YAC5E,MAAMM,KAAK,GAAGN,KAAK,KAAK,MAAM,GAAGO,MAAK,QAAA,CAACC,KAAK,GAAGD,MAAK,QAAA,CAACE,GAAG,AAAC;YAEzD,MAAMd,SAAS,GAAG,IAAI,CAACe,aAAa,CAACC,GAAG,CAACZ,QAAQ,CAACI,aAAa,CAACS,OAAO,CAAE,AAAC;YAC1E,MAAMC,IAAI,GAAGlB,SAAS,IAAI,IAAI,GAAGY,MAAK,QAAA,CAACO,GAAG,CAAC,IAAI,CAACpB,eAAe,CAACC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,AAAC;YACxF,8BAA8B;YAC9B,OAAOW,KAAK,CAACL,QAAQ,GAAGI,MAAM,CAAC,GAAGQ,IAAI,CAAC;SACxC;QAED,MAAME,SAAS,GAAGC,KAAI,QAAA,CAACC,QAAQ,CAAC,GAAG,EAAElB,QAAQ,CAACI,aAAa,CAACe,SAAS,CAAC,AAAC;QACvE,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACtB,QAAQ,CAACuB,KAAK,GAAGpC,2BAA2B,CAAC,AAAC;QAE3E,MAAMqC,SAAS,GAAGnB,UAAU,GACxBG,MAAK,QAAA,CAACC,KAAK,CAACgB,OAAO,CAACrC,eAAe,CAACsC,MAAM,CAACN,SAAS,CAAC,CAAC,GACtDZ,MAAK,QAAA,CAACmB,OAAO,CAACC,KAAK,CAACvC,gBAAgB,CAACqC,MAAM,CAACvC,2BAA2B,GAAGiC,SAAS,CAAC,CAAC,GACrFZ,MAAK,QAAA,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG7B,QAAQ,CAACuB,KAAK,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACjEvB,MAAK,QAAA,CAACO,GAAG,CACP,CAAC,CAAC,EAAEf,QAAQ,CAACgC,oBAAoB,CAC9BC,QAAQ,EAAE,CACVF,QAAQ,CAAC/B,QAAQ,CAACkC,cAAc,CAACD,QAAQ,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEnC,QAAQ,CAACkC,cAAc,CAAC,CAAC,CAAC,CACrF,GACD,EAAE,AAAC;QAEP,OACEhC,QAAQ,GACRM,MAAK,QAAA,CAAC4B,KAAK,CAACrB,GAAG,CAAC,CAAC,EAAEE,KAAI,QAAA,CAACoB,OAAO,CAACrB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAC9CR,MAAK,QAAA,CAACqB,IAAI,CAACZ,KAAI,QAAA,CAACqB,QAAQ,CAACtB,SAAS,CAAC,CAAC,GACpC,GAAG,GACHQ,SAAS,CACT;KACH;IAEDe,gBAAgB,CAACC,IAAY,EAAEC,qBAA8B,EAAQ;QACnE,8BAA8B;QAC9B,IAAI,CAAC/C,QAAQ,CAACgD,GAAG,CAAC,wBAAwB,CAAC,CAAC;KAC7C;IAEDC,qBAAqB,CAACC,KAIrB,EAAW;QACV,OAAOC,2BAA2B,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC;KAChD;IAED,qCAAqC,CACrCC,mBAAmB,GAAS;QAC1BC,CAAAA,GAAAA,iBAAU,AAGT,CAAA,WAHS,CACR,IAAI,CAACtD,QAAQ,EACbc,MAAK,QAAA,CAAC,iEAAiE,CAAC,CACzE,CAAC;KACH;IAED,iDAAiD,CACjDyC,sBAAsB,CAACR,qBAA8B,EAAQ;QAC3D,uDAAuD;QACvD,IAAIA,qBAAqB,EAAE;YACzB,+IAA+I;YAC/I,IAAI,CAAC/C,QAAQ,CAACgD,GAAG,CACflC,MAAK,QAAA,CAACE,GAAG,CACP;gBACE,8CAA8C;gBAC9C,iDAAiD;aAClD,CAACwC,IAAI,CAAC,IAAI,CAAC,CACb,CACF,CAAC;SACH;KACF;IAEDC,iBAAiB,CAACC,KAAmB,EAAQ;QAC3C,MAAMC,qBAAqB,GAAGrE,mCAAmC,CAAC,IAAI,CAACS,WAAW,EAAE2D,KAAK,CAAC,AAAC;QAC3F,IAAIC,qBAAqB,EAAE;YACzB,OAAO,IAAI,CAAC3D,QAAQ,CAACgD,GAAG,CAACY,oBAAoB,CAACD,qBAAqB,EAAED,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;SACtF;QACD,OAAO,KAAK,CAACJ,iBAAiB,CAACC,KAAK,CAAC,CAAC;KACvC;CACF;QA/FY9D,qBAAqB,GAArBA,qBAAqB;AAwG3B,SAASN,mCAAmC,CACjDS,WAAmB,EACnB2D,KAAmB,EACJ;IACf,IAAI,CAACA,KAAK,CAACG,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,MAAM,EAAEC,gBAAgB,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAGL,KAAK,AAAC;IACrD,IAAI,CAACI,gBAAgB,IAAI,CAACC,gBAAgB,EAAE;QAC1C,OAAO,IAAI,CAAC;KACb;IACD,MAAMC,YAAY,GAAGzC,KAAI,QAAA,CAACC,QAAQ,CAACzB,WAAW,EAAEgE,gBAAgB,CAAC,AAAC;IAElE,MAAME,aAAa,GACjB,6EAA6E,AAAC;IAEhF,IAAI1E,sBAAsB,CAACuE,gBAAgB,CAAC,EAAE;QAC5C,IAAIC,gBAAgB,CAACG,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC7C,OAAO;gBACL,CAAC,gBAAgB,EAAEpD,MAAK,QAAA,CAACqB,IAAI,CAC3B6B,YAAY,CACb,CAAC,wDAAwD,EAAElD,MAAK,QAAA,CAACqB,IAAI,CACpE2B,gBAAgB,CACjB,CAAC,EAAE,CAAC;gBACL,CAAC,sFAAsF,CAAC;gBACxFK,CAAAA,GAAAA,KAAS,AAAe,CAAA,UAAf,CAACF,aAAa,CAAC;aACzB,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;SACd,MAAM;YACL,OAAO;gBACL,CAAC,oEAAoE,EAAE1C,MAAK,QAAA,CAACqB,IAAI,CAC/E2B,gBAAgB,CACjB,CAAC,QAAQ,EAAEhD,MAAK,QAAA,CAACqB,IAAI,CAAC6B,YAAY,CAAC,CAAC,EAAE,CAAC;gBACxC,CAAC,sFAAsF,CAAC;gBACxFG,CAAAA,GAAAA,KAAS,AAAe,CAAA,UAAf,CAACF,aAAa,CAAC;aACzB,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;SACd;KACF;IACD,OAAO,CAAC,mBAAmB,EAAEM,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC;CACzE;AAEM,SAASzE,sBAAsB,CAAC6E,UAAkB,EAAW;IAClE,OAAO,SAASC,IAAI,CAACD,UAAU,CAAC,IAAIE,UAAmB,oBAAA,CAACJ,QAAQ,CAACE,UAAU,CAAC,CAAC;CAC9E;AAED,8EAA8E,CAC9E,SAASR,oBAAoB,CAACC,OAAe,EAAEU,UAAkB,EAAU;IACzE,MAAMC,SAAS,GAAGhF,cAAc,CAAC+E,UAAU,CAAC,AAAC;IAC7C,IAAIC,SAAS,EAAE;QACbX,OAAO,IAAI,IAAI,GAAGW,SAAS,CAAC;KAC7B;IACD,OAAOX,OAAO,CAAC;CAChB;AAOM,SAASrE,cAAc,CAACiF,YAAoB,EAAiB;IAClE,kDAAkD;IAClD,IAAI,CAACA,YAAY,CAACP,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QACjD,OAAO,IAAI,CAAC;KACb;IACD,MAAMQ,KAAK,GAAGD,YAAY,CAACE,KAAK,CAAC,IAAI,CAAC,AAAC;IACvC,MAAMC,KAAK,GAAGF,KAAK,CAACG,SAAS,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACZ,QAAQ,CAAC,qBAAqB,CAAC;IAAA,CAAC,AAAC;IAC9E,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IACD,OAAOF,KAAK,CAACK,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACpB,IAAI,CAAC,IAAI,CAAC,CAAC;CAC1C;AAED,8DAA8D,CAC9D,SAASL,2BAA2B,CAAC6B,IAAW,EAAW;IACzD,OACEA,IAAI,CAACvC,MAAM,KAAK,CAAC,IACjB,CAAC,8CAA8C4B,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,IAC1D,0BAA0BX,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1C;CACH;AAED,kEAAkE,CAClE,SAASvE,6BAA6B,CAACC,aAAoC,EAAU;QAClEA,GAAuB;IAAxC,MAAMF,QAAQ,GAAGE,CAAAA,GAAuB,GAAvBA,aAAa,QAAU,GAAvBA,KAAAA,CAAuB,GAAvBA,aAAa,CAAEF,QAAQ,YAAvBE,GAAuB,GAAI,IAAI,AAAC;IACjD,IAAIF,QAAQ,EAAE;QACZ,MAAMyE,SAAS,GAAG;YAAEC,GAAG,EAAE,KAAK;YAAEC,OAAO,EAAE,SAAS;YAAEC,GAAG,EAAE,KAAK;SAAE,CAAC5E,QAAQ,CAAC,IAAIA,QAAQ,AAAC;QACvF,OAAO,CAAC,EAAEM,MAAK,QAAA,CAACqB,IAAI,CAAC8C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;KACpC;IAED,OAAO,EAAE,CAAC;CACX"}