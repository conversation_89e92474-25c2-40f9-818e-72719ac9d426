{"version": 3, "sources": ["../../../../../src/start/server/metro/TerminalReporter.ts"], "sourcesContent": ["// This file represents an abstraction on the metro TerminalReporter.\n// We use this abstraction to safely extend the TerminalReporter for our own custom logging.\nimport chalk from 'chalk';\nimport { Terminal } from 'metro-core';\nimport UpstreamTerminalReporter from 'metro/src/lib/TerminalReporter';\nimport util from 'util';\n\nimport { stripAnsi } from '../../../utils/ansi';\nimport {\n  BundleDetails,\n  TerminalReportableEvent,\n  TerminalReporterInterface,\n} from './TerminalReporter.types';\n\n/**\n * A standard way to log a warning to the terminal. This should not be called\n * from some arbitrary Metro logic, only from the reporters. Instead of\n * calling this, add a new type of ReportableEvent instead, and implement a\n * proper handler in the reporter(s).\n */\nexport function logWarning(terminal: Terminal, format: string, ...args: any[]): void {\n  const str = util.format(format, ...args);\n  terminal.log('%s: %s', chalk.yellow('warning'), str);\n}\n\n/**\n * Similar to `logWarning`, but for messages that require the user to act.\n */\nexport function logError(terminal: Terminal, format: string, ...args: any[]): void {\n  terminal.log(\n    '%s: %s',\n    chalk.red('error'),\n    // Syntax errors may have colors applied for displaying code frames\n    // in various places outside of where Metro is currently running.\n    // If the current terminal does not support color, we'll strip the colors\n    // here.\n    util.format(chalk.supportsColor ? format : stripAnsi(format), ...args)\n  );\n}\n\nconst XTerminalReporter = UpstreamTerminalReporter as unknown as TerminalReporterInterface;\n\n/** Extended TerminalReporter class but with proper types and extra functionality to avoid using the `_log` method directly in subclasses. */\nexport class TerminalReporter extends XTerminalReporter implements TerminalReporterInterface {\n  /**\n   * A cache of { [buildID]: BundleDetails } which can be used to\n   * add more contextual logs. BundleDetails is currently only sent with `bundle_build_started`\n   * so we need to cache the details in order to print the platform info with other event types.\n   */\n  _bundleDetails: Map<string, BundleDetails> = new Map();\n\n  /** Keep track of how long a bundle takes to complete */\n  _bundleTimers: Map<string, number> = new Map();\n\n  _log(event: TerminalReportableEvent): void {\n    switch (event.type) {\n      case 'transform_cache_reset':\n        return this.transformCacheReset();\n      case 'dep_graph_loading':\n        return this.dependencyGraphLoading(event.hasReducedPerformance);\n      case 'client_log':\n        if (this.shouldFilterClientLog(event)) {\n          return;\n        }\n        break;\n    }\n    return super._log(event);\n  }\n\n  /** Gives subclasses an easy interface for filtering out logs. Return `true` to skip. */\n  shouldFilterClientLog(event: {\n    type: 'client_log';\n    level: 'trace' | 'info' | 'warn' | 'log' | 'group' | 'groupCollapsed' | 'groupEnd' | 'debug';\n    data: unknown[];\n  }): boolean {\n    return false;\n  }\n\n  /** Cache has been reset. */\n  transformCacheReset(): void {}\n\n  /** One of the first logs that will be printed. */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {}\n\n  /**\n   * Custom log event representing the end of the bundling.\n   *\n   * @param event event object.\n   * @param duration duration of the build in milliseconds.\n   */\n  bundleBuildEnded(event: TerminalReportableEvent, duration: number): void {}\n\n  /**\n   * This function is exclusively concerned with updating the internal state.\n   * No logging or status updates should be done at this point.\n   */\n  _updateState(\n    event: TerminalReportableEvent & { bundleDetails?: BundleDetails; buildID?: string }\n  ) {\n    // Append the buildID to the bundleDetails.\n    if (event.bundleDetails) {\n      event.bundleDetails.buildID = event.buildID;\n    }\n\n    super._updateState(event);\n    switch (event.type) {\n      case 'bundle_build_done':\n      case 'bundle_build_failed': {\n        const startTime = this._bundleTimers.get(event.buildID);\n        // Observed a bug in Metro where the `bundle_build_done` is invoked twice during a static bundle\n        // i.e. `expo export`.\n        if (startTime == null) {\n          break;\n        }\n\n        this.bundleBuildEnded(event, startTime ? Date.now() - startTime : 0);\n        this._bundleTimers.delete(event.buildID);\n        break;\n      }\n      case 'bundle_build_started':\n        this._bundleDetails.set(event.buildID, event.bundleDetails);\n        this._bundleTimers.set(event.buildID, Date.now());\n        break;\n    }\n  }\n}\n"], "names": ["logWarning", "logError", "terminal", "format", "args", "str", "util", "log", "chalk", "yellow", "red", "supportsColor", "stripAnsi", "XTerminalReporter", "UpstreamTerminalReporter", "TerminalReporter", "_bundleDetails", "Map", "_bundleTimers", "_log", "event", "type", "transformCacheReset", "dependencyGraphLoading", "hasReducedPerformance", "shouldFilterClientLog", "bundleBuildEnded", "duration", "_updateState", "bundleDetails", "buildID", "startTime", "get", "Date", "now", "delete", "set"], "mappings": "AAEA;;;;QAkBgBA,UAAU,GAAVA,UAAU;QAQVC,QAAQ,GAARA,QAAQ;AA1BN,IAAA,MAAO,kCAAP,OAAO,EAAA;AAEY,IAAA,iBAAgC,kCAAhC,gCAAgC,EAAA;AACpD,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEG,IAAA,KAAqB,WAArB,qBAAqB,CAAA;;;;;;AAaxC,SAASD,UAAU,CAACE,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAI,AAAO,EAAQ;IACnF,MAAMC,GAAG,GAAGC,KAAI,QAAA,CAACH,MAAM,CAACA,MAAM,KAAKC,IAAI,CAAC,AAAC;IACzCF,QAAQ,CAACK,GAAG,CAAC,QAAQ,EAAEC,MAAK,QAAA,CAACC,MAAM,CAAC,SAAS,CAAC,EAAEJ,GAAG,CAAC,CAAC;CACtD;AAKM,SAASJ,QAAQ,CAACC,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAI,AAAO,EAAQ;IACjFF,QAAQ,CAACK,GAAG,CACV,QAAQ,EACRC,MAAK,QAAA,CAACE,GAAG,CAAC,OAAO,CAAC,EAClB,mEAAmE;IACnE,iEAAiE;IACjE,yEAAyE;IACzE,QAAQ;IACRJ,KAAI,QAAA,CAACH,MAAM,CAACK,MAAK,QAAA,CAACG,aAAa,GAAGR,MAAM,GAAGS,CAAAA,GAAAA,KAAS,AAAQ,CAAA,UAAR,CAACT,MAAM,CAAC,KAAKC,IAAI,CAAC,CACvE,CAAC;CACH;AAED,MAAMS,iBAAiB,GAAGC,iBAAwB,QAAA,AAAwC,AAAC;AAGpF,MAAMC,gBAAgB,SAASF,iBAAiB;IACrD;;;;KAIG,CACHG,cAAc,GAA+B,IAAIC,GAAG,EAAE,CAAC;IAEvD,wDAAwD,CACxDC,aAAa,GAAwB,IAAID,GAAG,EAAE,CAAC;IAE/CE,IAAI,CAACC,KAA8B,EAAQ;QACzC,OAAQA,KAAK,CAACC,IAAI;YAChB,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAACC,mBAAmB,EAAE,CAAC;YACpC,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAACC,sBAAsB,CAACH,KAAK,CAACI,qBAAqB,CAAC,CAAC;YAClE,KAAK,YAAY;gBACf,IAAI,IAAI,CAACC,qBAAqB,CAACL,KAAK,CAAC,EAAE;oBACrC,OAAO;iBACR;gBACD,MAAM;SACT;QACD,OAAO,KAAK,CAACD,IAAI,CAACC,KAAK,CAAC,CAAC;KAC1B;IAED,wFAAwF,CACxFK,qBAAqB,CAACL,KAIrB,EAAW;QACV,OAAO,KAAK,CAAC;KACd;IAED,4BAA4B,CAC5BE,mBAAmB,GAAS,EAAE;IAE9B,kDAAkD,CAClDC,sBAAsB,CAACC,qBAA8B,EAAQ,EAAE;IAE/D;;;;;KAKG,CACHE,gBAAgB,CAACN,KAA8B,EAAEO,QAAgB,EAAQ,EAAE;IAE3E;;;KAGG,CACHC,YAAY,CACVR,KAAoF,EACpF;QACA,2CAA2C;QAC3C,IAAIA,KAAK,CAACS,aAAa,EAAE;YACvBT,KAAK,CAACS,aAAa,CAACC,OAAO,GAAGV,KAAK,CAACU,OAAO,CAAC;SAC7C;QAED,KAAK,CAACF,YAAY,CAACR,KAAK,CAAC,CAAC;QAC1B,OAAQA,KAAK,CAACC,IAAI;YAChB,KAAK,mBAAmB,CAAC;YACzB,KAAK,qBAAqB;gBAAE;oBAC1B,MAAMU,SAAS,GAAG,IAAI,CAACb,aAAa,CAACc,GAAG,CAACZ,KAAK,CAACU,OAAO,CAAC,AAAC;oBACxD,gGAAgG;oBAChG,sBAAsB;oBACtB,IAAIC,SAAS,IAAI,IAAI,EAAE;wBACrB,MAAM;qBACP;oBAED,IAAI,CAACL,gBAAgB,CAACN,KAAK,EAAEW,SAAS,GAAGE,IAAI,CAACC,GAAG,EAAE,GAAGH,SAAS,GAAG,CAAC,CAAC,CAAC;oBACrE,IAAI,CAACb,aAAa,CAACiB,MAAM,CAACf,KAAK,CAACU,OAAO,CAAC,CAAC;oBACzC,MAAM;iBACP;YACD,KAAK,sBAAsB;gBACzB,IAAI,CAACd,cAAc,CAACoB,GAAG,CAAChB,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACS,aAAa,CAAC,CAAC;gBAC5D,IAAI,CAACX,aAAa,CAACkB,GAAG,CAAChB,KAAK,CAACU,OAAO,EAAEG,IAAI,CAACC,GAAG,EAAE,CAAC,CAAC;gBAClD,MAAM;SACT;KACF;CACF;QAlFYnB,gBAAgB,GAAhBA,gBAAgB"}