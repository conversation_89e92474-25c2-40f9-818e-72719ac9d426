{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroBundlerDevServer.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getConfig } from '@expo/config';\nimport { prependMiddleware } from '@expo/dev-server';\nimport * as runtimeEnv from '@expo/env';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fetch from 'node-fetch';\nimport path from 'path';\n\nimport { Log } from '../../../log';\nimport getDevClientProperties from '../../../utils/analytics/getDevClientProperties';\nimport { logEventAsync } from '../../../utils/analytics/rudderstackClient';\nimport { getFreePortAsync } from '../../../utils/port';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\nimport { getStaticRenderFunctions } from '../getStaticRenderFunctions';\nimport { ContextModuleSourceMapsMiddleware } from '../middleware/ContextModuleSourceMapsMiddleware';\nimport { CreateFileMiddleware } from '../middleware/CreateFileMiddleware';\nimport { FaviconMiddleware } from '../middleware/FaviconMiddleware';\nimport { HistoryFallbackMiddleware } from '../middleware/HistoryFallbackMiddleware';\nimport { InterstitialPageMiddleware } from '../middleware/InterstitialPageMiddleware';\nimport { createBundleUrlPath, resolveMainModuleName } from '../middleware/ManifestMiddleware';\nimport { ReactDevToolsPageMiddleware } from '../middleware/ReactDevToolsPageMiddleware';\nimport {\n  DeepLinkHandler,\n  RuntimeRedirectMiddleware,\n} from '../middleware/RuntimeRedirectMiddleware';\nimport { ServeStaticMiddleware } from '../middleware/ServeStaticMiddleware';\nimport { ServerNext, ServerRequest, ServerResponse } from '../middleware/server.types';\nimport { startTypescriptTypeGenerationAsync } from '../type-generation/startTypescriptTypeGeneration';\nimport { instantiateMetroAsync } from './instantiateMetro';\nimport { getErrorOverlayHtmlAsync } from './metroErrorInterface';\nimport { metroWatchTypeScriptFiles } from './metroWatchTypeScriptFiles';\nimport { observeFileChanges } from './waitForMetroToObserveTypeScriptFile';\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\n/** Default port to use for apps running in Expo Go. */\nconst EXPO_GO_METRO_PORT = 8081;\n\n/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */\nconst DEV_CLIENT_METRO_PORT = 8081;\n\nexport class MetroBundlerDevServer extends BundlerDevServer {\n  private metro: import('metro').Server | null = null;\n\n  get name(): string {\n    return 'metro';\n  }\n\n  async resolvePortAsync(options: Partial<BundlerStartOptions> = {}): Promise<number> {\n    const port =\n      // If the manually defined port is busy then an error should be thrown...\n      options.port ??\n      // Otherwise use the default port based on the runtime target.\n      (options.devClient\n        ? // Don't check if the port is busy if we're using the dev client since most clients are hardcoded to 8081.\n          Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT\n        : // Otherwise (running in Expo Go) use a free port that falls back on the classic 8081 port.\n          await getFreePortAsync(EXPO_GO_METRO_PORT));\n\n    return port;\n  }\n\n  /** Get routes from Expo Router. */\n  async getRoutesAsync() {\n    const url = this.getDevServerUrl();\n    assert(url, 'Dev server must be started');\n    const { getManifest } = await getStaticRenderFunctions(this.projectRoot, url, {\n      // Ensure the API Routes are included\n      environment: 'node',\n    });\n\n    return getManifest({ fetchData: true });\n  }\n\n  async composeResourcesWithHtml({\n    mode,\n    resources,\n    template,\n    devBundleUrl,\n  }: {\n    mode: 'development' | 'production';\n    resources: SerialAsset[];\n    template: string;\n    devBundleUrl?: string;\n  }): Promise<string> {\n    if (!resources) {\n      return '';\n    }\n    const isDev = mode === 'development';\n    return htmlFromSerialAssets(resources, {\n      dev: isDev,\n      template,\n      bundleUrl: isDev ? devBundleUrl : undefined,\n    });\n  }\n\n  async getStaticRenderFunctionAsync({\n    mode,\n    minify = mode !== 'development',\n  }: {\n    mode: 'development' | 'production';\n    minify?: boolean;\n  }) {\n    const url = this.getDevServerUrl()!;\n\n    const { getStaticContent } = await getStaticRenderFunctions(this.projectRoot, url, {\n      minify,\n      dev: mode !== 'production',\n      // Ensure the API Routes are included\n      environment: 'node',\n    });\n    return async (path: string) => {\n      return await getStaticContent(new URL(path, url));\n    };\n  }\n\n  async getStaticResourcesAsync({\n    mode,\n    minify = mode !== 'development',\n  }: {\n    mode: string;\n    minify?: boolean;\n  }): Promise<SerialAsset[]> {\n    const devBundleUrlPathname = createBundleUrlPath({\n      platform: 'web',\n      mode,\n      minify,\n      environment: 'client',\n      serializerOutput: 'static',\n      mainModuleName: resolveMainModuleName(this.projectRoot, getConfig(this.projectRoot), 'web'),\n    });\n\n    const bundleUrl = new URL(devBundleUrlPathname, this.getDevServerUrl()!);\n\n    // Fetch the generated HTML from our custom Metro serializer\n    const results = await fetch(bundleUrl.toString());\n\n    const txt = await results.text();\n\n    let data: any;\n    try {\n      data = JSON.parse(txt);\n    } catch (error: any) {\n      Log.error(\n        'Failed to generate resources with Metro, the Metro config may not be using the correct serializer. Ensure the metro.config.js is extending the expo/metro-config and is not overriding the serializer.'\n      );\n      debug(txt);\n      throw error;\n    }\n\n    // NOTE: This could potentially need more validation in the future.\n    if (Array.isArray(data)) {\n      return data;\n    }\n\n    if (data != null && (data.errors || data.type?.match(/.*Error$/))) {\n      // {\n      //   type: 'InternalError',\n      //   errors: [],\n      //   message: 'Metro has encountered an error: While trying to resolve module `stylis` from file `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/@emotion/cache/dist/emotion-cache.browser.esm.js`, the package `/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/package.json` was successfully found. However, this package itself specifies a `main` module field that could not be resolved (`/Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs`. Indeed, none of these files exist:\\n' +\n      //     '\\n' +\n      //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css)\\n' +\n      //     '  * /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/stylis/dist/stylis.mjs/index(.web.ts|.ts|.web.tsx|.tsx|.web.js|.js|.web.jsx|.jsx|.web.json|.json|.web.cjs|.cjs|.web.scss|.scss|.web.sass|.sass|.web.css|.css): /Users/<USER>/Documents/GitHub/lab/emotion-error-test/node_modules/metro/src/node-haste/DependencyGraph.js (289:17)\\n' +\n      //     '\\n' +\n      //     '\\x1B[0m \\x1B[90m 287 |\\x1B[39m         }\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 288 |\\x1B[39m         \\x1B[36mif\\x1B[39m (error \\x1B[36minstanceof\\x1B[39m \\x1B[33mInvalidPackageError\\x1B[39m) {\\x1B[0m\\n' +\n      //     '\\x1B[0m\\x1B[31m\\x1B[1m>\\x1B[22m\\x1B[39m\\x1B[90m 289 |\\x1B[39m           \\x1B[36mthrow\\x1B[39m \\x1B[36mnew\\x1B[39m \\x1B[33mPackageResolutionError\\x1B[39m({\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m     |\\x1B[39m                 \\x1B[31m\\x1B[1m^\\x1B[22m\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 290 |\\x1B[39m             packageError\\x1B[33m:\\x1B[39m error\\x1B[33m,\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 291 |\\x1B[39m             originModulePath\\x1B[33m:\\x1B[39m \\x1B[36mfrom\\x1B[39m\\x1B[33m,\\x1B[39m\\x1B[0m\\n' +\n      //     '\\x1B[0m \\x1B[90m 292 |\\x1B[39m             targetModuleName\\x1B[33m:\\x1B[39m to\\x1B[33m,\\x1B[39m\\x1B[0m'\n      // }\n      // The Metro logger already showed this error.\n      throw new Error(data.message);\n    }\n\n    throw new Error(\n      'Invalid resources returned from the Metro serializer. Expected array, found: ' + data\n    );\n  }\n\n  private async renderStaticErrorAsync(error: Error) {\n    return getErrorOverlayHtmlAsync({\n      error,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  async getStaticPageAsync(\n    pathname: string,\n    {\n      mode,\n      minify = mode !== 'development',\n    }: {\n      mode: 'development' | 'production';\n      minify?: boolean;\n    }\n  ) {\n    const devBundleUrlPathname = createBundleUrlPath({\n      platform: 'web',\n      mode,\n      environment: 'client',\n      mainModuleName: resolveMainModuleName(this.projectRoot, getConfig(this.projectRoot), 'web'),\n    });\n\n    const bundleStaticHtml = async (): Promise<string> => {\n      const { getStaticContent } = await getStaticRenderFunctions(\n        this.projectRoot,\n        this.getDevServerUrl()!,\n        {\n          minify: false,\n          dev: mode !== 'production',\n          // Ensure the API Routes are included\n          environment: 'node',\n        }\n      );\n\n      const location = new URL(pathname, this.getDevServerUrl()!);\n      return await getStaticContent(location);\n    };\n\n    const [resources, staticHtml] = await Promise.all([\n      this.getStaticResourcesAsync({ mode, minify }),\n      bundleStaticHtml(),\n    ]);\n    const content = await this.composeResourcesWithHtml({\n      mode,\n      resources,\n      template: staticHtml,\n      devBundleUrl: devBundleUrlPathname,\n    });\n    return {\n      content,\n      resources,\n    };\n  }\n\n  async watchEnvironmentVariables() {\n    if (!this.instance) {\n      throw new Error(\n        'Cannot observe environment variable changes without a running Metro instance.'\n      );\n    }\n    if (!this.metro) {\n      // This can happen when the run command is used and the server is already running in another\n      // process.\n      debug('Skipping Environment Variable observation because Metro is not running (headless).');\n      return;\n    }\n\n    const envFiles = runtimeEnv\n      .getFiles(process.env.NODE_ENV)\n      .map((fileName) => path.join(this.projectRoot, fileName));\n\n    observeFileChanges(\n      {\n        metro: this.metro,\n        server: this.instance.server,\n      },\n      envFiles,\n      () => {\n        debug('Reloading environment variables...');\n        // Force reload the environment variables.\n        runtimeEnv.load(this.projectRoot, { force: true });\n      }\n    );\n  }\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    options.port = await this.resolvePortAsync(options);\n    this.urlCreator = this.getUrlCreator(options);\n\n    const parsedOptions = {\n      port: options.port,\n      maxWorkers: options.maxWorkers,\n      resetCache: options.resetDevServer,\n\n      // Use the unversioned metro config.\n      // TODO: Deprecate this property when expo-cli goes away.\n      unversioned: false,\n    };\n\n    // Required for symbolication:\n    process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;\n\n    const { metro, server, middleware, messageSocket } = await instantiateMetroAsync(\n      this,\n      parsedOptions\n    );\n\n    const manifestMiddleware = await this.getManifestMiddlewareAsync(options);\n\n    // Important that we noop source maps for context modules as soon as possible.\n    prependMiddleware(middleware, new ContextModuleSourceMapsMiddleware().getHandler());\n\n    // We need the manifest handler to be the first middleware to run so our\n    // routes take precedence over static files. For example, the manifest is\n    // served from '/' and if the user has an index.html file in their project\n    // then the manifest handler will never run, the static middleware will run\n    // and serve index.html instead of the manifest.\n    // https://github.com/expo/expo/issues/13114\n    prependMiddleware(middleware, manifestMiddleware.getHandler());\n\n    middleware.use(\n      new InterstitialPageMiddleware(this.projectRoot, {\n        // TODO: Prevent this from becoming stale.\n        scheme: options.location.scheme ?? null,\n      }).getHandler()\n    );\n    middleware.use(new ReactDevToolsPageMiddleware(this.projectRoot).getHandler());\n\n    const deepLinkMiddleware = new RuntimeRedirectMiddleware(this.projectRoot, {\n      onDeepLink: getDeepLinkHandler(this.projectRoot),\n      getLocation: ({ runtime }) => {\n        if (runtime === 'custom') {\n          return this.urlCreator?.constructDevClientUrl();\n        } else {\n          return this.urlCreator?.constructUrl({\n            scheme: 'exp',\n          });\n        }\n      },\n    });\n    middleware.use(deepLinkMiddleware.getHandler());\n\n    middleware.use(new CreateFileMiddleware(this.projectRoot).getHandler());\n\n    // Append support for redirecting unhandled requests to the index.html page on web.\n    if (this.isTargetingWeb()) {\n      const { exp } = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n      const useWebSSG = exp.web?.output === 'static';\n\n      // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.\n      middleware.use(new ServeStaticMiddleware(this.projectRoot).getHandler());\n\n      // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.\n      middleware.use(new FaviconMiddleware(this.projectRoot).getHandler());\n\n      if (useWebSSG) {\n        middleware.use(async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n          if (!req?.url) {\n            return next();\n          }\n\n          // TODO: Formal manifest for allowed paths\n          if (req.url.endsWith('.ico')) {\n            return next();\n          }\n          if (req.url.includes('serializer.output=static')) {\n            return next();\n          }\n\n          try {\n            const { content } = await this.getStaticPageAsync(req.url, {\n              mode: options.mode ?? 'development',\n            });\n\n            res.setHeader('Content-Type', 'text/html');\n            res.end(content);\n            return;\n          } catch (error: any) {\n            res.setHeader('Content-Type', 'text/html');\n            try {\n              res.end(await this.renderStaticErrorAsync(error));\n            } catch (staticError: any) {\n              // Fallback error for when Expo Router is misconfigured in the project.\n              res.end(\n                '<span><h3>Internal Error:</h3><b>Project is not setup correctly for static rendering (check terminal for more info):</b><br/>' +\n                  error.message +\n                  '<br/><br/>' +\n                  staticError.message +\n                  '</span>'\n              );\n            }\n          }\n        });\n      }\n\n      // This MUST run last since it's the fallback.\n      if (!useWebSSG) {\n        middleware.use(\n          new HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler()\n        );\n      }\n    }\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        this.metro = null;\n        callback?.(err);\n      });\n    };\n\n    this.metro = metro;\n    return {\n      server,\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware,\n      messageSocket,\n    };\n  }\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    if (!this.instance) {\n      throw new Error('Cannot wait for TypeScript without a running server.');\n    }\n\n    return new Promise<boolean>((resolve) => {\n      if (!this.metro) {\n        // This can happen when the run command is used and the server is already running in another\n        // process. In this case we can't wait for the TypeScript check to complete because we don't\n        // have access to the Metro server.\n        debug('Skipping TypeScript check because Metro is not running (headless).');\n        return resolve(false);\n      }\n\n      const off = metroWatchTypeScriptFiles({\n        projectRoot: this.projectRoot,\n        server: this.instance!.server,\n        metro: this.metro,\n        tsconfig: true,\n        throttle: true,\n        eventTypes: ['change', 'add'],\n        callback: async () => {\n          // Run once, this prevents the TypeScript project prerequisite from running on every file change.\n          off();\n          const { TypeScriptProjectPrerequisite } = await import(\n            '../../doctor/typescript/TypeScriptProjectPrerequisite'\n          );\n\n          try {\n            const req = new TypeScriptProjectPrerequisite(this.projectRoot);\n            await req.bootstrapAsync();\n            resolve(true);\n          } catch (error: any) {\n            // Ensure the process doesn't fail if the TypeScript check fails.\n            // This could happen during the install.\n            Log.log();\n            Log.error(\n              chalk.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`\n            );\n            Log.exception(error);\n            resolve(false);\n          }\n        },\n      });\n    });\n  }\n\n  public async startTypeScriptServices() {\n    return startTypescriptTypeGenerationAsync({\n      server: this.instance?.server,\n      metro: this.metro,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./metro.config.js', './metro.config.json', './rn-cli.config.js'];\n  }\n}\n\nexport function getDeepLinkHandler(projectRoot: string): DeepLinkHandler {\n  return async ({ runtime }) => {\n    if (runtime === 'expo') return;\n    const { exp } = getConfig(projectRoot);\n    await logEventAsync('dev client start command', {\n      status: 'started',\n      ...getDevClientProperties(projectRoot, exp),\n    });\n  };\n}\n\nfunction htmlFromSerialAssets(\n  assets: SerialAsset[],\n  { dev, template, bundleUrl }: { dev: boolean; template: string; bundleUrl?: string }\n) {\n  // Combine the CSS modules into tags that have hot refresh data attributes.\n  const styleString = assets\n    .filter((asset) => asset.type === 'css')\n    .map(({ metadata, filename, source }) => {\n      if (dev) {\n        return `<style data-expo-css-hmr=\"${metadata.hmrId}\">` + source + '\\n</style>';\n      } else {\n        return [\n          `<link rel=\"preload\" href=\"/${filename}\" as=\"style\">`,\n          `<link rel=\"stylesheet\" href=\"/${filename}\">`,\n        ].join('');\n      }\n    })\n    .join('');\n\n  const jsAssets = assets.filter((asset) => asset.type === 'js');\n\n  const scripts = bundleUrl\n    ? `<script src=\"${bundleUrl}\" defer></script>`\n    : jsAssets\n        .map(({ filename }) => {\n          return `<script src=\"/${filename}\" defer></script>`;\n        })\n        .join('');\n\n  return template\n    .replace('</head>', `${styleString}</head>`)\n    .replace('</body>', `${scripts}\\n</body>`);\n}\n"], "names": ["getDeepLinkHandler", "runtimeEnv", "debug", "require", "EXPO_GO_METRO_PORT", "DEV_CLIENT_METRO_PORT", "MetroBundlerDevServer", "BundlerDevServer", "metro", "name", "resolvePortAsync", "options", "port", "devClient", "Number", "process", "env", "RCT_METRO_PORT", "getFreePortAsync", "getRoutesAsync", "url", "getDevServerUrl", "assert", "getManifest", "getStaticRenderFunctions", "projectRoot", "environment", "fetchData", "composeResourcesWithHtml", "mode", "resources", "template", "devBundleUrl", "isDev", "htmlFromSerialAssets", "dev", "bundleUrl", "undefined", "getStaticRenderFunctionAsync", "minify", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "URL", "getStaticResourcesAsync", "data", "devBundleUrlPathname", "createBundleUrlPath", "platform", "serializerOutput", "mainModuleName", "resolveMainModuleName", "getConfig", "results", "fetch", "toString", "txt", "text", "JSON", "parse", "error", "Log", "Array", "isArray", "errors", "type", "match", "Error", "message", "renderStaticErrorAsync", "getErrorOverlayHtmlAsync", "getStaticPageAsync", "pathname", "bundleStaticHtml", "location", "staticHtml", "Promise", "all", "content", "watchEnvironmentVariables", "instance", "envFiles", "getFiles", "NODE_ENV", "map", "fileName", "join", "observeFileChanges", "server", "load", "force", "startImplementationAsync", "urlCreator", "getUrlCreator", "parsedOptions", "maxWorkers", "resetCache", "resetDevServer", "unversioned", "EXPO_DEV_SERVER_ORIGIN", "middleware", "messageSocket", "instantiateMetroAsync", "manifestMiddleware", "getManifestMiddlewareAsync", "prependMiddleware", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "use", "InterstitialPageMiddleware", "scheme", "ReactDevToolsPageMiddleware", "deepLinkMiddleware", "RuntimeRedirectMiddleware", "onDeepLink", "getLocation", "runtime", "constructDevClientUrl", "constructUrl", "CreateFileMiddleware", "isTargetingWeb", "exp", "skipSDKVersionRequirement", "useWebSSG", "web", "output", "ServeStaticMiddleware", "FaviconMiddleware", "req", "res", "next", "endsWith", "includes", "<PERSON><PERSON><PERSON><PERSON>", "end", "staticError", "HistoryFallbackMiddleware", "internal", "originalClose", "close", "bind", "callback", "err", "host", "protocol", "waitForTypeScriptAsync", "resolve", "off", "metroWatchTypeScriptFiles", "tsconfig", "throttle", "eventTypes", "TypeScriptProjectPrerequisite", "bootstrapAsync", "log", "chalk", "red", "exception", "startTypeScriptServices", "startTypescriptTypeGenerationAsync", "getConfigModuleIds", "logEventAsync", "status", "getDevClientProperties", "assets", "styleString", "filter", "asset", "metadata", "filename", "source", "hmrId", "jsAssets", "scripts", "replace"], "mappings": "AAMA;;;;QA4dgBA,kBAAkB,GAAlBA,kBAAkB;AA5dR,IAAA,OAAc,WAAd,cAAc,CAAA;AACN,IAAA,UAAkB,WAAlB,kBAAkB,CAAA;AACxCC,IAAAA,UAAU,mCAAM,WAAW,EAAjB;AAEH,IAAA,OAAQ,kCAAR,QAAQ,EAAA;AACT,IAAA,MAAO,kCAAP,OAAO,EAAA;AACP,IAAA,UAAY,kCAAZ,YAAY,EAAA;AACb,IAAA,KAAM,kCAAN,MAAM,EAAA;AAEH,IAAA,IAAc,WAAd,cAAc,CAAA;AACC,IAAA,uBAAiD,kCAAjD,iDAAiD,EAAA;AACtD,IAAA,kBAA4C,WAA5C,4CAA4C,CAAA;AACzC,IAAA,KAAqB,WAArB,qBAAqB,CAAA;AACmB,IAAA,iBAAqB,WAArB,qBAAqB,CAAA;AACrD,IAAA,yBAA6B,WAA7B,6BAA6B,CAAA;AACpB,IAAA,kCAAiD,WAAjD,iDAAiD,CAAA;AAC9D,IAAA,qBAAoC,WAApC,oCAAoC,CAAA;AACvC,IAAA,kBAAiC,WAAjC,iCAAiC,CAAA;AACzB,IAAA,0BAAyC,WAAzC,yCAAyC,CAAA;AACxC,IAAA,2BAA0C,WAA1C,0CAA0C,CAAA;AAC1B,IAAA,mBAAkC,WAAlC,kCAAkC,CAAA;AACjD,IAAA,4BAA2C,WAA3C,2CAA2C,CAAA;AAIhF,IAAA,0BAAyC,WAAzC,yCAAyC,CAAA;AACV,IAAA,sBAAqC,WAArC,qCAAqC,CAAA;AAExB,IAAA,8BAAkD,WAAlD,kDAAkD,CAAA;AAC/D,IAAA,iBAAoB,WAApB,oBAAoB,CAAA;AACjB,IAAA,oBAAuB,WAAvB,uBAAuB,CAAA;AACtB,IAAA,0BAA6B,WAA7B,6BAA6B,CAAA;AACpC,IAAA,oCAAuC,WAAvC,uCAAuC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1E,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,uDAAuD,CACvD,MAAMC,kBAAkB,GAAG,IAAI,AAAC;AAEhC,mGAAmG,CACnG,MAAMC,qBAAqB,GAAG,IAAI,AAAC;AAE5B,MAAMC,qBAAqB,SAASC,iBAAgB,iBAAA;IACzD,AAAQC,KAAK,GAAkC,IAAI,CAAC;IAEpD,IAAIC,IAAI,GAAW;QACjB,OAAO,OAAO,CAAC;KAChB;IAED,MAAMC,gBAAgB,CAACC,OAAqC,GAAG,EAAE,EAAmB;YAEhF,yEAAyE;QACzEA,MAAY;QAFd,MAAMC,IAAI,GAERD,CAAAA,MAAY,GAAZA,OAAO,CAACC,IAAI,YAAZD,MAAY,GACZ,8DAA8D;QAC9D,CAACA,OAAO,CAACE,SAAS,GAEdC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,IAAIZ,qBAAqB,GAE3D,MAAMa,CAAAA,GAAAA,KAAgB,AAAoB,CAAA,iBAApB,CAACd,kBAAkB,CAAC,CAAC,AAAC;QAElD,OAAOQ,IAAI,CAAC;KACb;IAED,mCAAmC,CACnC,MAAMO,cAAc,GAAG;QACrB,MAAMC,GAAG,GAAG,IAAI,CAACC,eAAe,EAAE,AAAC;QACnCC,CAAAA,GAAAA,OAAM,AAAmC,CAAA,QAAnC,CAACF,GAAG,EAAE,4BAA4B,CAAC,CAAC;QAC1C,MAAM,EAAEG,WAAW,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,yBAAwB,AAGpD,CAAA,yBAHoD,CAAC,IAAI,CAACC,WAAW,EAAEL,GAAG,EAAE;YAC5E,qCAAqC;YACrCM,WAAW,EAAE,MAAM;SACpB,CAAC,AAAC;QAEH,OAAOH,WAAW,CAAC;YAAEI,SAAS,EAAE,IAAI;SAAE,CAAC,CAAC;KACzC;IAED,MAAMC,wBAAwB,CAAC,EAC7BC,IAAI,CAAA,EACJC,SAAS,CAAA,EACTC,QAAQ,CAAA,EACRC,YAAY,CAAA,EAMb,EAAmB;QAClB,IAAI,CAACF,SAAS,EAAE;YACd,OAAO,EAAE,CAAC;SACX;QACD,MAAMG,KAAK,GAAGJ,IAAI,KAAK,aAAa,AAAC;QACrC,OAAOK,oBAAoB,CAACJ,SAAS,EAAE;YACrCK,GAAG,EAAEF,KAAK;YACVF,QAAQ;YACRK,SAAS,EAAEH,KAAK,GAAGD,YAAY,GAAGK,SAAS;SAC5C,CAAC,CAAC;KACJ;IAED,MAAMC,4BAA4B,CAAC,EACjCT,IAAI,CAAA,EACJU,MAAM,EAAGV,IAAI,KAAK,aAAa,CAAA,EAIhC,EAAE;QACD,MAAMT,GAAG,GAAG,IAAI,CAACC,eAAe,EAAE,AAAC,AAAC;QAEpC,MAAM,EAAEmB,gBAAgB,CAAA,EAAE,GAAG,MAAMhB,CAAAA,GAAAA,yBAAwB,AAKzD,CAAA,yBALyD,CAAC,IAAI,CAACC,WAAW,EAAEL,GAAG,EAAE;YACjFmB,MAAM;YACNJ,GAAG,EAAEN,IAAI,KAAK,YAAY;YAC1B,qCAAqC;YACrCH,WAAW,EAAE,MAAM;SACpB,CAAC,AAAC;QACH,OAAO,OAAOe,IAAY,GAAK;YAC7B,OAAO,MAAMD,gBAAgB,CAAC,IAAIE,GAAG,CAACD,IAAI,EAAErB,GAAG,CAAC,CAAC,CAAC;SACnD,CAAC;KACH;IAED,MAAMuB,uBAAuB,CAAC,EAC5Bd,IAAI,CAAA,EACJU,MAAM,EAAGV,IAAI,KAAK,aAAa,CAAA,EAIhC,EAA0B;YAiCWe,GAAS;QAhC7C,MAAMC,oBAAoB,GAAGC,CAAAA,GAAAA,mBAAmB,AAO9C,CAAA,oBAP8C,CAAC;YAC/CC,QAAQ,EAAE,KAAK;YACflB,IAAI;YACJU,MAAM;YACNb,WAAW,EAAE,QAAQ;YACrBsB,gBAAgB,EAAE,QAAQ;YAC1BC,cAAc,EAAEC,CAAAA,GAAAA,mBAAqB,AAAsD,CAAA,sBAAtD,CAAC,IAAI,CAACzB,WAAW,EAAE0B,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAAC1B,WAAW,CAAC,EAAE,KAAK,CAAC;SAC5F,CAAC,AAAC;QAEH,MAAMW,SAAS,GAAG,IAAIM,GAAG,CAACG,oBAAoB,EAAE,IAAI,CAACxB,eAAe,EAAE,CAAE,AAAC;QAEzE,4DAA4D;QAC5D,MAAM+B,OAAO,GAAG,MAAMC,CAAAA,GAAAA,UAAK,AAAsB,CAAA,QAAtB,CAACjB,SAAS,CAACkB,QAAQ,EAAE,CAAC,AAAC;QAElD,MAAMC,GAAG,GAAG,MAAMH,OAAO,CAACI,IAAI,EAAE,AAAC;QAEjC,IAAIZ,IAAI,AAAK,AAAC;QACd,IAAI;YACFA,IAAI,GAAGa,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAAC;SACxB,CAAC,OAAOI,KAAK,EAAO;YACnBC,IAAG,IAAA,CAACD,KAAK,CACP,wMAAwM,CACzM,CAAC;YACFzD,KAAK,CAACqD,GAAG,CAAC,CAAC;YACX,MAAMI,KAAK,CAAC;SACb;QAED,mEAAmE;QACnE,IAAIE,KAAK,CAACC,OAAO,CAAClB,IAAI,CAAC,EAAE;YACvB,OAAOA,IAAI,CAAC;SACb;QAED,IAAIA,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,CAACmB,MAAM,KAAInB,CAAAA,GAAS,GAATA,IAAI,CAACoB,IAAI,SAAO,GAAhBpB,KAAAA,CAAgB,GAAhBA,GAAS,CAAEqB,KAAK,YAAY,CAAA,CAAC,EAAE;YACjE,IAAI;YACJ,2BAA2B;YAC3B,gBAAgB;YAChB,2jBAA2jB;YAC3jB,aAAa;YACb,8OAA8O;YAC9O,4WAA4W;YAC5W,aAAa;YACb,4DAA4D;YAC5D,sJAAsJ;YACtJ,8KAA8K;YAC9K,mGAAmG;YACnG,mHAAmH;YACnH,sIAAsI;YACtI,gHAAgH;YAChH,IAAI;YACJ,8CAA8C;YAC9C,MAAM,IAAIC,KAAK,CAACtB,IAAI,CAACuB,OAAO,CAAC,CAAC;SAC/B;QAED,MAAM,IAAID,KAAK,CACb,+EAA+E,GAAGtB,IAAI,CACvF,CAAC;KACH;IAED,MAAcwB,sBAAsB,CAACT,KAAY,EAAE;QACjD,OAAOU,CAAAA,GAAAA,oBAAwB,AAG7B,CAAA,yBAH6B,CAAC;YAC9BV,KAAK;YACLlC,WAAW,EAAE,IAAI,CAACA,WAAW;SAC9B,CAAC,CAAC;KACJ;IAED,MAAM6C,kBAAkB,CACtBC,QAAgB,EAChB,EACE1C,IAAI,CAAA,EACJU,MAAM,EAAGV,IAAI,KAAK,aAAa,CAAA,EAIhC,EACD;QACA,MAAMgB,oBAAoB,GAAGC,CAAAA,GAAAA,mBAAmB,AAK9C,CAAA,oBAL8C,CAAC;YAC/CC,QAAQ,EAAE,KAAK;YACflB,IAAI;YACJH,WAAW,EAAE,QAAQ;YACrBuB,cAAc,EAAEC,CAAAA,GAAAA,mBAAqB,AAAsD,CAAA,sBAAtD,CAAC,IAAI,CAACzB,WAAW,EAAE0B,CAAAA,GAAAA,OAAS,AAAkB,CAAA,UAAlB,CAAC,IAAI,CAAC1B,WAAW,CAAC,EAAE,KAAK,CAAC;SAC5F,CAAC,AAAC;QAEH,MAAM+C,gBAAgB,GAAG,UAA6B;YACpD,MAAM,EAAEhC,gBAAgB,CAAA,EAAE,GAAG,MAAMhB,CAAAA,GAAAA,yBAAwB,AAS1D,CAAA,yBAT0D,CACzD,IAAI,CAACC,WAAW,EAChB,IAAI,CAACJ,eAAe,EAAE,EACtB;gBACEkB,MAAM,EAAE,KAAK;gBACbJ,GAAG,EAAEN,IAAI,KAAK,YAAY;gBAC1B,qCAAqC;gBACrCH,WAAW,EAAE,MAAM;aACpB,CACF,AAAC;YAEF,MAAM+C,QAAQ,GAAG,IAAI/B,GAAG,CAAC6B,QAAQ,EAAE,IAAI,CAAClD,eAAe,EAAE,CAAE,AAAC;YAC5D,OAAO,MAAMmB,gBAAgB,CAACiC,QAAQ,CAAC,CAAC;SACzC,AAAC;QAEF,MAAM,CAAC3C,SAAS,EAAE4C,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;YAChD,IAAI,CAACjC,uBAAuB,CAAC;gBAAEd,IAAI;gBAAEU,MAAM;aAAE,CAAC;YAC9CiC,gBAAgB,EAAE;SACnB,CAAC,AAAC;QACH,MAAMK,OAAO,GAAG,MAAM,IAAI,CAACjD,wBAAwB,CAAC;YAClDC,IAAI;YACJC,SAAS;YACTC,QAAQ,EAAE2C,UAAU;YACpB1C,YAAY,EAAEa,oBAAoB;SACnC,CAAC,AAAC;QACH,OAAO;YACLgC,OAAO;YACP/C,SAAS;SACV,CAAC;KACH;IAED,MAAMgD,yBAAyB,GAAG;QAChC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,MAAM,IAAIb,KAAK,CACb,+EAA+E,CAChF,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC1D,KAAK,EAAE;YACf,4FAA4F;YAC5F,WAAW;YACXN,KAAK,CAAC,oFAAoF,CAAC,CAAC;YAC5F,OAAO;SACR;QAED,MAAM8E,QAAQ,GAAG/E,UAAU,CACxBgF,QAAQ,CAAClE,OAAO,CAACC,GAAG,CAACkE,QAAQ,CAAC,CAC9BC,GAAG,CAAC,CAACC,QAAQ,GAAK3C,KAAI,QAAA,CAAC4C,IAAI,CAAC,IAAI,CAAC5D,WAAW,EAAE2D,QAAQ,CAAC;QAAA,CAAC,AAAC;QAE5DE,CAAAA,GAAAA,oCAAkB,AAWjB,CAAA,mBAXiB,CAChB;YACE9E,KAAK,EAAE,IAAI,CAACA,KAAK;YACjB+E,MAAM,EAAE,IAAI,CAACR,QAAQ,CAACQ,MAAM;SAC7B,EACDP,QAAQ,EACR,IAAM;YACJ9E,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5C,0CAA0C;YAC1CD,UAAU,CAACuF,IAAI,CAAC,IAAI,CAAC/D,WAAW,EAAE;gBAAEgE,KAAK,EAAE,IAAI;aAAE,CAAC,CAAC;SACpD,CACF,CAAC;KACH;IAED,MAAgBC,wBAAwB,CACtC/E,OAA4B,EACA;QAC5BA,OAAO,CAACC,IAAI,GAAG,MAAM,IAAI,CAACF,gBAAgB,CAACC,OAAO,CAAC,CAAC;QACpD,IAAI,CAACgF,UAAU,GAAG,IAAI,CAACC,aAAa,CAACjF,OAAO,CAAC,CAAC;QAE9C,MAAMkF,aAAa,GAAG;YACpBjF,IAAI,EAAED,OAAO,CAACC,IAAI;YAClBkF,UAAU,EAAEnF,OAAO,CAACmF,UAAU;YAC9BC,UAAU,EAAEpF,OAAO,CAACqF,cAAc;YAElC,oCAAoC;YACpC,yDAAyD;YACzDC,WAAW,EAAE,KAAK;SACnB,AAAC;QAEF,8BAA8B;QAC9BlF,OAAO,CAACC,GAAG,CAACkF,sBAAsB,GAAG,CAAC,iBAAiB,EAAEvF,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;QAExE,MAAM,EAAEJ,KAAK,CAAA,EAAE+E,MAAM,CAAA,EAAEY,UAAU,CAAA,EAAEC,aAAa,CAAA,EAAE,GAAG,MAAMC,CAAAA,GAAAA,iBAAqB,AAG/E,CAAA,sBAH+E,CAC9E,IAAI,EACJR,aAAa,CACd,AAAC;QAEF,MAAMS,kBAAkB,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAAC5F,OAAO,CAAC,AAAC;QAE1E,8EAA8E;QAC9E6F,CAAAA,GAAAA,UAAiB,AAAkE,CAAA,kBAAlE,CAACL,UAAU,EAAE,IAAIM,kCAAiC,kCAAA,EAAE,CAACC,UAAU,EAAE,CAAC,CAAC;QAEpF,wEAAwE;QACxE,yEAAyE;QACzE,0EAA0E;QAC1E,2EAA2E;QAC3E,gDAAgD;QAChD,4CAA4C;QAC5CF,CAAAA,GAAAA,UAAiB,AAA6C,CAAA,kBAA7C,CAACL,UAAU,EAAEG,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC;YAKnD/F,OAAuB;QAHnCwF,UAAU,CAACQ,GAAG,CACZ,IAAIC,2BAA0B,2BAAA,CAAC,IAAI,CAACnF,WAAW,EAAE;YAC/C,0CAA0C;YAC1CoF,MAAM,EAAElG,CAAAA,OAAuB,GAAvBA,OAAO,CAAC8D,QAAQ,CAACoC,MAAM,YAAvBlG,OAAuB,GAAI,IAAI;SACxC,CAAC,CAAC+F,UAAU,EAAE,CAChB,CAAC;QACFP,UAAU,CAACQ,GAAG,CAAC,IAAIG,4BAA2B,4BAAA,CAAC,IAAI,CAACrF,WAAW,CAAC,CAACiF,UAAU,EAAE,CAAC,CAAC;QAE/E,MAAMK,kBAAkB,GAAG,IAAIC,0BAAyB,0BAAA,CAAC,IAAI,CAACvF,WAAW,EAAE;YACzEwF,UAAU,EAAEjH,kBAAkB,CAAC,IAAI,CAACyB,WAAW,CAAC;YAChDyF,WAAW,EAAE,CAAC,EAAEC,OAAO,CAAA,EAAE,GAAK;gBAC5B,IAAIA,OAAO,KAAK,QAAQ,EAAE;wBACjB,GAAe;oBAAtB,OAAO,CAAA,GAAe,GAAf,IAAI,CAACxB,UAAU,SAAuB,GAAtC,KAAA,CAAsC,GAAtC,GAAe,CAAEyB,qBAAqB,EAAE,CAAC;iBACjD,MAAM;wBACE,IAAe;oBAAtB,OAAO,CAAA,IAAe,GAAf,IAAI,CAACzB,UAAU,SAAc,GAA7B,KAAA,CAA6B,GAA7B,IAAe,CAAE0B,YAAY,CAAC;wBACnCR,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;iBACJ;aACF;SACF,CAAC,AAAC;QACHV,UAAU,CAACQ,GAAG,CAACI,kBAAkB,CAACL,UAAU,EAAE,CAAC,CAAC;QAEhDP,UAAU,CAACQ,GAAG,CAAC,IAAIW,qBAAoB,qBAAA,CAAC,IAAI,CAAC7F,WAAW,CAAC,CAACiF,UAAU,EAAE,CAAC,CAAC;QAExE,mFAAmF;QACnF,IAAI,IAAI,CAACa,cAAc,EAAE,EAAE;gBAEPC,IAAO;YADzB,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGrE,CAAAA,GAAAA,OAAS,AAAuD,CAAA,UAAvD,CAAC,IAAI,CAAC1B,WAAW,EAAE;gBAAEgG,yBAAyB,EAAE,IAAI;aAAE,CAAC,AAAC;YACjF,MAAMC,SAAS,GAAGF,CAAAA,CAAAA,IAAO,GAAPA,GAAG,CAACG,GAAG,SAAQ,GAAfH,KAAAA,CAAe,GAAfA,IAAO,CAAEI,MAAM,CAAA,KAAK,QAAQ,AAAC;YAE/C,oHAAoH;YACpHzB,UAAU,CAACQ,GAAG,CAAC,IAAIkB,sBAAqB,sBAAA,CAAC,IAAI,CAACpG,WAAW,CAAC,CAACiF,UAAU,EAAE,CAAC,CAAC;YAEzE,0GAA0G;YAC1GP,UAAU,CAACQ,GAAG,CAAC,IAAImB,kBAAiB,kBAAA,CAAC,IAAI,CAACrG,WAAW,CAAC,CAACiF,UAAU,EAAE,CAAC,CAAC;YAErE,IAAIgB,SAAS,EAAE;gBACbvB,UAAU,CAACQ,GAAG,CAAC,OAAOoB,GAAkB,EAAEC,GAAmB,EAAEC,IAAgB,GAAK;oBAClF,IAAI,CAACF,CAAAA,GAAG,QAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAE3G,GAAG,CAAA,EAAE;wBACb,OAAO6G,IAAI,EAAE,CAAC;qBACf;oBAED,0CAA0C;oBAC1C,IAAIF,GAAG,CAAC3G,GAAG,CAAC8G,QAAQ,CAAC,MAAM,CAAC,EAAE;wBAC5B,OAAOD,IAAI,EAAE,CAAC;qBACf;oBACD,IAAIF,GAAG,CAAC3G,GAAG,CAAC+G,QAAQ,CAAC,0BAA0B,CAAC,EAAE;wBAChD,OAAOF,IAAI,EAAE,CAAC;qBACf;oBAED,IAAI;4BAEMtH,KAAY;wBADpB,MAAM,EAAEkE,OAAO,CAAA,EAAE,GAAG,MAAM,IAAI,CAACP,kBAAkB,CAACyD,GAAG,CAAC3G,GAAG,EAAE;4BACzDS,IAAI,EAAElB,CAAAA,KAAY,GAAZA,OAAO,CAACkB,IAAI,YAAZlB,KAAY,GAAI,aAAa;yBACpC,CAAC,AAAC;wBAEHqH,GAAG,CAACI,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;wBAC3CJ,GAAG,CAACK,GAAG,CAACxD,OAAO,CAAC,CAAC;wBACjB,OAAO;qBACR,CAAC,OAAOlB,KAAK,EAAO;wBACnBqE,GAAG,CAACI,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;wBAC3C,IAAI;4BACFJ,GAAG,CAACK,GAAG,CAAC,MAAM,IAAI,CAACjE,sBAAsB,CAACT,KAAK,CAAC,CAAC,CAAC;yBACnD,CAAC,OAAO2E,WAAW,EAAO;4BACzB,uEAAuE;4BACvEN,GAAG,CAACK,GAAG,CACL,+HAA+H,GAC7H1E,KAAK,CAACQ,OAAO,GACb,YAAY,GACZmE,WAAW,CAACnE,OAAO,GACnB,SAAS,CACZ,CAAC;yBACH;qBACF;iBACF,CAAC,CAAC;aACJ;YAED,8CAA8C;YAC9C,IAAI,CAACuD,SAAS,EAAE;gBACdvB,UAAU,CAACQ,GAAG,CACZ,IAAI4B,0BAAyB,0BAAA,CAACjC,kBAAkB,CAACI,UAAU,EAAE,CAAC8B,QAAQ,CAAC,CAAC9B,UAAU,EAAE,CACrF,CAAC;aACH;SACF;QACD,qEAAqE;QACrE,MAAM+B,aAAa,GAAGlD,MAAM,CAACmD,KAAK,CAACC,IAAI,CAACpD,MAAM,CAAC,AAAC;QAEhDA,MAAM,CAACmD,KAAK,GAAG,CAACE,QAAgC,GAAK;YACnD,OAAOH,aAAa,CAAC,CAACI,GAAW,GAAK;gBACpC,IAAI,CAAC9D,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAACvE,KAAK,GAAG,IAAI,CAAC;gBAClBoI,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAGC,GAAG,CAAC,AAjZvB,CAiZwB;aACjB,CAAC,CAAC;SACJ,CAAC;QAEF,IAAI,CAACrI,KAAK,GAAGA,KAAK,CAAC;QACnB,OAAO;YACL+E,MAAM;YACNd,QAAQ,EAAE;gBACR,mDAAmD;gBACnD7D,IAAI,EAAED,OAAO,CAACC,IAAI;gBAClB,kCAAkC;gBAClCkI,IAAI,EAAE,WAAW;gBACjB,iDAAiD;gBACjD1H,GAAG,EAAE,CAAC,iBAAiB,EAAET,OAAO,CAACC,IAAI,CAAC,CAAC;gBACvCmI,QAAQ,EAAE,MAAM;aACjB;YACD5C,UAAU;YACVC,aAAa;SACd,CAAC;KACH;IAED,MAAa4C,sBAAsB,GAAqB;QACtD,IAAI,CAAC,IAAI,CAACjE,QAAQ,EAAE;YAClB,MAAM,IAAIb,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACzE;QAED,OAAO,IAAIS,OAAO,CAAU,CAACsE,OAAO,GAAK;YACvC,IAAI,CAAC,IAAI,CAACzI,KAAK,EAAE;gBACf,4FAA4F;gBAC5F,4FAA4F;gBAC5F,mCAAmC;gBACnCN,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBAC5E,OAAO+I,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;YAED,MAAMC,GAAG,GAAGC,CAAAA,GAAAA,0BAAyB,AA6BnC,CAAA,0BA7BmC,CAAC;gBACpC1H,WAAW,EAAE,IAAI,CAACA,WAAW;gBAC7B8D,MAAM,EAAE,IAAI,CAACR,QAAQ,CAAEQ,MAAM;gBAC7B/E,KAAK,EAAE,IAAI,CAACA,KAAK;gBACjB4I,QAAQ,EAAE,IAAI;gBACdC,QAAQ,EAAE,IAAI;gBACdC,UAAU,EAAE;oBAAC,QAAQ;oBAAE,KAAK;iBAAC;gBAC7BV,QAAQ,EAAE,UAAY;oBACpB,iGAAiG;oBACjGM,GAAG,EAAE,CAAC;oBACN,MAAM,EAAEK,6BAA6B,CAAA,EAAE,GAAG,MAAM;+DAC9C,uDAAuD;sBACxD,AAAC;oBAEF,IAAI;wBACF,MAAMxB,GAAG,GAAG,IAAIwB,6BAA6B,CAAC,IAAI,CAAC9H,WAAW,CAAC,AAAC;wBAChE,MAAMsG,GAAG,CAACyB,cAAc,EAAE,CAAC;wBAC3BP,OAAO,CAAC,IAAI,CAAC,CAAC;qBACf,CAAC,OAAOtF,KAAK,EAAO;wBACnB,iEAAiE;wBACjE,wCAAwC;wBACxCC,IAAG,IAAA,CAAC6F,GAAG,EAAE,CAAC;wBACV7F,IAAG,IAAA,CAACD,KAAK,CACP+F,MAAK,QAAA,CAACC,GAAG,CAAC,gGAAgG,CAAC,CAC5G,CAAC;wBACF/F,IAAG,IAAA,CAACgG,SAAS,CAACjG,KAAK,CAAC,CAAC;wBACrBsF,OAAO,CAAC,KAAK,CAAC,CAAC;qBAChB;iBACF;aACF,CAAC,AAAC;SACJ,CAAC,CAAC;KACJ;IAED,MAAaY,uBAAuB,GAAG;YAE3B,GAAa;QADvB,OAAOC,CAAAA,GAAAA,8BAAkC,AAIvC,CAAA,mCAJuC,CAAC;YACxCvE,MAAM,EAAE,CAAA,GAAa,GAAb,IAAI,CAACR,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEQ,MAAM;YAC7B/E,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBiB,WAAW,EAAE,IAAI,CAACA,WAAW;SAC9B,CAAC,CAAC;KACJ;IAED,AAAUsI,kBAAkB,GAAa;QACvC,OAAO;YAAC,mBAAmB;YAAE,qBAAqB;YAAE,oBAAoB;SAAC,CAAC;KAC3E;CACF;QAhbYzJ,qBAAqB,GAArBA,qBAAqB;AAkb3B,SAASN,kBAAkB,CAACyB,WAAmB,EAAmB;IACvE,OAAO,OAAO,EAAE0F,OAAO,CAAA,EAAE,GAAK;QAC5B,IAAIA,OAAO,KAAK,MAAM,EAAE,OAAO;QAC/B,MAAM,EAAEK,GAAG,CAAA,EAAE,GAAGrE,CAAAA,GAAAA,OAAS,AAAa,CAAA,UAAb,CAAC1B,WAAW,CAAC,AAAC;QACvC,MAAMuI,CAAAA,GAAAA,kBAAa,AAGjB,CAAA,cAHiB,CAAC,0BAA0B,EAAE;YAC9CC,MAAM,EAAE,SAAS;YACjB,GAAGC,CAAAA,GAAAA,uBAAsB,AAAkB,CAAA,QAAlB,CAACzI,WAAW,EAAE+F,GAAG,CAAC;SAC5C,CAAC,CAAC;KACJ,CAAC;CACH;AAED,SAAStF,oBAAoB,CAC3BiI,MAAqB,EACrB,EAAEhI,GAAG,CAAA,EAAEJ,QAAQ,CAAA,EAAEK,SAAS,CAAA,EAA0D,EACpF;IACA,2EAA2E;IAC3E,MAAMgI,WAAW,GAAGD,MAAM,CACvBE,MAAM,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACtG,IAAI,KAAK,KAAK;IAAA,CAAC,CACvCmB,GAAG,CAAC,CAAC,EAAEoF,QAAQ,CAAA,EAAEC,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAK;QACvC,IAAItI,GAAG,EAAE;YACP,OAAO,CAAC,0BAA0B,EAAEoI,QAAQ,CAACG,KAAK,CAAC,EAAE,CAAC,GAAGD,MAAM,GAAG,YAAY,CAAC;SAChF,MAAM;YACL,OAAO;gBACL,CAAC,2BAA2B,EAAED,QAAQ,CAAC,aAAa,CAAC;gBACrD,CAAC,8BAA8B,EAAEA,QAAQ,CAAC,EAAE,CAAC;aAC9C,CAACnF,IAAI,CAAC,EAAE,CAAC,CAAC;SACZ;KACF,CAAC,CACDA,IAAI,CAAC,EAAE,CAAC,AAAC;IAEZ,MAAMsF,QAAQ,GAAGR,MAAM,CAACE,MAAM,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACtG,IAAI,KAAK,IAAI;IAAA,CAAC,AAAC;IAE/D,MAAM4G,OAAO,GAAGxI,SAAS,GACrB,CAAC,aAAa,EAAEA,SAAS,CAAC,iBAAiB,CAAC,GAC5CuI,QAAQ,CACLxF,GAAG,CAAC,CAAC,EAAEqF,QAAQ,CAAA,EAAE,GAAK;QACrB,OAAO,CAAC,cAAc,EAAEA,QAAQ,CAAC,iBAAiB,CAAC,CAAC;KACrD,CAAC,CACDnF,IAAI,CAAC,EAAE,CAAC,AAAC;IAEhB,OAAOtD,QAAQ,CACZ8I,OAAO,CAAC,SAAS,EAAE,CAAC,EAAET,WAAW,CAAC,OAAO,CAAC,CAAC,CAC3CS,OAAO,CAAC,SAAS,EAAE,CAAC,EAAED,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;CAC9C"}