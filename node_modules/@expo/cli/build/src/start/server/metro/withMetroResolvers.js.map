{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroResolvers.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ConfigT as MetroConfig } from 'metro-config';\nimport { ResolutionContext } from 'metro-resolver';\n\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { importMetroResolverFromProject } from './resolveFromProject';\n\nconst debug = require('debug')('expo:metro:withMetroResolvers') as typeof console.log;\n\nexport type MetroResolver = NonNullable<MetroConfig['resolver']['resolveRequest']>;\n\n/** Expo Metro Resolvers can return `null` to skip without throwing an error. Metro Resolvers will throw either a `FailedToResolveNameError` or `FailedToResolvePathError`. */\nexport type ExpoCustomMetroResolver = (\n  ...args: Parameters<MetroResolver>\n) => ReturnType<MetroResolver> | null;\n\n/** @returns `MetroResolver` utilizing the upstream `resolve` method. */\nexport function getDefaultMetroResolver(projectRoot: string): MetroResolver {\n  const { resolve } = importMetroResolverFromProject(projectRoot);\n  return (context: ResolutionContext, moduleName: string, platform: string | null) => {\n    return resolve(context, moduleName, platform);\n  };\n}\n\n/**\n * Extend the Metro config `resolver.resolveRequest` method with additional resolvers that can\n * exit early by returning a `Resolution` or skip to the next resolver by returning `null`.\n *\n * @param config Metro config.\n * @param projectRoot path to the project root used to resolve the default Metro resolver.\n * @param resolvers custom MetroResolver to chain.\n * @returns a new `MetroConfig` with the `resolver.resolveRequest` method chained.\n */\nexport function withMetroResolvers(\n  config: MetroConfig,\n  projectRoot: string,\n  resolvers: ExpoCustomMetroResolver[]\n): MetroConfig {\n  debug(\n    `Appending ${\n      resolvers.length\n    } custom resolvers to Metro config. (has custom resolver: ${!!config.resolver.resolveRequest})`\n  );\n  const originalResolveRequest =\n    config.resolver.resolveRequest || getDefaultMetroResolver(projectRoot);\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const universalContext = {\n          ...context,\n          preferNativePlatform: platform !== 'web',\n        };\n\n        for (const resolver of resolvers) {\n          try {\n            const resolution = resolver(universalContext, moduleName, platform);\n            if (resolution) {\n              return resolution;\n            }\n          } catch (error: any) {\n            // If no user-defined resolver, use Expo's default behavior.\n            // This prevents extraneous resolution attempts on failure.\n            if (!config.resolver.resolveRequest) {\n              throw error;\n            }\n\n            // If the error is directly related to a resolver not being able to resolve a module, then\n            // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n            const isResolutionError =\n              isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n            if (!isResolutionError) {\n              throw error;\n            }\n            debug(\n              `Custom resolver threw: ${error.constructor.name}. (module: ${moduleName}, platform: ${platform})`\n            );\n          }\n        }\n        // If we haven't returned by now, use the original resolver or upstream resolver.\n        return originalResolveRequest(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n"], "names": ["getDefaultMetroResolver", "withMetroResolvers", "debug", "require", "projectRoot", "resolve", "importMetroResolverFromProject", "context", "moduleName", "platform", "config", "resolvers", "length", "resolver", "resolveRequest", "originalResolveRequest", "universalContext", "preferNativePlatform", "resolution", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "constructor", "name"], "mappings": "AAMA;;;;QAgBgBA,uBAAuB,GAAvBA,uBAAuB;QAgBvBC,kBAAkB,GAAlBA,kBAAkB;AA7BqC,IAAA,YAAe,WAAf,eAAe,CAAA;AACvC,IAAA,mBAAsB,WAAtB,sBAAsB,CAAA;AAErE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,AAAsB,AAAC;AAU/E,SAASH,uBAAuB,CAACI,WAAmB,EAAiB;IAC1E,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAGC,CAAAA,GAAAA,mBAA8B,AAAa,CAAA,+BAAb,CAACF,WAAW,CAAC,AAAC;IAChE,OAAO,CAACG,OAA0B,EAAEC,UAAkB,EAAEC,QAAuB,GAAK;QAClF,OAAOJ,OAAO,CAACE,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAAC;KAC/C,CAAC;CACH;AAWM,SAASR,kBAAkB,CAChCS,MAAmB,EACnBN,WAAmB,EACnBO,SAAoC,EACvB;IACbT,KAAK,CACH,CAAC,UAAU,EACTS,SAAS,CAACC,MAAM,CACjB,yDAAyD,EAAE,CAAC,CAACF,MAAM,CAACG,QAAQ,CAACC,cAAc,CAAC,CAAC,CAAC,CAChG,CAAC;IACF,MAAMC,sBAAsB,GAC1BL,MAAM,CAACG,QAAQ,CAACC,cAAc,IAAId,uBAAuB,CAACI,WAAW,CAAC,AAAC;IAEzE,OAAO;QACL,GAAGM,MAAM;QACTG,QAAQ,EAAE;YACR,GAAGH,MAAM,CAACG,QAAQ;YAClBC,cAAc,EAACP,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;gBAC5C,MAAMO,gBAAgB,GAAG;oBACvB,GAAGT,OAAO;oBACVU,oBAAoB,EAAER,QAAQ,KAAK,KAAK;iBACzC,AAAC;gBAEF,KAAK,MAAMI,QAAQ,IAAIF,SAAS,CAAE;oBAChC,IAAI;wBACF,MAAMO,UAAU,GAAGL,QAAQ,CAACG,gBAAgB,EAAER,UAAU,EAAEC,QAAQ,CAAC,AAAC;wBACpE,IAAIS,UAAU,EAAE;4BACd,OAAOA,UAAU,CAAC;yBACnB;qBACF,CAAC,OAAOC,KAAK,EAAO;wBACnB,4DAA4D;wBAC5D,2DAA2D;wBAC3D,IAAI,CAACT,MAAM,CAACG,QAAQ,CAACC,cAAc,EAAE;4BACnC,MAAMK,KAAK,CAAC;yBACb;wBAED,0FAA0F;wBAC1F,2FAA2F;wBAC3F,MAAMC,iBAAiB,GACrBC,CAAAA,GAAAA,YAA0B,AAAO,CAAA,2BAAP,CAACF,KAAK,CAAC,IAAIG,CAAAA,GAAAA,YAA0B,AAAO,CAAA,2BAAP,CAACH,KAAK,CAAC,AAAC;wBACzE,IAAI,CAACC,iBAAiB,EAAE;4BACtB,MAAMD,KAAK,CAAC;yBACb;wBACDjB,KAAK,CACH,CAAC,uBAAuB,EAAEiB,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,WAAW,EAAEhB,UAAU,CAAC,YAAY,EAAEC,QAAQ,CAAC,CAAC,CAAC,CACnG,CAAC;qBACH;iBACF;gBACD,iFAAiF;gBACjF,OAAOM,sBAAsB,CAACC,gBAAgB,EAAER,UAAU,EAAEC,QAAQ,CAAC,CAAC;aACvE;SACF;KACF,CAAC;CACH"}