{"version": 3, "names": ["packageName", "label", "isRequired", "description", "getDiagnostics", "needsToBeFixed", "isSoftwareNotInstalled", "runAutomaticFix", "loader", "logManualInstallation", "brewInstall", "pkg", "onSuccess", "succeed", "chalk", "bold", "onFail", "fail", "healthcheck", "url"], "sources": ["../../../src/tools/healthchecks/iosDeploy.ts"], "sourcesContent": ["import {isSoftwareNotInstalled} from '../checkInstallation';\nimport {HealthCheckInterface} from '../../types';\nimport {brewInstall} from '../brewInstall';\nimport chalk from 'chalk';\n\nconst packageName = 'ios-deploy';\n\nexport default {\n  label: packageName,\n  isRequired: false,\n  description:\n    'Required for installing your app on a physical device with the CLI',\n  getDiagnostics: async () => ({\n    needsToBeFixed: await isSoftwareNotInstalled(packageName),\n  }),\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    await brewInstall({\n      pkg: packageName,\n      label: packageName,\n      loader,\n      onSuccess: () => {\n        loader.succeed(\n          `Successfully installed ${chalk.bold(packageName)} with Homebrew`,\n        );\n      },\n      onFail: () => {\n        loader.fail();\n        logManualInstallation({\n          healthcheck: packageName,\n          url: 'https://github.com/ios-control/ios-deploy#installation',\n        });\n      },\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;AAEA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,MAAMA,WAAW,GAAG,YAAY;AAAC,eAElB;EACbC,KAAK,EAAED,WAAW;EAClBE,UAAU,EAAE,KAAK;EACjBC,WAAW,EACT,oEAAoE;EACtEC,cAAc,EAAE,aAAa;IAC3BC,cAAc,EAAE,MAAM,IAAAC,yCAAsB,EAACN,WAAW;EAC1D,CAAC,CAAC;EACFO,eAAe,EAAE,OAAO;IAACC,MAAM;IAAEC;EAAqB,CAAC,KAAK;IAC1D,MAAM,IAAAC,wBAAW,EAAC;MAChBC,GAAG,EAAEX,WAAW;MAChBC,KAAK,EAAED,WAAW;MAClBQ,MAAM;MACNI,SAAS,EAAE,MAAM;QACfJ,MAAM,CAACK,OAAO,CACX,0BAAyBC,gBAAK,CAACC,IAAI,CAACf,WAAW,CAAE,gBAAe,CAClE;MACH,CAAC;MACDgB,MAAM,EAAE,MAAM;QACZR,MAAM,CAACS,IAAI,EAAE;QACbR,qBAAqB,CAAC;UACpBS,WAAW,EAAElB,WAAW;UACxBmB,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}