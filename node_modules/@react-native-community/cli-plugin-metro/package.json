{"name": "@react-native-community/cli-plugin-metro", "version": "11.3.7", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-server-api": "11.3.7", "@react-native-community/cli-tools": "11.3.7", "chalk": "^4.1.2", "execa": "^5.0.0", "metro": "0.76.8", "metro-config": "0.76.8", "metro-core": "0.76.8", "metro-react-native-babel-transformer": "0.76.8", "metro-resolver": "0.76.8", "metro-runtime": "0.76.8", "readline": "^1.3.0"}, "devDependencies": {"@react-native-community/cli-types": "11.3.7"}, "files": ["build", "!*.map"], "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-plugin-metro", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-plugin-metro"}, "gitHead": "7a0e1772eb3ff6d8a0c4368893de0ce786f9daae"}