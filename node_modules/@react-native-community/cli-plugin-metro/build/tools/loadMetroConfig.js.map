{"version": 3, "names": ["getOverrideConfig", "ctx", "outOfTreePlatforms", "Object", "keys", "platforms", "filter", "platform", "npmPackageName", "resolver", "length", "resolveRequest", "reactNativePlatformResolver", "reduce", "result", "serializer", "getModulesRunBeforeMainModule", "require", "resolve", "path", "join", "reactNativePath", "map", "paths", "root", "loadMetroConfig", "options", "overrideConfig", "reporter", "cwd", "projectConfig", "resolveConfig", "config", "isEmpty", "CLIError", "logger", "debug", "filepath", "global", "__REACT_NATIVE_METRO_CONFIG_LOADED", "test", "fs", "readFileSync", "warn", "loadedConfig", "loadConfig", "getDefaultMetroConfig", "mergeConfig"], "sources": ["../../src/tools/loadMetroConfig.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport {\n  ConfigT,\n  InputConfigT,\n  loadConfig,\n  mergeConfig,\n  resolveConfig,\n  ResolverConfigT,\n} from 'metro-config';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\nimport type {Config} from '@react-native-community/cli-types';\nimport getDefaultMetroConfig from './getDefaultMetroConfig';\nimport {reactNativePlatformResolver} from './metroPlatformResolver';\n\nexport type {Config};\n\nexport type ConfigLoadingContext = Pick<\n  Config,\n  'root' | 'reactNativePath' | 'platforms'\n>;\n\ndeclare global {\n  var __REACT_NATIVE_METRO_CONFIG_LOADED: boolean;\n}\n\n/**\n * Get the config options to override based on RN CLI inputs.\n */\nfunction getOverrideConfig(ctx: ConfigLoadingContext): InputConfigT {\n  const outOfTreePlatforms = Object.keys(ctx.platforms).filter(\n    (platform) => ctx.platforms[platform].npmPackageName,\n  );\n  const resolver: Partial<ResolverConfigT> = {\n    platforms: [...Object.keys(ctx.platforms), 'native'],\n  };\n\n  if (outOfTreePlatforms.length) {\n    resolver.resolveRequest = reactNativePlatformResolver(\n      outOfTreePlatforms.reduce<{[platform: string]: string}>(\n        (result, platform) => {\n          result[platform] = ctx.platforms[platform].npmPackageName!;\n          return result;\n        },\n        {},\n      ),\n    );\n  }\n\n  return {\n    resolver,\n    serializer: {\n      // We can include multiple copies of InitializeCore here because metro will\n      // only add ones that are already part of the bundle\n      getModulesRunBeforeMainModule: () => [\n        require.resolve(\n          path.join(ctx.reactNativePath, 'Libraries/Core/InitializeCore'),\n        ),\n        ...outOfTreePlatforms.map((platform) =>\n          require.resolve(\n            `${ctx.platforms[platform]\n              .npmPackageName!}/Libraries/Core/InitializeCore`,\n            {paths: [ctx.root]},\n          ),\n        ),\n      ],\n    },\n  };\n}\n\nexport interface ConfigOptionsT {\n  maxWorkers?: number;\n  port?: number;\n  projectRoot?: string;\n  resetCache?: boolean;\n  watchFolders?: string[];\n  sourceExts?: string[];\n  reporter?: any;\n  config?: string;\n}\n\n/**\n * Load Metro config.\n *\n * Allows the CLI to override select values in `metro.config.js` based on\n * dynamic user options in `ctx`.\n */\nexport default async function loadMetroConfig(\n  ctx: ConfigLoadingContext,\n  options: ConfigOptionsT = {},\n): Promise<ConfigT> {\n  const overrideConfig = getOverrideConfig(ctx);\n  if (options.reporter) {\n    overrideConfig.reporter = options.reporter;\n  }\n\n  const cwd = ctx.root;\n  const projectConfig = await resolveConfig(options.config, cwd);\n\n  if (projectConfig.isEmpty) {\n    throw new CLIError(`No Metro config found in ${cwd}`);\n  }\n\n  logger.debug(`Reading Metro config from ${projectConfig.filepath}`);\n\n  if (\n    !global.__REACT_NATIVE_METRO_CONFIG_LOADED &&\n    // TODO(huntie): Remove this check from 0.73 onwards (all users will be on\n    // the next major @react-native/metro-config version)\n    !/['\"']@react-native\\/metro-config['\"']/.test(\n      fs.readFileSync(projectConfig.filepath, 'utf8'),\n    )\n  ) {\n    logger.warn(\n      'From React Native 0.72, your metro.config.js file should extend' +\n        \"'@react-native/metro-config'. Please see the React Native 0.72 \" +\n        'changelog, or copy the template at:\\n' +\n        'https://github.com/facebook/react-native/blob/main/packages/react-native/template/metro.config.js',\n    );\n    logger.warn('Falling back to internal defaults.');\n\n    const loadedConfig = await loadConfig(\n      {cwd: ctx.root, ...options},\n      // Provide React Native defaults on top of Metro defaults\n      getDefaultMetroConfig(ctx),\n    );\n\n    return mergeConfig(loadedConfig, overrideConfig);\n  }\n\n  return mergeConfig(\n    await loadConfig({\n      cwd,\n      ...options,\n    }),\n    overrideConfig,\n  );\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AAAoE;AAapE;AACA;AACA;AACA,SAASA,iBAAiB,CAACC,GAAyB,EAAgB;EAClE,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,SAAS,CAAC,CAACC,MAAM,CACzDC,QAAQ,IAAKN,GAAG,CAACI,SAAS,CAACE,QAAQ,CAAC,CAACC,cAAc,CACrD;EACD,MAAMC,QAAkC,GAAG;IACzCJ,SAAS,EAAE,CAAC,GAAGF,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,SAAS,CAAC,EAAE,QAAQ;EACrD,CAAC;EAED,IAAIH,kBAAkB,CAACQ,MAAM,EAAE;IAC7BD,QAAQ,CAACE,cAAc,GAAG,IAAAC,kDAA2B,EACnDV,kBAAkB,CAACW,MAAM,CACvB,CAACC,MAAM,EAAEP,QAAQ,KAAK;MACpBO,MAAM,CAACP,QAAQ,CAAC,GAAGN,GAAG,CAACI,SAAS,CAACE,QAAQ,CAAC,CAACC,cAAe;MAC1D,OAAOM,MAAM;IACf,CAAC,EACD,CAAC,CAAC,CACH,CACF;EACH;EAEA,OAAO;IACLL,QAAQ;IACRM,UAAU,EAAE;MACV;MACA;MACAC,6BAA6B,EAAE,MAAM,CACnCC,OAAO,CAACC,OAAO,CACbC,eAAI,CAACC,IAAI,CAACnB,GAAG,CAACoB,eAAe,EAAE,+BAA+B,CAAC,CAChE,EACD,GAAGnB,kBAAkB,CAACoB,GAAG,CAAEf,QAAQ,IACjCU,OAAO,CAACC,OAAO,CACZ,GAAEjB,GAAG,CAACI,SAAS,CAACE,QAAQ,CAAC,CACvBC,cAAgB,gCAA+B,EAClD;QAACe,KAAK,EAAE,CAACtB,GAAG,CAACuB,IAAI;MAAC,CAAC,CACpB,CACF;IAEL;EACF,CAAC;AACH;AAaA;AACA;AACA;AACA;AACA;AACA;AACe,eAAeC,eAAe,CAC3CxB,GAAyB,EACzByB,OAAuB,GAAG,CAAC,CAAC,EACV;EAClB,MAAMC,cAAc,GAAG3B,iBAAiB,CAACC,GAAG,CAAC;EAC7C,IAAIyB,OAAO,CAACE,QAAQ,EAAE;IACpBD,cAAc,CAACC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;EAC5C;EAEA,MAAMC,GAAG,GAAG5B,GAAG,CAACuB,IAAI;EACpB,MAAMM,aAAa,GAAG,MAAM,IAAAC,4BAAa,EAACL,OAAO,CAACM,MAAM,EAAEH,GAAG,CAAC;EAE9D,IAAIC,aAAa,CAACG,OAAO,EAAE;IACzB,MAAM,KAAIC,oBAAQ,EAAE,4BAA2BL,GAAI,EAAC,CAAC;EACvD;EAEAM,kBAAM,CAACC,KAAK,CAAE,6BAA4BN,aAAa,CAACO,QAAS,EAAC,CAAC;EAEnE,IACE,CAACC,MAAM,CAACC,kCAAkC;EAC1C;EACA;EACA,CAAC,uCAAuC,CAACC,IAAI,CAC3CC,aAAE,CAACC,YAAY,CAACZ,aAAa,CAACO,QAAQ,EAAE,MAAM,CAAC,CAChD,EACD;IACAF,kBAAM,CAACQ,IAAI,CACT,iEAAiE,GAC/D,iEAAiE,GACjE,uCAAuC,GACvC,mGAAmG,CACtG;IACDR,kBAAM,CAACQ,IAAI,CAAC,oCAAoC,CAAC;IAEjD,MAAMC,YAAY,GAAG,MAAM,IAAAC,yBAAU,EACnC;MAAChB,GAAG,EAAE5B,GAAG,CAACuB,IAAI;MAAE,GAAGE;IAAO,CAAC;IAC3B;IACA,IAAAoB,8BAAqB,EAAC7C,GAAG,CAAC,CAC3B;IAED,OAAO,IAAA8C,0BAAW,EAACH,YAAY,EAAEjB,cAAc,CAAC;EAClD;EAEA,OAAO,IAAAoB,0BAAW,EAChB,MAAM,IAAAF,yBAAU,EAAC;IACfhB,GAAG;IACH,GAAGH;EACL,CAAC,CAAC,EACFC,cAAc,CACf;AACH"}