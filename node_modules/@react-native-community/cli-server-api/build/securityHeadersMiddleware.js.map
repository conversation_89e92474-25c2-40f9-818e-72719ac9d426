{"version": 3, "names": ["securityHeadersMiddleware", "req", "res", "next", "headers", "origin", "match", "startsWith", "Error", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/securityHeadersMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\n\nexport default function securityHeadersMiddleware(\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (err?: any) => void,\n) {\n  // Block any cross origin request.\n  if (\n    typeof req.headers.origin === 'string' &&\n    !req.headers.origin.match(/^https?:\\/\\/localhost:/) &&\n    !req.headers.origin.startsWith('devtools://devtools')\n  ) {\n    next(\n      new Error(\n        'Unauthorized request from ' +\n          req.headers.origin +\n          '. This may happen because of a conflicting browser extension. Please try to disable it and try again.',\n      ),\n    );\n    return;\n  }\n\n  // Block MIME-type sniffing.\n  res.setHeader('X-Content-Type-Options', 'nosniff');\n\n  next();\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAGe,SAASA,yBAAyB,CAC/CC,GAAyB,EACzBC,GAAwB,EACxBC,IAAyB,EACzB;EACA;EACA,IACE,OAAOF,GAAG,CAACG,OAAO,CAACC,MAAM,KAAK,QAAQ,IACtC,CAACJ,GAAG,CAACG,OAAO,CAACC,MAAM,CAACC,KAAK,CAAC,wBAAwB,CAAC,IACnD,CAACL,GAAG,CAACG,OAAO,CAACC,MAAM,CAACE,UAAU,CAAC,qBAAqB,CAAC,EACrD;IACAJ,IAAI,CACF,IAAIK,KAAK,CACP,4BAA4B,GAC1BP,GAAG,CAACG,OAAO,CAACC,MAAM,GAClB,uGAAuG,CAC1G,CACF;IACD;EACF;;EAEA;EACAH,GAAG,CAACO,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;EAElDN,IAAI,EAAE;AACR"}