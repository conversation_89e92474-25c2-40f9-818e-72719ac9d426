{"name": "metro-source-map", "version": "0.76.8", "description": "🚇 Source map generator for Metro.", "main": "src/source-map.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "invariant": "^2.2.4", "metro-symbolicate": "0.76.8", "nullthrows": "^1.1.1", "ob1": "0.76.8", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.20.0", "@babel/parser": "^7.20.0", "terser": "^5.15.0"}, "engines": {"node": ">=16"}}