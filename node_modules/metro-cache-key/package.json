{"name": "metro-cache-key", "version": "0.76.8", "description": "🚇 Cache key utility.", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "devDependencies": {"metro-memory-fs": "0.76.8"}, "engines": {"node": ">=16"}}