{"name": "metro-babel-transformer", "version": "0.76.8", "description": "🚇 Base Babel transformer for Metro.", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "keywords": ["transformer", "metro"], "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "hermes-parser": "0.12.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">=16"}}