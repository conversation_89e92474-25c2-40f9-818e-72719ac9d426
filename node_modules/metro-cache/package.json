{"name": "metro-cache", "version": "0.76.8", "description": "🚇 Cache layers for Metro.", "main": "src/index.js", "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"metro-core": "0.76.8", "rimraf": "^3.0.2"}, "devDependencies": {"metro-memory-fs": "0.76.8"}, "license": "MIT", "engines": {"node": ">=16"}}