"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = findAllPodfilePaths;
function _glob() {
  const data = _interopRequireDefault(require("glob"));
  _glob = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

// These folders will be excluded from search to speed it up
const GLOB_EXCLUDE_PATTERN = ['**/@(Pods|node_modules|Carthage)/**'];
function findAllPodfilePaths(cwd) {
  return _glob().default.sync('**/Podfile', {
    cwd,
    ignore: GLOB_EXCLUDE_PATTERN
  });
}

//# sourceMappingURL=findAllPodfilePaths.ts.map