{"version": 3, "names": ["DEFAULT_GROUPS", "rmdirAsync", "promisify", "rmdir", "cleanDir", "directory", "fileExists", "Promise", "resolve", "maxRetries", "recursive", "<PERSON><PERSON><PERSON>", "startPath", "files", "file", "filename", "path", "undefined", "promptForCaches", "groups", "caches", "prompts", "type", "name", "message", "choices", "Object", "entries", "map", "cmd", "group", "title", "chalk", "dim", "description", "value", "selected", "includes", "min", "clean", "_argv", "_config", "cleanOptions", "include", "projectRoot", "verifyCache", "Error", "COMMANDS", "android", "tasks", "label", "action", "candidates", "os", "platform", "gradlew", "script", "basename", "execa", "cwd", "dirname", "cocoapods", "metro", "tmpdir", "npm", "watchman", "yarn", "split", "length", "spinner", "<PERSON><PERSON><PERSON><PERSON>", "commands", "warn", "start", "then", "succeed", "catch", "e", "fail", "func", "options", "default", "process"], "sources": ["../src/clean.ts"], "sourcesContent": ["import {getLoader} from '@react-native-community/cli-tools';\nimport type {Config as CLIConfig} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport execa from 'execa';\nimport {existsSync as fileExists, rmdir} from 'fs';\nimport os from 'os';\nimport path from 'path';\nimport prompts from 'prompts';\nimport {promisify} from 'util';\n\ntype Args = {\n  include?: string;\n  projectRoot: string;\n  verifyCache?: boolean;\n};\n\ntype Task = {\n  label: string;\n  action: () => Promise<void>;\n};\n\ntype CleanGroups = {\n  [key: string]: {\n    description: string;\n    tasks: Task[];\n  };\n};\n\nconst DEFAULT_GROUPS = ['metro', 'watchman'];\n\nconst rmdirAsync = promisify(rmdir);\n\nfunction cleanDir(directory: string): Promise<void> {\n  if (!fileExists(directory)) {\n    return Promise.resolve();\n  }\n\n  return rmdirAsync(directory, {maxRetries: 3, recursive: true});\n}\n\nfunction findPath(startPath: string, files: string[]): string | undefined {\n  // TODO: Find project files via `@react-native-community/cli`\n  for (const file of files) {\n    const filename = path.resolve(startPath, file);\n    if (fileExists(filename)) {\n      return filename;\n    }\n  }\n\n  return undefined;\n}\n\nasync function promptForCaches(\n  groups: CleanGroups,\n): Promise<string[] | undefined> {\n  const {caches} = await prompts({\n    type: 'multiselect',\n    name: 'caches',\n    message: 'Select all caches to clean',\n    choices: Object.entries(groups).map(([cmd, group]) => ({\n      title: `${cmd} ${chalk.dim(`(${group.description})`)}`,\n      value: cmd,\n      selected: DEFAULT_GROUPS.includes(cmd),\n    })),\n    min: 1,\n  });\n  return caches;\n}\n\nexport async function clean(\n  _argv: string[],\n  _config: CLIConfig,\n  cleanOptions: Args,\n): Promise<void> {\n  const {include, projectRoot, verifyCache} = cleanOptions;\n  if (!fileExists(projectRoot)) {\n    throw new Error(`Invalid path provided! ${projectRoot}`);\n  }\n\n  const COMMANDS: CleanGroups = {\n    android: {\n      description: 'Android build caches, e.g. Gradle',\n      tasks: [\n        {\n          label: 'Clean Gradle cache',\n          action: async () => {\n            const candidates =\n              os.platform() === 'win32'\n                ? ['android/gradlew.bat', 'gradlew.bat']\n                : ['android/gradlew', 'gradlew'];\n            const gradlew = findPath(projectRoot, candidates);\n            if (gradlew) {\n              const script = path.basename(gradlew);\n              await execa(\n                os.platform() === 'win32' ? script : `./${script}`,\n                ['clean'],\n                {cwd: path.dirname(gradlew)},\n              );\n            }\n          },\n        },\n      ],\n    },\n    ...(os.platform() === 'darwin'\n      ? {\n          cocoapods: {\n            description: 'CocoaPods cache',\n            tasks: [\n              {\n                label: 'Clean CocoaPods pod cache',\n                action: async () => {\n                  await execa('pod', ['cache', 'clean', '--all'], {\n                    cwd: projectRoot,\n                  });\n                },\n              },\n              {\n                label: 'Remove installed CocoaPods',\n                action: () => cleanDir('ios/Pods'),\n              },\n              {\n                label: 'Remove CocoaPods spec cache',\n                action: () => cleanDir('~/.cocoapods'),\n              },\n            ],\n          },\n        }\n      : undefined),\n    metro: {\n      description: 'Metro, haste-map caches',\n      tasks: [\n        {\n          label: 'Clean Metro cache',\n          action: () => cleanDir(`${os.tmpdir()}/metro-*`),\n        },\n        {\n          label: 'Clean Haste cache',\n          action: () => cleanDir(`${os.tmpdir()}/haste-map-*`),\n        },\n        {\n          label: 'Clean React Native cache',\n          action: () => cleanDir(`${os.tmpdir()}/react-*`),\n        },\n      ],\n    },\n    npm: {\n      description:\n        '`node_modules` folder in the current package, and optionally verify npm cache',\n      tasks: [\n        {\n          label: 'Remove node_modules',\n          action: () => cleanDir(`${projectRoot}/node_modules`),\n        },\n        ...(verifyCache\n          ? [\n              {\n                label: 'Verify npm cache',\n                action: async () => {\n                  await execa('npm', ['cache', 'verify'], {cwd: projectRoot});\n                },\n              },\n            ]\n          : []),\n      ],\n    },\n    watchman: {\n      description: 'Stop Watchman and delete its cache',\n      tasks: [\n        {\n          label: 'Stop Watchman',\n          action: async () => {\n            await execa(\n              os.platform() === 'win32' ? 'tskill' : 'killall',\n              ['watchman'],\n              {cwd: projectRoot},\n            );\n          },\n        },\n        {\n          label: 'Delete Watchman cache',\n          action: async () => {\n            await execa('watchman', ['watch-del-all'], {cwd: projectRoot});\n          },\n        },\n      ],\n    },\n    yarn: {\n      description: 'Yarn cache',\n      tasks: [\n        {\n          label: 'Clean Yarn cache',\n          action: async () => {\n            await execa('yarn', ['cache', 'clean'], {cwd: projectRoot});\n          },\n        },\n      ],\n    },\n  };\n\n  const groups = include ? include.split(',') : await promptForCaches(COMMANDS);\n  if (!groups || groups.length === 0) {\n    return;\n  }\n\n  const spinner = getLoader();\n  for (const group of groups) {\n    const commands = COMMANDS[group];\n    if (!commands) {\n      spinner.warn(`Unknown group: ${group}`);\n      continue;\n    }\n\n    for (const {action, label} of commands.tasks) {\n      spinner.start(label);\n      await action()\n        .then(() => {\n          spinner.succeed();\n        })\n        .catch((e) => {\n          spinner.fail(`${label} » ${e}`);\n        });\n    }\n  }\n}\n\nexport default {\n  func: clean,\n  name: 'clean',\n  description:\n    'Cleans your project by removing React Native related caches and modules.',\n  options: [\n    {\n      name: '--include <string>',\n      description:\n        'Comma-separated flag of caches to clear e.g. `npm,yarn`. If omitted, an interactive prompt will appear.',\n    },\n    {\n      name: '--project-root <string>',\n      description:\n        'Root path to your React Native project. When not specified, defaults to current working directory.',\n      default: process.cwd(),\n    },\n    {\n      name: '--verify-cache',\n      description:\n        'Whether to verify the cache. Currently only applies to npm cache.',\n      default: false,\n    },\n  ],\n};\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+B;AAoB/B,MAAMA,cAAc,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AAE5C,MAAMC,UAAU,GAAG,IAAAC,iBAAS,EAACC,WAAK,CAAC;AAEnC,SAASC,QAAQ,CAACC,SAAiB,EAAiB;EAClD,IAAI,CAAC,IAAAC,gBAAU,EAACD,SAAS,CAAC,EAAE;IAC1B,OAAOE,OAAO,CAACC,OAAO,EAAE;EAC1B;EAEA,OAAOP,UAAU,CAACI,SAAS,EAAE;IAACI,UAAU,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAI,CAAC,CAAC;AAChE;AAEA,SAASC,QAAQ,CAACC,SAAiB,EAAEC,KAAe,EAAsB;EACxE;EACA,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;IACxB,MAAME,QAAQ,GAAGC,eAAI,CAACR,OAAO,CAACI,SAAS,EAAEE,IAAI,CAAC;IAC9C,IAAI,IAAAR,gBAAU,EAACS,QAAQ,CAAC,EAAE;MACxB,OAAOA,QAAQ;IACjB;EACF;EAEA,OAAOE,SAAS;AAClB;AAEA,eAAeC,eAAe,CAC5BC,MAAmB,EACY;EAC/B,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAC,kBAAO,EAAC;IAC7BC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,4BAA4B;IACrCC,OAAO,EAAEC,MAAM,CAACC,OAAO,CAACR,MAAM,CAAC,CAACS,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;MACrDC,KAAK,EAAG,GAAEF,GAAI,IAAGG,gBAAK,CAACC,GAAG,CAAE,IAAGH,KAAK,CAACI,WAAY,GAAE,CAAE,EAAC;MACtDC,KAAK,EAAEN,GAAG;MACVO,QAAQ,EAAEpC,cAAc,CAACqC,QAAQ,CAACR,GAAG;IACvC,CAAC,CAAC,CAAC;IACHS,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOlB,MAAM;AACf;AAEO,eAAemB,KAAK,CACzBC,KAAe,EACfC,OAAkB,EAClBC,YAAkB,EACH;EACf,MAAM;IAACC,OAAO;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAGH,YAAY;EACxD,IAAI,CAAC,IAAApC,gBAAU,EAACsC,WAAW,CAAC,EAAE;IAC5B,MAAM,IAAIE,KAAK,CAAE,0BAAyBF,WAAY,EAAC,CAAC;EAC1D;EAEA,MAAMG,QAAqB,GAAG;IAC5BC,OAAO,EAAE;MACPd,WAAW,EAAE,mCAAmC;MAChDe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,YAAY;UAClB,MAAMC,UAAU,GACdC,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GACrB,CAAC,qBAAqB,EAAE,aAAa,CAAC,GACtC,CAAC,iBAAiB,EAAE,SAAS,CAAC;UACpC,MAAMC,OAAO,GAAG5C,QAAQ,CAACiC,WAAW,EAAEQ,UAAU,CAAC;UACjD,IAAIG,OAAO,EAAE;YACX,MAAMC,MAAM,GAAGxC,eAAI,CAACyC,QAAQ,CAACF,OAAO,CAAC;YACrC,MAAM,IAAAG,gBAAK,EACTL,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GAAGE,MAAM,GAAI,KAAIA,MAAO,EAAC,EAClD,CAAC,OAAO,CAAC,EACT;cAACG,GAAG,EAAE3C,eAAI,CAAC4C,OAAO,CAACL,OAAO;YAAC,CAAC,CAC7B;UACH;QACF;MACF,CAAC;IAEL,CAAC;IACD,IAAIF,aAAE,CAACC,QAAQ,EAAE,KAAK,QAAQ,GAC1B;MACEO,SAAS,EAAE;QACT3B,WAAW,EAAE,iBAAiB;QAC9Be,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,2BAA2B;UAClCC,MAAM,EAAE,YAAY;YAClB,MAAM,IAAAO,gBAAK,EAAC,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;cAC9CC,GAAG,EAAEf;YACP,CAAC,CAAC;UACJ;QACF,CAAC,EACD;UACEM,KAAK,EAAE,4BAA4B;UACnCC,MAAM,EAAE,MAAM/C,QAAQ,CAAC,UAAU;QACnC,CAAC,EACD;UACE8C,KAAK,EAAE,6BAA6B;UACpCC,MAAM,EAAE,MAAM/C,QAAQ,CAAC,cAAc;QACvC,CAAC;MAEL;IACF,CAAC,GACDa,SAAS,CAAC;IACd6C,KAAK,EAAE;MACL5B,WAAW,EAAE,yBAAyB;MACtCe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,MAAM/C,QAAQ,CAAE,GAAEiD,aAAE,CAACU,MAAM,EAAG,UAAS;MACjD,CAAC,EACD;QACEb,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,MAAM/C,QAAQ,CAAE,GAAEiD,aAAE,CAACU,MAAM,EAAG,cAAa;MACrD,CAAC,EACD;QACEb,KAAK,EAAE,0BAA0B;QACjCC,MAAM,EAAE,MAAM/C,QAAQ,CAAE,GAAEiD,aAAE,CAACU,MAAM,EAAG,UAAS;MACjD,CAAC;IAEL,CAAC;IACDC,GAAG,EAAE;MACH9B,WAAW,EACT,+EAA+E;MACjFe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,qBAAqB;QAC5BC,MAAM,EAAE,MAAM/C,QAAQ,CAAE,GAAEwC,WAAY,eAAc;MACtD,CAAC,EACD,IAAIC,WAAW,GACX,CACE;QACEK,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAO,gBAAK,EAAC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YAACC,GAAG,EAAEf;UAAW,CAAC,CAAC;QAC7D;MACF,CAAC,CACF,GACD,EAAE,CAAC;IAEX,CAAC;IACDqB,QAAQ,EAAE;MACR/B,WAAW,EAAE,oCAAoC;MACjDe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,eAAe;QACtBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAO,gBAAK,EACTL,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GAAG,QAAQ,GAAG,SAAS,EAChD,CAAC,UAAU,CAAC,EACZ;YAACK,GAAG,EAAEf;UAAW,CAAC,CACnB;QACH;MACF,CAAC,EACD;QACEM,KAAK,EAAE,uBAAuB;QAC9BC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAO,gBAAK,EAAC,UAAU,EAAE,CAAC,eAAe,CAAC,EAAE;YAACC,GAAG,EAAEf;UAAW,CAAC,CAAC;QAChE;MACF,CAAC;IAEL,CAAC;IACDsB,IAAI,EAAE;MACJhC,WAAW,EAAE,YAAY;MACzBe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAO,gBAAK,EAAC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAACC,GAAG,EAAEf;UAAW,CAAC,CAAC;QAC7D;MACF,CAAC;IAEL;EACF,CAAC;EAED,MAAMzB,MAAM,GAAGwB,OAAO,GAAGA,OAAO,CAACwB,KAAK,CAAC,GAAG,CAAC,GAAG,MAAMjD,eAAe,CAAC6B,QAAQ,CAAC;EAC7E,IAAI,CAAC5B,MAAM,IAAIA,MAAM,CAACiD,MAAM,KAAK,CAAC,EAAE;IAClC;EACF;EAEA,MAAMC,OAAO,GAAG,IAAAC,qBAAS,GAAE;EAC3B,KAAK,MAAMxC,KAAK,IAAIX,MAAM,EAAE;IAC1B,MAAMoD,QAAQ,GAAGxB,QAAQ,CAACjB,KAAK,CAAC;IAChC,IAAI,CAACyC,QAAQ,EAAE;MACbF,OAAO,CAACG,IAAI,CAAE,kBAAiB1C,KAAM,EAAC,CAAC;MACvC;IACF;IAEA,KAAK,MAAM;MAACqB,MAAM;MAAED;IAAK,CAAC,IAAIqB,QAAQ,CAACtB,KAAK,EAAE;MAC5CoB,OAAO,CAACI,KAAK,CAACvB,KAAK,CAAC;MACpB,MAAMC,MAAM,EAAE,CACXuB,IAAI,CAAC,MAAM;QACVL,OAAO,CAACM,OAAO,EAAE;MACnB,CAAC,CAAC,CACDC,KAAK,CAAEC,CAAC,IAAK;QACZR,OAAO,CAACS,IAAI,CAAE,GAAE5B,KAAM,MAAK2B,CAAE,EAAC,CAAC;MACjC,CAAC,CAAC;IACN;EACF;AACF;AAAC,eAEc;EACbE,IAAI,EAAExC,KAAK;EACXhB,IAAI,EAAE,OAAO;EACbW,WAAW,EACT,0EAA0E;EAC5E8C,OAAO,EAAE,CACP;IACEzD,IAAI,EAAE,oBAAoB;IAC1BW,WAAW,EACT;EACJ,CAAC,EACD;IACEX,IAAI,EAAE,yBAAyB;IAC/BW,WAAW,EACT,oGAAoG;IACtG+C,OAAO,EAAEC,OAAO,CAACvB,GAAG;EACtB,CAAC,EACD;IACEpC,IAAI,EAAE,gBAAgB;IACtBW,WAAW,EACT,mEAAmE;IACrE+C,OAAO,EAAE;EACX,CAAC;AAEL,CAAC;AAAA"}