"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.copyTemplate = copyTemplate;
exports.executePostInitScript = executePostInitScript;
exports.getTemplateConfig = getTemplateConfig;
exports.installTemplatePackage = installTemplatePackage;
function _execa() {
  const data = _interopRequireDefault(require("execa"));
  _execa = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var PackageManager = _interopRequireWildcard(require("../../tools/packageManager"));
var _copyFiles = _interopRequireDefault(require("../../tools/copyFiles"));
var _replacePathSepForRegex = _interopRequireDefault(require("../../tools/replacePathSepForRegex"));
function _fs() {
  const data = _interopRequireDefault(require("fs"));
  _fs = function () {
    return data;
  };
  return data;
}
function _chalk() {
  const data = _interopRequireDefault(require("chalk"));
  _chalk = function () {
    return data;
  };
  return data;
}
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
async function installTemplatePackage(templateName, root, npm) {
  _cliTools().logger.debug(`Installing template from ${templateName}`);
  await PackageManager.init({
    preferYarn: !npm,
    silent: true,
    root
  });
  return PackageManager.install([templateName], {
    preferYarn: !npm,
    silent: true,
    root
  });
}
function getTemplateConfig(templateName, templateSourceDir) {
  const configFilePath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, 'template.config.js');
  _cliTools().logger.debug(`Getting config from ${configFilePath}`);
  if (!_fs().default.existsSync(configFilePath)) {
    throw new (_cliTools().CLIError)(`Couldn't find the "${configFilePath} file inside "${templateName}" template. Please make sure the template is valid.
      Read more: ${_chalk().default.underline.dim('https://github.com/react-native-community/cli/blob/master/docs/init.md#creating-custom-template')}`);
  }
  return require(configFilePath);
}
async function copyTemplate(templateName, templateDir, templateSourceDir) {
  const templatePath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, templateDir);
  _cliTools().logger.debug(`Copying template from ${templatePath}`);
  let regexStr = _path().default.resolve(templatePath, 'node_modules');
  await (0, _copyFiles.default)(templatePath, process.cwd(), {
    exclude: [new RegExp((0, _replacePathSepForRegex.default)(regexStr))]
  });
}
function executePostInitScript(templateName, postInitScript, templateSourceDir) {
  const scriptPath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, postInitScript);
  _cliTools().logger.debug(`Executing post init script located ${scriptPath}`);
  return (0, _execa().default)(scriptPath, {
    stdio: 'inherit'
  });
}

//# sourceMappingURL=template.ts.map