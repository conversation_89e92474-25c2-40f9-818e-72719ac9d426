# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ava/babel-preset-stage-4@^1.0.0":
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/@ava/babel-preset-stage-4/-/babel-preset-stage-4-1.0.0.tgz#a613b5e152f529305422546b072d47facfb26291"
  dependencies:
    babel-plugin-check-es2015-constants "^6.8.0"
    babel-plugin-syntax-trailing-function-commas "^6.20.0"
    babel-plugin-transform-async-to-generator "^6.16.0"
    babel-plugin-transform-es2015-destructuring "^6.19.0"
    babel-plugin-transform-es2015-function-name "^6.9.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.18.0"
    babel-plugin-transform-es2015-parameters "^6.21.0"
    babel-plugin-transform-es2015-spread "^6.8.0"
    babel-plugin-transform-es2015-sticky-regex "^6.8.0"
    babel-plugin-transform-es2015-unicode-regex "^6.11.0"
    babel-plugin-transform-exponentiation-operator "^6.8.0"
    package-hash "^1.2.0"

"@ava/babel-preset-transform-test-files@^2.0.0":
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/@ava/babel-preset-transform-test-files/-/babel-preset-transform-test-files-2.0.0.tgz#0347c7f3350f2e383f63d8c909bd4d0fe3bd64c3"
  dependencies:
    babel-plugin-ava-throws-helper "^1.0.0"
    babel-plugin-espower "^2.3.2"
    package-hash "^1.2.0"

"@ava/pretty-format@^1.0.0":
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/@ava/pretty-format/-/pretty-format-1.0.0.tgz#ddeac982d2d92a8b590f770c0ddc5a8b09b34042"
  dependencies:
    ansi-styles "^2.2.1"

abbrev@1:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/abbrev/-/abbrev-1.0.9.tgz#91b4792588a7738c25f35dd6f63752a2f8776135"

acorn-jsx@^3.0.0, acorn-jsx@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/acorn-jsx/-/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
  dependencies:
    acorn "^3.0.4"

acorn@^3.0.4:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"

acorn@^4.0.1:
  version "4.0.5"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-4.0.5.tgz#d5c76ef581c59748047067e2fc96302b1333c132"

ajv-keywords@^1.0.0:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-1.5.1.tgz#314dd0a4b3368fad3dfcdc54ede6171b886daf3c"

ajv@^4.7.0:
  version "4.11.2"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-4.11.2.tgz#f166c3c11cbc6cb9dcc102a5bcfe5b72c95287e6"
  dependencies:
    co "^4.6.0"
    json-stable-stringify "^1.0.1"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-align@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/ansi-align/-/ansi-align-1.1.0.tgz#2f0c1658829739add5ebb15e6b0c6e3423f016ba"
  dependencies:
    string-width "^1.0.1"

ansi-escapes@^1.1.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-1.0.0.tgz#cb102df1c56f5123eab8b67cd7b98027a0279178"

anymatch@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/anymatch/-/anymatch-1.3.0.tgz#a3e52fa39168c825ff57b0248126ce5a8ff95507"
  dependencies:
    arrify "^1.0.0"
    micromatch "^2.1.5"

append-transform@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/append-transform/-/append-transform-0.4.0.tgz#d76ebf8ca94d276e247a36bad44a4b74ab611991"
  dependencies:
    default-require-extensions "^1.0.0"

aproba@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/aproba/-/aproba-1.0.4.tgz#2713680775e7614c8ba186c065d4e2e52d1072c0"

archy@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/archy/-/archy-1.0.0.tgz#f9c8c13757cc1dd7bc379ac77b2c62a5c2868c40"

are-we-there-yet@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/are-we-there-yet/-/are-we-there-yet-1.1.2.tgz#80e470e95a084794fe1899262c5667c6e88de1b3"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.0 || ^1.1.13"

argparse@^1.0.7:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.9.tgz#73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86"
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/arr-diff/-/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-exclude@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/arr-exclude/-/arr-exclude-1.0.0.tgz#dfc7c2e552a270723ccda04cf3128c8cbfe5c631"

arr-flatten@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/arr-flatten/-/arr-flatten-1.0.1.tgz#e5ffe54d45e19f32f216e91eb99c8ce892bb604b"

array-differ@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/array-differ/-/array-differ-1.0.0.tgz#eff52e3758249d33be402b8bb8e564bb2b5d4031"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-find-index/-/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1, array-uniq@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/array-unique/-/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asn1@~0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/asn1/-/asn1-0.2.3.tgz#dac8787713c9966849fc8180777ebe9c1ddf3b86"

assert-plus@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-0.2.0.tgz#d74e1b87e7affc0db8aadb7021f3fe48101ab234"

assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

async-each@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/async-each/-/async-each-1.0.1.tgz#19d386a1d9edc6e7c1c85d388aedbcc56d33602d"

async@^1.4.0, async@^1.4.2:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/async/-/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

async@~0.2.6:
  version "0.2.10"
  resolved "https://registry.yarnpkg.com/async/-/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

auto-bind@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/auto-bind/-/auto-bind-1.1.0.tgz#93b864dc7ee01a326281775d5c75ca0a751e5961"

ava-init@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/ava-init/-/ava-init-0.2.0.tgz#9304c8b4c357d66e3dfdae1fbff47b1199d5c55d"
  dependencies:
    arr-exclude "^1.0.0"
    execa "^0.5.0"
    has-yarn "^1.0.0"
    read-pkg-up "^2.0.0"
    write-pkg "^2.0.0"

ava@^0.18.0:
  version "0.18.0"
  resolved "https://registry.yarnpkg.com/ava/-/ava-0.18.0.tgz#9bfe4f7d5eacb6037b41a70113fba056b15a9701"
  dependencies:
    "@ava/babel-preset-stage-4" "^1.0.0"
    "@ava/babel-preset-transform-test-files" "^2.0.0"
    "@ava/pretty-format" "^1.0.0"
    arr-flatten "^1.0.1"
    array-union "^1.0.1"
    array-uniq "^1.0.2"
    arrify "^1.0.0"
    auto-bind "^1.1.0"
    ava-init "^0.2.0"
    babel-code-frame "^6.16.0"
    babel-core "^6.17.0"
    bluebird "^3.0.0"
    caching-transform "^1.0.0"
    chalk "^1.0.0"
    chokidar "^1.4.2"
    clean-stack "^1.1.1"
    clean-yaml-object "^0.1.0"
    cli-cursor "^2.1.0"
    cli-spinners "^1.0.0"
    cli-truncate "^0.2.0"
    co-with-promise "^4.6.0"
    code-excerpt "^2.1.0"
    common-path-prefix "^1.0.0"
    convert-source-map "^1.2.0"
    core-assert "^0.2.0"
    currently-unhandled "^0.4.1"
    debug "^2.2.0"
    diff "^3.0.1"
    dot-prop "^4.1.0"
    empower-core "^0.6.1"
    equal-length "^1.0.0"
    figures "^2.0.0"
    find-cache-dir "^0.1.1"
    fn-name "^2.0.0"
    get-port "^2.1.0"
    globby "^6.0.0"
    has-flag "^2.0.0"
    ignore-by-default "^1.0.0"
    indent-string "^3.0.0"
    is-ci "^1.0.7"
    is-generator-fn "^1.0.0"
    is-obj "^1.0.0"
    is-observable "^0.2.0"
    is-promise "^2.1.0"
    jest-snapshot "^18.1.0"
    last-line-stream "^1.0.0"
    lodash.debounce "^4.0.3"
    lodash.difference "^4.3.0"
    lodash.flatten "^4.2.0"
    lodash.isequal "^4.5.0"
    loud-rejection "^1.2.0"
    matcher "^0.1.1"
    max-timeout "^1.0.0"
    md5-hex "^2.0.0"
    meow "^3.7.0"
    ms "^0.7.1"
    multimatch "^2.1.0"
    observable-to-promise "^0.4.0"
    option-chain "^0.1.0"
    package-hash "^1.2.0"
    pkg-conf "^2.0.0"
    plur "^2.0.0"
    pretty-ms "^2.0.0"
    require-precompiled "^0.1.0"
    resolve-cwd "^1.0.0"
    slash "^1.0.0"
    source-map-support "^0.4.0"
    stack-utils "^0.4.0"
    strip-ansi "^3.0.1"
    strip-bom-buf "^1.0.0"
    time-require "^0.1.2"
    unique-temp-dir "^1.0.0"
    update-notifier "^1.0.0"

aws-sign2@~0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/aws-sign2/-/aws-sign2-0.6.0.tgz#14342dd38dbcc94d0e5b87d763cd63612c0e794f"

aws4@^1.2.1:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/aws4/-/aws4-1.5.0.tgz#0a29ffb79c31c9e712eeb087e8e7a64b4a56d755"

babel-code-frame@^6.16.0, babel-code-frame@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.22.0.tgz#027620bee567a88c32561574e7fd0801d33118e4"
  dependencies:
    chalk "^1.1.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

babel-core@^6.17.0, babel-core@^6.22.0:
  version "6.22.1"
  resolved "https://registry.yarnpkg.com/babel-core/-/babel-core-6.22.1.tgz#9c5fd658ba1772d28d721f6d25d968fc7ae21648"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-generator "^6.22.0"
    babel-helpers "^6.22.0"
    babel-messages "^6.22.0"
    babel-register "^6.22.0"
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"
    babel-traverse "^6.22.1"
    babel-types "^6.22.0"
    babylon "^6.11.0"
    convert-source-map "^1.1.0"
    debug "^2.1.1"
    json5 "^0.5.0"
    lodash "^4.2.0"
    minimatch "^3.0.2"
    path-is-absolute "^1.0.0"
    private "^0.1.6"
    slash "^1.0.0"
    source-map "^0.5.0"

babel-generator@^6.1.0, babel-generator@^6.18.0, babel-generator@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-generator/-/babel-generator-6.22.0.tgz#d642bf4961911a8adc7c692b0c9297f325cda805"
  dependencies:
    babel-messages "^6.22.0"
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.2.0"
    source-map "^0.5.0"

babel-helper-builder-binary-assignment-operator-visitor@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-builder-binary-assignment-operator-visitor/-/babel-helper-builder-binary-assignment-operator-visitor-6.22.0.tgz#29df56be144d81bdeac08262bfa41d2c5e91cdcd"
  dependencies:
    babel-helper-explode-assignable-expression "^6.22.0"
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-helper-call-delegate@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-call-delegate/-/babel-helper-call-delegate-6.22.0.tgz#119921b56120f17e9dae3f74b4f5cc7bcc1b37ef"
  dependencies:
    babel-helper-hoist-variables "^6.22.0"
    babel-runtime "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"

babel-helper-explode-assignable-expression@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-explode-assignable-expression/-/babel-helper-explode-assignable-expression-6.22.0.tgz#c97bf76eed3e0bae4048121f2b9dae1a4e7d0478"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"

babel-helper-function-name@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-function-name/-/babel-helper-function-name-6.22.0.tgz#51f1bdc4bb89b15f57a9b249f33d742816dcbefc"
  dependencies:
    babel-helper-get-function-arity "^6.22.0"
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"

babel-helper-get-function-arity@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.22.0.tgz#0beb464ad69dc7347410ac6ade9f03a50634f5ce"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-helper-hoist-variables@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.22.0.tgz#3eacbf731d80705845dd2e9718f600cfb9b4ba72"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-helper-regex@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-regex/-/babel-helper-regex-6.22.0.tgz#79f532be1647b1f0ee3474b5f5c3da58001d247d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"
    lodash "^4.2.0"

babel-helper-remap-async-to-generator@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helper-remap-async-to-generator/-/babel-helper-remap-async-to-generator-6.22.0.tgz#2186ae73278ed03b8b15ced089609da981053383"
  dependencies:
    babel-helper-function-name "^6.22.0"
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"

babel-helpers@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-helpers/-/babel-helpers-6.22.0.tgz#d275f55f2252b8101bff07bc0c556deda657392c"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"

babel-messages@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-messages/-/babel-messages-6.22.0.tgz#36066a214f1217e4ed4164867669ecb39e3ea575"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-ava-throws-helper@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-ava-throws-helper/-/babel-plugin-ava-throws-helper-1.0.0.tgz#8fe6e79d2fd19838b5c3649f89cfb03fd563e241"
  dependencies:
    babel-template "^6.7.0"
    babel-types "^6.7.2"

babel-plugin-check-es2015-constants@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-espower@^2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-espower/-/babel-plugin-espower-2.3.2.tgz#5516b8fcdb26c9f0e1d8160749f6e4c65e71271e"
  dependencies:
    babel-generator "^6.1.0"
    babylon "^6.1.0"
    call-matcher "^1.0.0"
    core-js "^2.0.0"
    espower-location-detector "^1.0.0"
    espurify "^1.6.0"
    estraverse "^4.1.1"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-async-functions/-/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-exponentiation-operator/-/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"

babel-plugin-syntax-trailing-function-commas@^6.20.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-syntax-trailing-function-commas/-/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"

babel-plugin-transform-async-to-generator@^6.16.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-async-to-generator/-/babel-plugin-transform-async-to-generator-6.22.0.tgz#194b6938ec195ad36efc4c33a971acf00d8cd35e"
  dependencies:
    babel-helper-remap-async-to-generator "^6.22.0"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-destructuring@^6.19.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.22.0.tgz#8e0af2f885a0b2cf999d47c4c1dd23ce88cfa4c6"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.9.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.22.0.tgz#f5fcc8b09093f9a23c76ac3d9e392c3ec4b77104"
  dependencies:
    babel-helper-function-name "^6.22.0"
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-plugin-transform-es2015-modules-commonjs@^6.18.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.22.0.tgz#6ca04e22b8e214fb50169730657e7a07dc941145"
  dependencies:
    babel-plugin-transform-strict-mode "^6.22.0"
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"
    babel-types "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.21.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.22.0.tgz#57076069232019094f27da8c68bb7162fe208dbb"
  dependencies:
    babel-helper-call-delegate "^6.22.0"
    babel-helper-get-function-arity "^6.22.0"
    babel-runtime "^6.22.0"
    babel-template "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"

babel-plugin-transform-es2015-spread@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.22.0.tgz#ab316829e866ee3f4b9eb96939757d19a5bc4593"
  dependencies:
    babel-helper-regex "^6.22.0"
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.11.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.22.0.tgz#8d9cc27e7ee1decfe65454fb986452a04a613d20"
  dependencies:
    babel-helper-regex "^6.22.0"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.8.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-exponentiation-operator/-/babel-plugin-transform-exponentiation-operator-6.22.0.tgz#d57c8335281918e54ef053118ce6eb108468084d"
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.22.0"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.22.0.tgz#e008df01340fdc87e959da65991b7e05970c8c7c"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"

babel-register@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-register/-/babel-register-6.22.0.tgz#a61dd83975f9ca4a9e7d6eff3059494cd5ea4c63"
  dependencies:
    babel-core "^6.22.0"
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    home-or-tmp "^2.0.0"
    lodash "^4.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.4.2"

babel-runtime@^6.22.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-runtime/-/babel-runtime-6.22.0.tgz#1cf8b4ac67c77a4ddb0db2ae1f74de52ac4ca611"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-template@^6.16.0, babel-template@^6.22.0, babel-template@^6.7.0:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-template/-/babel-template-6.22.0.tgz#403d110905a4626b317a2a1fcb8f3b73204b2edb"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.22.0"
    babel-types "^6.22.0"
    babylon "^6.11.0"
    lodash "^4.2.0"

babel-traverse@^6.18.0, babel-traverse@^6.22.0, babel-traverse@^6.22.1:
  version "6.22.1"
  resolved "https://registry.yarnpkg.com/babel-traverse/-/babel-traverse-6.22.1.tgz#3b95cd6b7427d6f1f757704908f2fc9748a5f59f"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-messages "^6.22.0"
    babel-runtime "^6.22.0"
    babel-types "^6.22.0"
    babylon "^6.15.0"
    debug "^2.2.0"
    globals "^9.0.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

babel-types@^6.18.0, babel-types@^6.22.0, babel-types@^6.7.2:
  version "6.22.0"
  resolved "https://registry.yarnpkg.com/babel-types/-/babel-types-6.22.0.tgz#2a447e8d0ea25d2512409e4175479fd78cc8b1db"
  dependencies:
    babel-runtime "^6.22.0"
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^1.0.1"

babylon@^6.1.0, babylon@^6.11.0, babylon@^6.13.0, babylon@^6.15.0:
  version "6.15.0"
  resolved "https://registry.yarnpkg.com/babylon/-/babylon-6.15.0.tgz#ba65cfa1a80e1759b0e89fb562e27dccae70348e"

balanced-match@^0.4.1:
  version "0.4.2"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-0.4.2.tgz#cb3f3e3c732dc0f01ee70b403f302e61d7709838"

bcrypt-pbkdf@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz#63bc5dcb61331b92bc05fd528953c33462a06f8d"
  dependencies:
    tweetnacl "^0.14.3"

binary-extensions@^1.0.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-1.8.0.tgz#48ec8d16df4377eae5fa5884682480af4d95c774"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.yarnpkg.com/block-stream/-/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bluebird@^3.0.0:
  version "3.4.7"
  resolved "https://registry.yarnpkg.com/bluebird/-/bluebird-3.4.7.tgz#f72d760be09b7f76d08ed8fae98b289a8d05fab3"

boom@2.x.x:
  version "2.10.1"
  resolved "https://registry.yarnpkg.com/boom/-/boom-2.10.1.tgz#39c8918ceff5799f83f9492a848f625add0c766f"
  dependencies:
    hoek "2.x.x"

boxen@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/boxen/-/boxen-0.6.0.tgz#8364d4248ac34ff0ef1b2f2bf49a6c60ce0d81b6"
  dependencies:
    ansi-align "^1.1.0"
    camelcase "^2.1.0"
    chalk "^1.1.1"
    cli-boxes "^1.0.0"
    filled-array "^1.0.0"
    object-assign "^4.0.1"
    repeating "^2.0.0"
    string-width "^1.0.1"
    widest-line "^1.0.0"

brace-expansion@^1.0.0:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.6.tgz#7197d7eaa9b87e648390ea61fc66c84427420df9"
  dependencies:
    balanced-match "^0.4.1"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://registry.yarnpkg.com/braces/-/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

buf-compare@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/buf-compare/-/buf-compare-1.0.1.tgz#fef28da8b8113a0a0db4430b0b6467b69730b34a"

buffer-shims@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/buffer-shims/-/buffer-shims-1.0.0.tgz#9978ce317388c649ad8793028c3477ef044a8b51"

builtin-modules@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/builtin-modules/-/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

caching-transform@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/caching-transform/-/caching-transform-1.0.1.tgz#6dbdb2f20f8d8fbce79f3e94e9d1742dcdf5c0a1"
  dependencies:
    md5-hex "^1.2.0"
    mkdirp "^0.5.1"
    write-file-atomic "^1.1.4"

call-matcher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/call-matcher/-/call-matcher-1.0.1.tgz#5134d077984f712a54dad3cbf62de28dce416ca8"
  dependencies:
    core-js "^2.0.0"
    deep-equal "^1.0.0"
    espurify "^1.6.0"
    estraverse "^4.0.0"

call-signature@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/call-signature/-/call-signature-0.0.2.tgz#a84abc825a55ef4cb2b028bd74e205a65b9a4996"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/caller-path/-/caller-path-0.1.0.tgz#94085ef63581ecd3daa92444a8fe94e82577751f"
  dependencies:
    callsites "^0.2.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/callsites/-/callsites-0.2.0.tgz#afab96262910a7f33c19a5775825c69f34e350ca"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/camelcase-keys/-/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

camelcase@^2.0.0, camelcase@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-3.0.0.tgz#32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a"

capture-stack-trace@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/capture-stack-trace/-/capture-stack-trace-1.0.0.tgz#4a6fa07399c26bba47f0b2496b4d0fb408c5550d"

caseless@~0.11.0:
  version "0.11.0"
  resolved "https://registry.yarnpkg.com/caseless/-/caseless-0.11.0.tgz#715b96ea9841593cc33067923f5ec60ebda4f7d7"

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-0.4.0.tgz#5199a3ddcd0c1efe23bc08c1b027b06176e0c64f"
  dependencies:
    ansi-styles "~1.0.0"
    has-color "~0.1.0"
    strip-ansi "~0.1.0"

chalk@^1.0.0, chalk@^1.1.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chokidar@^1.4.2:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/chokidar/-/chokidar-1.6.1.tgz#2f4447ab5e96e50fb3d789fd90d4c72e0e4c70c2"
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

ci-info@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/ci-info/-/ci-info-1.0.0.tgz#dc5285f2b4e251821683681c381c3388f46ec534"

circular-json@^0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/circular-json/-/circular-json-0.3.1.tgz#be8b36aefccde8b3ca7aa2d6afc07a37242c0d2d"

clean-stack@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/clean-stack/-/clean-stack-1.1.1.tgz#a1b3711122df162df7c7cb9b3c0470f28cb58adb"

clean-yaml-object@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/clean-yaml-object/-/clean-yaml-object-0.1.0.tgz#63fb110dc2ce1a84dc21f6d9334876d010ae8b68"

cli-boxes@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/cli-boxes/-/cli-boxes-1.0.0.tgz#4fa917c3e59c94a004cd61f8ee509da651687143"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  dependencies:
    restore-cursor "^1.0.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/cli-spinners/-/cli-spinners-1.0.0.tgz#ef987ed3d48391ac3dab9180b406a742180d6e6a"

cli-truncate@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/cli-truncate/-/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-2.1.0.tgz#b234ca209b29ef66fc518d9b98d5847b00edf00a"

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.2.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/cliui/-/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

co-with-promise@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/co-with-promise/-/co-with-promise-4.6.0.tgz#413e7db6f5893a60b942cf492c4bec93db415ab7"
  dependencies:
    pinkie-promise "^1.0.0"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

code-excerpt@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/code-excerpt/-/code-excerpt-2.1.0.tgz#5dcc081e88f4a7e3b554e9e35d7ef232d47f8147"
  dependencies:
    convert-to-spaces "^1.0.1"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/code-point-at/-/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

combined-stream@^1.0.5, combined-stream@~1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.5.tgz#938370a57b4a51dea2c77c15d5c5fdf895164009"
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.9.0:
  version "2.9.0"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.9.0.tgz#9c99094176e12240cb22d6c5146098400fe0f7d4"
  dependencies:
    graceful-readlink ">= 1.0.0"

common-path-prefix@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/common-path-prefix/-/common-path-prefix-1.0.0.tgz#cd52f6f0712e0baab97d6f9732874f22f47752c0"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

component-type@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/component-type/-/component-type-1.2.1.tgz#8a47901700238e4fc32269771230226f24b415a9"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@^1.4.6:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.0.tgz#0aac662fd52be78964d5532f694784e70110acf7"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

configstore@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/configstore/-/configstore-2.1.0.tgz#737a3a7036e9886102aa6099e47bb33ab1aba1a1"
  dependencies:
    dot-prop "^3.0.0"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    object-assign "^4.0.1"
    os-tmpdir "^1.0.0"
    osenv "^0.1.0"
    uuid "^2.0.1"
    write-file-atomic "^1.1.2"
    xdg-basedir "^2.0.0"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-control-strings/-/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

convert-source-map@^1.1.0, convert-source-map@^1.2.0, convert-source-map@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.3.0.tgz#e9f3e9c6e2728efc2676696a70eb382f73106a67"

convert-to-spaces@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/convert-to-spaces/-/convert-to-spaces-1.0.1.tgz#df97c15b6d061375cc4f3efe01bfc1f4d2a83ad6"

core-assert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/core-assert/-/core-assert-0.2.1.tgz#f85e2cf9bfed28f773cc8b3fa5c5b69bdc02fe3f"
  dependencies:
    buf-compare "^1.0.0"
    is-error "^2.2.0"

core-js@^2.0.0, core-js@^2.4.0:
  version "2.4.1"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-2.4.1.tgz#4de911e667b0eae9124e34254b53aea6fc618d3e"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

create-error-class@^3.0.1:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/create-error-class/-/create-error-class-3.0.2.tgz#06be7abef947a3f14a30fd610671d401bca8b7b6"
  dependencies:
    capture-stack-trace "^1.0.0"

cross-spawn@^4, cross-spawn@^4.0.0:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-4.0.2.tgz#7b9247621c23adfdd3856004a823cbe397424d41"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cryptiles@2.x.x:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/cryptiles/-/cryptiles-2.0.5.tgz#3bdfecdc608147c1c67202fa291e7dca59eaa3b8"
  dependencies:
    boom "2.x.x"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/currently-unhandled/-/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

d@^0.1.1, d@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/d/-/d-0.1.1.tgz#da184c535d18d8ee7ba2aa229b914009fae11309"
  dependencies:
    es5-ext "~0.10.2"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.yarnpkg.com/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-time@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/date-time/-/date-time-0.1.1.tgz#ed2f6d93d9790ce2fd66d5b5ff3edd5bbcbf3b07"

debug-log@^1.0.0, debug-log@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/debug-log/-/debug-log-1.0.1.tgz#2307632d4c04382b8df8a32f70b895046d52745f"

debug@^2.1.1, debug@^2.2.0:
  version "2.6.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.0.tgz#bc596bcabe7617f11d9fa15361eded5608b8499b"
  dependencies:
    ms "0.7.2"

debug@~2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.2.0.tgz#f87057e995b1a1f6ae6a4960664137bc56f039da"
  dependencies:
    ms "0.7.1"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

deep-equal@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/deep-equal/-/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"

deep-extend@~0.4.0:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/deep-extend/-/deep-extend-0.4.1.tgz#efe4113d08085f4e6f9687759810f807469e2253"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"

default-require-extensions@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/default-require-extensions/-/default-require-extensions-1.0.0.tgz#f37ea15d3e13ffd9b437d33e1a75b5fb97874cb8"
  dependencies:
    strip-bom "^2.0.0"

deglob@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/deglob/-/deglob-2.1.0.tgz#4d44abe16ef32c779b4972bd141a80325029a14a"
  dependencies:
    find-root "^1.0.0"
    glob "^7.0.5"
    ignore "^3.0.9"
    pkg-config "^1.1.0"
    run-parallel "^1.1.2"
    uniq "^1.0.1"

del@^2.0.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/del/-/del-2.2.2.tgz#c12c981d067846c84bcaf862cff930d907ffd1a8"
  dependencies:
    globby "^5.0.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/detect-indent/-/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

diff@^3.0.0, diff@^3.0.1:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/diff/-/diff-3.2.0.tgz#c9ce393a4b7cbd0b058a725c93df299027868ff9"

doctrine@^1.2.2:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

dot-prop@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/dot-prop/-/dot-prop-3.0.0.tgz#1b708af094a49c9a0e7dbcad790aba539dac1177"
  dependencies:
    is-obj "^1.0.0"

dot-prop@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/dot-prop/-/dot-prop-4.1.0.tgz#eb29eac57dfa31fda1edef50ea462ee3d38ff3ab"
  dependencies:
    is-obj "^1.0.0"

duplexer2@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/duplexer2/-/duplexer2-0.1.4.tgz#8b12dab878c0d69e3e7891051662a32fc6bddcc1"
  dependencies:
    readable-stream "^2.0.2"

ecc-jsbn@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz#0fc73a9ed5f0d53c38193398523ef7e543777505"
  dependencies:
    jsbn "~0.1.0"

empower-core@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/empower-core/-/empower-core-0.6.1.tgz#6c187f502fcef7554d57933396aac655483772b1"
  dependencies:
    call-signature "0.0.2"
    core-js "^2.0.0"

equal-length@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/equal-length/-/equal-length-1.0.1.tgz#21ca112d48ab24b4e1e7ffc0e5339d31fdfc274c"

error-ex@^1.2.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.0.tgz#e67b43f3e82c96ea3a584ffee0b9fc3325d802d9"
  dependencies:
    is-arrayish "^0.2.1"

es5-ext@^0.10.7, es5-ext@^0.10.8, es5-ext@~0.10.11, es5-ext@~0.10.2, es5-ext@~0.10.7:
  version "0.10.12"
  resolved "https://registry.yarnpkg.com/es5-ext/-/es5-ext-0.10.12.tgz#aa84641d4db76b62abba5e45fd805ecbab140047"
  dependencies:
    es6-iterator "2"
    es6-symbol "~3.1"

es6-iterator@2:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/es6-iterator/-/es6-iterator-2.0.0.tgz#bd968567d61635e33c0b80727613c9cb4b096bac"
  dependencies:
    d "^0.1.1"
    es5-ext "^0.10.7"
    es6-symbol "3"

es6-map@^0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/es6-map/-/es6-map-0.1.4.tgz#a34b147be224773a4d7da8072794cefa3632b897"
  dependencies:
    d "~0.1.1"
    es5-ext "~0.10.11"
    es6-iterator "2"
    es6-set "~0.1.3"
    es6-symbol "~3.1.0"
    event-emitter "~0.3.4"

es6-set@~0.1.3:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/es6-set/-/es6-set-0.1.4.tgz#9516b6761c2964b92ff479456233a247dc707ce8"
  dependencies:
    d "~0.1.1"
    es5-ext "~0.10.11"
    es6-iterator "2"
    es6-symbol "3"
    event-emitter "~0.3.4"

es6-symbol@3, es6-symbol@~3.1, es6-symbol@~3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/es6-symbol/-/es6-symbol-3.1.0.tgz#94481c655e7a7cad82eba832d97d5433496d7ffa"
  dependencies:
    d "~0.1.1"
    es5-ext "~0.10.11"

es6-weak-map@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/es6-weak-map/-/es6-weak-map-2.0.1.tgz#0d2bbd8827eb5fb4ba8f97fbfea50d43db21ea81"
  dependencies:
    d "^0.1.1"
    es5-ext "^0.10.8"
    es6-iterator "2"
    es6-symbol "3"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escope@^3.6.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/escope/-/escope-3.6.0.tgz#e01975e812781a163a6dadfdd80398dc64c889c3"
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-config-standard-jsx@3.2.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/eslint-config-standard-jsx/-/eslint-config-standard-jsx-3.2.0.tgz#c240e26ed919a11a42aa4de8059472b38268d620"

eslint-config-standard@6.2.1:
  version "6.2.1"
  resolved "https://registry.yarnpkg.com/eslint-config-standard/-/eslint-config-standard-6.2.1.tgz#d3a68aafc7191639e7ee441e7348739026354292"

eslint-plugin-promise@~3.4.0:
  version "3.4.0"
  resolved "https://registry.yarnpkg.com/eslint-plugin-promise/-/eslint-plugin-promise-3.4.0.tgz#6ba9048c2df57be77d036e0c68918bc9b4fc4195"

eslint-plugin-react@~6.7.1:
  version "6.7.1"
  resolved "https://registry.yarnpkg.com/eslint-plugin-react/-/eslint-plugin-react-6.7.1.tgz#1af96aea545856825157d97c1b50d5a8fb64a5a7"
  dependencies:
    doctrine "^1.2.2"
    jsx-ast-utils "^1.3.3"

eslint-plugin-standard@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/eslint-plugin-standard/-/eslint-plugin-standard-2.0.1.tgz#3589699ff9c917f2c25f76a916687f641c369ff3"

eslint@~3.10.2:
  version "3.10.2"
  resolved "https://registry.yarnpkg.com/eslint/-/eslint-3.10.2.tgz#c9a10e8bf6e9d65651204778c503341f1eac3ce7"
  dependencies:
    babel-code-frame "^6.16.0"
    chalk "^1.1.3"
    concat-stream "^1.4.6"
    debug "^2.1.1"
    doctrine "^1.2.2"
    escope "^3.6.0"
    espree "^3.3.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    glob "^7.0.3"
    globals "^9.2.0"
    ignore "^3.2.0"
    imurmurhash "^0.1.4"
    inquirer "^0.12.0"
    is-my-json-valid "^2.10.0"
    is-resolvable "^1.0.0"
    js-yaml "^3.5.1"
    json-stable-stringify "^1.0.0"
    levn "^0.3.0"
    lodash "^4.0.0"
    mkdirp "^0.5.0"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.1"
    pluralize "^1.2.1"
    progress "^1.1.8"
    require-uncached "^1.0.2"
    shelljs "^0.7.5"
    strip-bom "^3.0.0"
    strip-json-comments "~1.0.1"
    table "^3.7.8"
    text-table "~0.2.0"
    user-home "^2.0.0"

espower-location-detector@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/espower-location-detector/-/espower-location-detector-1.0.0.tgz#a17b7ecc59d30e179e2bef73fb4137704cb331b5"
  dependencies:
    is-url "^1.2.1"
    path-is-absolute "^1.0.0"
    source-map "^0.5.0"
    xtend "^4.0.0"

espree@^3.3.1:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/espree/-/espree-3.3.2.tgz#dbf3fadeb4ecb4d4778303e50103b3d36c88b89c"
  dependencies:
    acorn "^4.0.1"
    acorn-jsx "^3.0.0"

esprima@^2.6.0:
  version "2.7.3"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"

espurify@^1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/espurify/-/espurify-1.6.0.tgz#6cb993582d9422bd6f2d4b258aadb14833f394f0"
  dependencies:
    core-js "^2.0.0"

esrecurse@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.1.0.tgz#4713b6536adf7f2ac4f327d559e7756bff648220"
  dependencies:
    estraverse "~4.1.0"
    object-assign "^4.0.1"

estraverse@^4.0.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

estraverse@~4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.1.1.tgz#f6caca728933a850ef90661d0e17982ba47111a2"

esutils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

event-emitter@~0.3.4:
  version "0.3.4"
  resolved "https://registry.yarnpkg.com/event-emitter/-/event-emitter-0.3.4.tgz#8d63ddfb4cfe1fae3b32ca265c4c720222080bb5"
  dependencies:
    d "~0.1.1"
    es5-ext "~0.10.7"

execa@^0.5.0:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/execa/-/execa-0.5.1.tgz#de3fb85cb8d6e91c85bcbceb164581785cb57b36"
  dependencies:
    cross-spawn "^4.0.0"
    get-stream "^2.2.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/exit-hook/-/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/expand-brackets/-/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/expand-range/-/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

extend@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/extend/-/extend-3.0.0.tgz#5a474353b9f3353ddd8176dfd37b91c83a46f1d4"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/extglob/-/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extsprintf@1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/extsprintf/-/extsprintf-1.0.2.tgz#e1080e0658e300b06294990cc70e1502235fd550"

fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"

figures@^1.3.5:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-2.0.0.tgz#c392990c3e684783d838b8c84a45d8a048458361"
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

filename-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/filename-regex/-/filename-regex-2.0.0.tgz#996e3e80479b98b9897f15a8a58b3d084e926775"

fill-range@^2.1.0:
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-2.2.3.tgz#50b77dfd7e469bc7492470963699fe7a8485a723"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^1.1.3"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

filled-array@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/filled-array/-/filled-array-1.1.0.tgz#c3c4f6c663b923459a9aa29912d2d031f1507f84"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-root@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/find-root/-/find-root-1.0.0.tgz#962ff211aab25c6520feeeb8d6287f8f6e95807a"

find-up@^1.0.0, find-up@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

flat-cache@^1.2.1:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-1.2.2.tgz#fa86714e72c21db88601761ecf2f555d1abc6b96"
  dependencies:
    circular-json "^0.3.1"
    del "^2.0.2"
    graceful-fs "^4.1.2"
    write "^0.2.1"

fn-name@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/fn-name/-/fn-name-2.0.1.tgz#5214d7537a4d06a4a301c0cc262feb84188002e7"

for-in@^0.1.5:
  version "0.1.6"
  resolved "https://registry.yarnpkg.com/for-in/-/for-in-0.1.6.tgz#c9f96e89bfad18a545af5ec3ed352a1d9e5b4dc8"

for-own@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/for-own/-/for-own-0.1.4.tgz#0149b41a39088c7515f51ebe1c1386d45f935072"
  dependencies:
    for-in "^0.1.5"

foreground-child@^1.3.3, foreground-child@^1.5.3:
  version "1.5.6"
  resolved "https://registry.yarnpkg.com/foreground-child/-/foreground-child-1.5.6.tgz#4fd71ad2dfde96789b980a5c0a295937cb2f5ce9"
  dependencies:
    cross-spawn "^4"
    signal-exit "^3.0.0"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.1.1:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.1.2.tgz#89c3534008b97eada4cbb157d58f6f5df025eae4"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.12"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.0.0:
  version "1.0.17"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-1.0.17.tgz#8537f3f12272678765b4fd6528c0f1f66f8f4558"
  dependencies:
    nan "^2.3.0"
    node-pre-gyp "^0.6.29"

fstream-ignore@~1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/fstream-ignore/-/fstream-ignore-1.0.5.tgz#9c31dae34767018fe1d249b24dada67d092da105"
  dependencies:
    fstream "^1.0.0"
    inherits "2"
    minimatch "^3.0.0"

fstream@^1.0.0, fstream@^1.0.2, fstream@~1.0.10:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/fstream/-/fstream-1.0.10.tgz#604e8a92fe26ffd9f6fae30399d4984e1ab22822"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

gauge@~2.7.1:
  version "2.7.2"
  resolved "https://registry.yarnpkg.com/gauge/-/gauge-2.7.2.tgz#15cecc31b02d05345a5d6b0e171cdb3ad2307774"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    supports-color "^0.2.0"
    wide-align "^1.1.0"

generate-function@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/generate-function/-/generate-function-2.0.0.tgz#6858fe7c0969b7d4e9093337647ac79f60dfbe74"

generate-object-property@^1.1.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/generate-object-property/-/generate-object-property-1.2.0.tgz#9c0e1c40308ce804f4783618b937fa88f99d50d0"
  dependencies:
    is-property "^1.0.0"

get-caller-file@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/get-caller-file/-/get-caller-file-1.0.2.tgz#f702e63127e7e231c160a80c1554acb70d5047e5"

get-port@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/get-port/-/get-port-2.1.0.tgz#8783f9dcebd1eea495a334e1a6a251e78887ab1a"
  dependencies:
    pinkie-promise "^2.0.0"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/get-stdin/-/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

get-stdin@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/get-stdin/-/get-stdin-5.0.1.tgz#122e161591e21ff4c52530305693f20e6393a398"

get-stream@^2.2.0:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-2.3.1.tgz#5f38f93f346009666ee0150a054167f91bdd95de"
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

getpass@^0.1.1:
  version "0.1.6"
  resolved "https://registry.yarnpkg.com/getpass/-/getpass-0.1.6.tgz#283ffd9fc1256840875311c1b60e8c40187110e6"
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/glob-base/-/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.0.5, glob@^7.0.6:
  version "7.1.1"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.1.tgz#805211df04faaf1c63a3600306cdf5ade50b2ec8"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^9.0.0, globals@^9.2.0:
  version "9.14.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-9.14.0.tgz#8859936af0038741263053b39d0e76ca241e4034"

globby@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-5.0.0.tgz#ebd84667ca0dbb330b99bcfc68eac2bc54370e0d"
  dependencies:
    array-union "^1.0.1"
    arrify "^1.0.0"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^6.0.0:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

got@^5.0.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/got/-/got-5.7.1.tgz#5f81635a61e4a6589f180569ea4e381680a51f35"
  dependencies:
    create-error-class "^3.0.1"
    duplexer2 "^0.1.4"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    node-status-codes "^1.0.0"
    object-assign "^4.0.1"
    parse-json "^2.1.0"
    pinkie-promise "^2.0.0"
    read-all-stream "^3.0.0"
    readable-stream "^2.0.5"
    timed-out "^3.0.0"
    unzip-response "^1.0.2"
    url-parse-lax "^1.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.1.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

"graceful-readlink@>= 1.0.0":
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/graceful-readlink/-/graceful-readlink-1.0.1.tgz#4cafad76bc62f02fa039b2f94e9a3dd3a391a725"

handlebars@^4.0.3:
  version "4.0.6"
  resolved "https://registry.yarnpkg.com/handlebars/-/handlebars-4.0.6.tgz#2ce4484850537f9c97a8026d5399b935c4ed4ed7"
  dependencies:
    async "^1.4.0"
    optimist "^0.6.1"
    source-map "^0.4.4"
  optionalDependencies:
    uglify-js "^2.6"

har-validator@~2.0.6:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/har-validator/-/har-validator-2.0.6.tgz#cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d"
  dependencies:
    chalk "^1.1.1"
    commander "^2.9.0"
    is-my-json-valid "^2.12.4"
    pinkie-promise "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-color@~0.1.0:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/has-color/-/has-color-0.1.7.tgz#67144a5260c34fc3cca677d041daf52fe7b78b2f"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"

has-flag@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-2.0.0.tgz#e8207af1cc7b30d446cc70b734b5e8be18f88d51"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/has-unicode/-/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

has-yarn@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-yarn/-/has-yarn-1.0.0.tgz#89e25db604b725c8f5976fff0addc921b828a5a7"

hawk@~3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/hawk/-/hawk-3.1.3.tgz#078444bd7c1640b0fe540d2c9b73d59678e8e1c4"
  dependencies:
    boom "2.x.x"
    cryptiles "2.x.x"
    hoek "2.x.x"
    sntp "1.x.x"

hoek@2.x.x:
  version "2.16.3"
  resolved "https://registry.yarnpkg.com/hoek/-/hoek-2.16.3.tgz#20bb7403d3cea398e91dc4710a8ff1b8274a25ed"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/home-or-tmp/-/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/hosted-git-info/-/hosted-git-info-2.1.5.tgz#0ba81d90da2e25ab34a332e6ec77936e1598118b"

http-signature@~1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/http-signature/-/http-signature-1.1.1.tgz#df72e267066cd0ac67fb76adf8e134a8fbcf91bf"
  dependencies:
    assert-plus "^0.2.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

ignore-by-default@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/ignore-by-default/-/ignore-by-default-1.0.1.tgz#48ca6d72f6c6a3af00a9ad4ae6876be3889e2b09"

ignore@^3.0.9, ignore@^3.2.0:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/ignore/-/ignore-3.2.2.tgz#1c51e1ef53bab6ddc15db4d9ac4ec139eceb3410"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/indent-string/-/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

indent-string@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/indent-string/-/indent-string-3.1.0.tgz#08ff4334603388399b329e6b9538dc7a3cf5de7d"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

ini@~1.3.0:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/ini/-/ini-1.3.4.tgz#0537cb79daf59b59a1a517dff706c86ec039162e"

inquirer@^0.12.0:
  version "0.12.0"
  resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-0.12.0.tgz#1ef2bfd63504df0bc75785fff8c2c41df12f077e"
  dependencies:
    ansi-escapes "^1.1.0"
    ansi-regex "^2.0.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^2.0.0"
    figures "^1.3.5"
    lodash "^4.3.0"
    readline2 "^1.0.1"
    run-async "^0.1.0"
    rx-lite "^3.1.2"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/interpret/-/interpret-1.0.1.tgz#d579fb7f693b858004947af39fa0db49f795602c"

invariant@^2.2.0:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/invariant/-/invariant-2.2.2.tgz#9e1f56ac0acdb6bf303306f338be3b204ae60360"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/invert-kv/-/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

irregular-plurals@^1.0.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/irregular-plurals/-/irregular-plurals-1.2.0.tgz#38f299834ba8c00c30be9c554e137269752ff3ac"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.0.2:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.4.tgz#cfc86ccd5dc5a52fa80489111c6920c457e2d98b"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-builtin-module/-/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-ci@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-ci/-/is-ci-1.0.10.tgz#f739336b2632365061a9d48270cd56ae3369318e"
  dependencies:
    ci-info "^1.0.0"

is-dotfile@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-dotfile/-/is-dotfile-1.0.2.tgz#2c132383f39199f8edc268ca01b9b007d205cc4d"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-error@^2.2.0:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/is-error/-/is-error-2.2.1.tgz#684a96d84076577c98f4cdb40c6d26a5123bf19c"

is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-extendable/-/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-finite@^1.0.0, is-finite@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-generator-fn@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-generator-fn/-/is-generator-fn-1.0.0.tgz#969d49e1bb3329f6bb7f09089be26578b2ddd46a"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-my-json-valid@^2.10.0, is-my-json-valid@^2.12.4:
  version "2.15.0"
  resolved "https://registry.yarnpkg.com/is-my-json-valid/-/is-my-json-valid-2.15.0.tgz#936edda3ca3c211fd98f3b2d3e08da43f7b2915b"
  dependencies:
    generate-function "^2.0.0"
    generate-object-property "^1.1.0"
    jsonpointer "^4.0.0"
    xtend "^4.0.0"

is-npm@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-npm/-/is-npm-1.0.0.tgz#f2fb63a65e4905b406c86072765a1a4dc793b9f4"

is-number@^2.0.2, is-number@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-obj@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-obj/-/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"

is-observable@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/is-observable/-/is-observable-0.2.0.tgz#b361311d83c6e5d726cabf5e250b0237106f5ae2"
  dependencies:
    symbol-observable "^0.2.2"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-cwd/-/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz#6477582b8214d602346094567003be8a9eac04dc"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-1.0.0.tgz#fc06e5a1683fbda13de667aff717bbc10a48f37f"
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-primitive/-/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"

is-property@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-property/-/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-redirect/-/is-redirect-1.0.0.tgz#1d03dded53bd8db0f30c26e4f95d36fc7c87dc24"

is-resolvable@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-resolvable/-/is-resolvable-1.0.0.tgz#8df57c61ea2e3c501408d100fb013cf8d6e0cc62"
  dependencies:
    tryit "^1.0.1"

is-retry-allowed@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-retry-allowed/-/is-retry-allowed-1.1.0.tgz#11a060568b67339444033d0125a61a20d564fb34"

is-stream@^1.0.0, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-url@^1.2.1:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/is-url/-/is-url-1.2.2.tgz#498905a593bf47cc2d9e7f738372bbf7696c7f26"

is-utf8@^0.2.0, is-utf8@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-utf8/-/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isexe@^1.1.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/isexe/-/isexe-1.1.2.tgz#36f3e22e60750920f5e7241a476a8c6a42275ad0"

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/isobject/-/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/isstream/-/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

istanbul-lib-coverage@^1.0.0, istanbul-lib-coverage@^1.0.0-alpha, istanbul-lib-coverage@^1.0.0-alpha.0, istanbul-lib-coverage@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/istanbul-lib-coverage/-/istanbul-lib-coverage-1.0.1.tgz#f263efb519c051c5f1f3343034fc40e7b43ff212"

istanbul-lib-hook@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/istanbul-lib-hook/-/istanbul-lib-hook-1.0.0.tgz#fc5367ee27f59268e8f060b0c7aaf051d9c425c5"
  dependencies:
    append-transform "^0.4.0"

istanbul-lib-instrument@^1.4.2:
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/istanbul-lib-instrument/-/istanbul-lib-instrument-1.4.2.tgz#0e2fdfac93c1dabf2e31578637dc78a19089f43e"
  dependencies:
    babel-generator "^6.18.0"
    babel-template "^6.16.0"
    babel-traverse "^6.18.0"
    babel-types "^6.18.0"
    babylon "^6.13.0"
    istanbul-lib-coverage "^1.0.0"
    semver "^5.3.0"

istanbul-lib-report@^1.0.0-alpha.3:
  version "1.0.0-alpha.3"
  resolved "https://registry.yarnpkg.com/istanbul-lib-report/-/istanbul-lib-report-1.0.0-alpha.3.tgz#32d5f6ec7f33ca3a602209e278b2e6ff143498af"
  dependencies:
    async "^1.4.2"
    istanbul-lib-coverage "^1.0.0-alpha"
    mkdirp "^0.5.1"
    path-parse "^1.0.5"
    rimraf "^2.4.3"
    supports-color "^3.1.2"

istanbul-lib-source-maps@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.1.0.tgz#9d429218f35b823560ea300a96ff0c3bbdab785f"
  dependencies:
    istanbul-lib-coverage "^1.0.0-alpha.0"
    mkdirp "^0.5.1"
    rimraf "^2.4.4"
    source-map "^0.5.3"

istanbul-reports@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/istanbul-reports/-/istanbul-reports-1.0.1.tgz#9a17176bc4a6cbebdae52b2f15961d52fa623fbc"
  dependencies:
    handlebars "^4.0.3"

jest-diff@^18.1.0:
  version "18.1.0"
  resolved "https://registry.yarnpkg.com/jest-diff/-/jest-diff-18.1.0.tgz#4ff79e74dd988c139195b365dc65d87f606f4803"
  dependencies:
    chalk "^1.1.3"
    diff "^3.0.0"
    jest-matcher-utils "^18.1.0"
    pretty-format "^18.1.0"

jest-file-exists@^17.0.0:
  version "17.0.0"
  resolved "https://registry.yarnpkg.com/jest-file-exists/-/jest-file-exists-17.0.0.tgz#7f63eb73a1c43a13f461be261768b45af2cdd169"

jest-matcher-utils@^18.1.0:
  version "18.1.0"
  resolved "https://registry.yarnpkg.com/jest-matcher-utils/-/jest-matcher-utils-18.1.0.tgz#1ac4651955ee2a60cef1e7fcc98cdfd773c0f932"
  dependencies:
    chalk "^1.1.3"
    pretty-format "^18.1.0"

jest-mock@^18.0.0:
  version "18.0.0"
  resolved "https://registry.yarnpkg.com/jest-mock/-/jest-mock-18.0.0.tgz#5c248846ea33fa558b526f5312ab4a6765e489b3"

jest-snapshot@^18.1.0:
  version "18.1.0"
  resolved "https://registry.yarnpkg.com/jest-snapshot/-/jest-snapshot-18.1.0.tgz#55b96d2ee639c9bce76f87f2a3fd40b71c7a5916"
  dependencies:
    jest-diff "^18.1.0"
    jest-file-exists "^17.0.0"
    jest-matcher-utils "^18.1.0"
    jest-util "^18.1.0"
    natural-compare "^1.4.0"
    pretty-format "^18.1.0"

jest-util@^18.1.0:
  version "18.1.0"
  resolved "https://registry.yarnpkg.com/jest-util/-/jest-util-18.1.0.tgz#3a99c32114ab17f84be094382527006e6d4bfc6a"
  dependencies:
    chalk "^1.1.1"
    diff "^3.0.0"
    graceful-fs "^4.1.6"
    jest-file-exists "^17.0.0"
    jest-mock "^18.0.0"
    mkdirp "^0.5.1"

jodid25519@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/jodid25519/-/jodid25519-1.0.2.tgz#06d4912255093419477d425633606e0e90782967"
  dependencies:
    jsbn "~0.1.0"

join-component@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/join-component/-/join-component-1.1.0.tgz#b8417b750661a392bee2c2537c68b2a9d4977cd5"

js-tokens@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.1.tgz#08e9f132484a2c45a30907e9dc4d5567b7f114d7"

js-yaml@^3.5.1:
  version "3.7.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.7.0.tgz#5c967ddd837a9bfdca5f2de84253abe8a1c03b80"
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

jsbn@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/jsbn/-/jsbn-0.1.0.tgz#650987da0dd74f4ebf5a11377a2aa2d273e97dfd"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/json-schema/-/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stable-stringify@^1.0.0, json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/json5/-/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

jsonpointer@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/jsonpointer/-/jsonpointer-4.0.1.tgz#4fd92cb34e0e9db3c89c8622ecf51f9b978c6cb9"

jsprim@^1.2.2:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/jsprim/-/jsprim-1.3.1.tgz#2a7256f70412a29ee3670aaca625994c4dcff252"
  dependencies:
    extsprintf "1.0.2"
    json-schema "0.2.3"
    verror "1.3.6"

jsx-ast-utils@^1.3.3:
  version "1.3.5"
  resolved "https://registry.yarnpkg.com/jsx-ast-utils/-/jsx-ast-utils-1.3.5.tgz#9ba6297198d9f754594d62e59496ffb923778dd4"
  dependencies:
    acorn-jsx "^3.0.1"
    object-assign "^4.1.0"

kind-of@^3.0.2:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/kind-of/-/kind-of-3.1.0.tgz#475d698a5e49ff5e53d14e3e732429dc8bf4cf47"
  dependencies:
    is-buffer "^1.0.2"

last-line-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/last-line-stream/-/last-line-stream-1.0.0.tgz#d1b64d69f86ff24af2d04883a2ceee14520a5600"
  dependencies:
    through2 "^2.0.0"

latest-version@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/latest-version/-/latest-version-2.0.0.tgz#56f8d6139620847b8017f8f1f4d78e211324168b"
  dependencies:
    package-json "^2.0.0"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"

lazy-req@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/lazy-req/-/lazy-req-1.1.0.tgz#bdaebead30f8d824039ce0ce149d4daa07ba1fac"

lcid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/lcid/-/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash.debounce@^4.0.3:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"

lodash.difference@^4.3.0:
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/lodash.difference/-/lodash.difference-4.5.0.tgz#9ccb4e505d486b91651345772885a2df27fd017c"

lodash.flatten@^4.2.0:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/lodash.flatten/-/lodash.flatten-4.4.0.tgz#f31c22225a9632d2bbf8e4addbef240aa765a61f"

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "https://registry.yarnpkg.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"

lodash@^4.0.0, lodash@^4.2.0, lodash@^4.3.0:
  version "4.17.4"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.4.tgz#78203a4d1c328ae1d86dca6460e369b57f4055ae"

longest@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"

loose-envify@^1.0.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.3.1.tgz#d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848"
  dependencies:
    js-tokens "^3.0.0"

loud-rejection@^1.0.0, loud-rejection@^1.2.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/loud-rejection/-/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lowercase-keys@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/lowercase-keys/-/lowercase-keys-1.0.0.tgz#4e3366b39e7f5457e35f1324bdf6f88d0bfc7306"

lru-cache@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.0.2.tgz#1d17679c069cda5d040991a09dbc2c0db377e55e"
  dependencies:
    pseudomap "^1.0.1"
    yallist "^2.0.0"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

matcher@^0.1.1:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/matcher/-/matcher-0.1.2.tgz#ef20cbde64c24c50cc61af5b83ee0b1b8ff00101"
  dependencies:
    escape-string-regexp "^1.0.4"

max-timeout@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/max-timeout/-/max-timeout-1.0.0.tgz#b68f69a2f99e0b476fd4cb23e2059ca750715e1f"

md5-hex@^1.2.0, md5-hex@^1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/md5-hex/-/md5-hex-1.3.0.tgz#d2c4afe983c4370662179b8cad145219135046c4"
  dependencies:
    md5-o-matic "^0.1.1"

md5-hex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/md5-hex/-/md5-hex-2.0.0.tgz#d0588e9f1c74954492ecd24ac0ac6ce997d92e33"
  dependencies:
    md5-o-matic "^0.1.1"

md5-o-matic@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/md5-o-matic/-/md5-o-matic-0.1.1.tgz#822bccd65e117c514fab176b25945d54100a03c3"

meow@^3.7.0:
  version "3.7.0"
  resolved "https://registry.yarnpkg.com/meow/-/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-source-map@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/merge-source-map/-/merge-source-map-1.0.3.tgz#da1415f2722a5119db07b14c4f973410863a2abf"
  dependencies:
    source-map "^0.5.3"

micromatch@^2.1.5, micromatch@^2.3.11:
  version "2.3.11"
  resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

mime-db@~1.26.0:
  version "1.26.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.26.0.tgz#eaffcd0e4fc6935cf8134da246e2e6c35305adff"

mime-types@^2.1.12, mime-types@~2.1.7:
  version "2.1.14"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.14.tgz#f7ef7d97583fcaf3b7d282b6f8b5679dab1e94ee"
  dependencies:
    mime-db "~1.26.0"

mimic-fn@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.1.0.tgz#e667783d92e89dbd342818b5230b9d62a672ad18"

minimatch@^3.0.0, minimatch@^3.0.2:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.3.tgz#2a4e4090b96b2db06a9d7df01055a62a77c9b774"
  dependencies:
    brace-expansion "^1.0.0"

minimist@0.0.8, minimist@~0.0.1:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@^1.1.0, minimist@^1.1.3, minimist@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

"mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@~0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

ms@0.7.1:
  version "0.7.1"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.1.tgz#9cd13c03adbff25b65effde7ce864ee952017098"

ms@0.7.2, ms@^0.7.1:
  version "0.7.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-0.7.2.tgz#ae25cf2512b3885a1d95d7f037868d8431124765"

multimatch@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/multimatch/-/multimatch-2.1.0.tgz#9c7906a22fb4c02919e2f5f75161b4cdbd4b2a2b"
  dependencies:
    array-differ "^1.0.0"
    array-union "^1.0.1"
    arrify "^1.0.0"
    minimatch "^3.0.0"

mute-stream@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.5.tgz#8fbfabb0a98a253d3184331f9e8deb7372fac6c0"

nan@^2.3.0:
  version "2.5.1"
  resolved "https://registry.yarnpkg.com/nan/-/nan-2.5.1.tgz#d5b01691253326a97a2bbee9e61c55d8d60351e2"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"

node-pre-gyp@^0.6.29:
  version "0.6.33"
  resolved "https://registry.yarnpkg.com/node-pre-gyp/-/node-pre-gyp-0.6.33.tgz#640ac55198f6a925972e0c16c4ac26a034d5ecc9"
  dependencies:
    mkdirp "~0.5.1"
    nopt "~3.0.6"
    npmlog "^4.0.1"
    rc "~1.1.6"
    request "^2.79.0"
    rimraf "~2.5.4"
    semver "~5.3.0"
    tar "~2.2.1"
    tar-pack "~3.3.0"

node-status-codes@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/node-status-codes/-/node-status-codes-1.0.0.tgz#5ae5541d024645d32a58fcddc9ceecea7ae3ac2f"

nopt@~3.0.6:
  version "3.0.6"
  resolved "https://registry.yarnpkg.com/nopt/-/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.3.5"
  resolved "https://registry.yarnpkg.com/normalize-package-data/-/normalize-package-data-2.3.5.tgz#8d924f142960e1777e7ffe170543631cc7cb02df"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-2.0.1.tgz#47886ac1662760d4261b7d979d241709d3ce3f7a"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/npm-run-path/-/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  dependencies:
    path-key "^2.0.0"

npmlog@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/npmlog/-/npmlog-4.0.2.tgz#d03950e0e78ce1527ba26d2a7592e9348ac3e75f"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.1"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

nyc@^10.1.2:
  version "10.1.2"
  resolved "https://registry.yarnpkg.com/nyc/-/nyc-10.1.2.tgz#ea7acaa20a235210101604f4e7d56d28453b0274"
  dependencies:
    archy "^1.0.0"
    arrify "^1.0.1"
    caching-transform "^1.0.0"
    convert-source-map "^1.3.0"
    debug-log "^1.0.1"
    default-require-extensions "^1.0.0"
    find-cache-dir "^0.1.1"
    find-up "^1.1.2"
    foreground-child "^1.5.3"
    glob "^7.0.6"
    istanbul-lib-coverage "^1.0.1"
    istanbul-lib-hook "^1.0.0"
    istanbul-lib-instrument "^1.4.2"
    istanbul-lib-report "^1.0.0-alpha.3"
    istanbul-lib-source-maps "^1.1.0"
    istanbul-reports "^1.0.0"
    md5-hex "^1.2.0"
    merge-source-map "^1.0.2"
    micromatch "^2.3.11"
    mkdirp "^0.5.0"
    resolve-from "^2.0.0"
    rimraf "^2.5.4"
    signal-exit "^3.0.1"
    spawn-wrap "1.2.4"
    test-exclude "^3.3.0"
    yargs "^6.6.0"
    yargs-parser "^4.0.2"

oauth-sign@~0.8.1:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/oauth-sign/-/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/object.omit/-/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

observable-to-promise@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/observable-to-promise/-/observable-to-promise-0.4.0.tgz#28afe71645308f2d41d71f47ad3fece1a377e52b"
  dependencies:
    is-observable "^0.2.0"
    symbol-observable "^0.2.2"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

once@~1.3.3:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/once/-/once-1.3.3.tgz#b2e261557ce4c314ec8304f3fa82663e4297ca20"
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"

onetime@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-2.0.0.tgz#52aa8110e52fc5126ffc667bd8ec21c2ed209ce6"
  dependencies:
    mimic-fn "^1.0.0"

optimist@^0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

option-chain@^0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/option-chain/-/option-chain-0.1.1.tgz#e9b811e006f1c0f54802f28295bfc8970f8dcfbd"
  dependencies:
    object-assign "^4.0.1"

optionator@^0.8.2:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-homedir/-/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/os-locale/-/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  dependencies:
    lcid "^1.0.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@^0.1.0:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/osenv/-/osenv-0.1.4.tgz#42fe6d5953df06c8064be6f176c3d05aaaa34644"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/p-finally/-/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"

p-limit@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-1.1.0.tgz#b07ff2d9a5d88bec806035895a2bab66a27988bc"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

package-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/package-hash/-/package-hash-1.2.0.tgz#003e56cd57b736a6ed6114cc2b81542672770e44"
  dependencies:
    md5-hex "^1.3.0"

package-json@^2.0.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/package-json/-/package-json-2.4.0.tgz#0d15bd67d1cbbddbb2ca222ff2edb86bcb31a8bb"
  dependencies:
    got "^5.0.0"
    registry-auth-token "^3.0.1"
    registry-url "^3.0.3"
    semver "^5.1.0"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/parse-glob/-/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.1.0, parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parse-ms@^0.1.0:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/parse-ms/-/parse-ms-0.1.2.tgz#dd3fa25ed6c2efc7bdde12ad9b46c163aa29224e"

parse-ms@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parse-ms/-/parse-ms-1.0.1.tgz#56346d4749d78f23430ca0c713850aef91aa361d"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-key@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/path-key/-/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  dependencies:
    pify "^2.0.0"

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-1.0.0.tgz#d1da67f5482563bb7cf57f286ae2822ecfbf3670"
  dependencies:
    pinkie "^1.0.0"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-1.0.0.tgz#5a47f28ba1015d0201bda7bf0f358e47bec8c7e4"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-conf@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/pkg-conf/-/pkg-conf-2.0.0.tgz#071c87650403bccfb9c627f58751bfe47c067279"
  dependencies:
    find-up "^2.0.0"
    load-json-file "^2.0.0"

pkg-config@^1.0.1, pkg-config@^1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/pkg-config/-/pkg-config-1.1.1.tgz#557ef22d73da3c8837107766c52eadabde298fe4"
  dependencies:
    debug-log "^1.0.0"
    find-root "^1.0.0"
    xtend "^4.0.1"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  dependencies:
    find-up "^1.0.0"

plur@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/plur/-/plur-1.0.0.tgz#db85c6814f5e5e5a3b49efc28d604fec62975156"

plur@^2.0.0:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/plur/-/plur-2.1.2.tgz#7482452c1a0f508e3e344eaec312c91c29dc655a"
  dependencies:
    irregular-plurals "^1.0.0"

pluralize@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/pluralize/-/pluralize-1.2.1.tgz#d1a21483fd22bb41e58a12fa3421823140897c45"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"

prepend-http@^1.0.1:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/prepend-http/-/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/preserve/-/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

pretty-format@^18.1.0:
  version "18.1.0"
  resolved "https://registry.yarnpkg.com/pretty-format/-/pretty-format-18.1.0.tgz#fb65a86f7a7f9194963eee91865c1bcf1039e284"
  dependencies:
    ansi-styles "^2.2.1"

pretty-ms@^0.2.1:
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/pretty-ms/-/pretty-ms-0.2.2.tgz#da879a682ff33a37011046f13d627f67c73b84f6"
  dependencies:
    parse-ms "^0.1.0"

pretty-ms@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/pretty-ms/-/pretty-ms-2.1.0.tgz#4257c256df3fb0b451d6affaab021884126981dc"
  dependencies:
    is-finite "^1.0.1"
    parse-ms "^1.0.0"
    plur "^1.0.0"

private@^0.1.6:
  version "0.1.6"
  resolved "https://registry.yarnpkg.com/private/-/private-0.1.6.tgz#55c6a976d0f9bafb9924851350fe47b9b5fbb7c1"

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-1.0.7.tgz#150e20b756590ad3f91093f25a4f2ad8bff30ba3"

progress@^1.1.8:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/progress/-/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

pseudomap@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

punycode@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

qs@~6.3.0:
  version "6.3.0"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.3.0.tgz#f403b264f23bc01228c74131b407f18d5ea5d442"

randomatic@^1.1.3:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/randomatic/-/randomatic-1.1.6.tgz#110dcabff397e9dcff7c0789ccc0a49adf1ec5bb"
  dependencies:
    is-number "^2.0.2"
    kind-of "^3.0.2"

rc@^1.0.1, rc@^1.1.6, rc@~1.1.6:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/rc/-/rc-1.1.6.tgz#43651b76b6ae53b5c802f1151fa3fc3b059969c9"
  dependencies:
    deep-extend "~0.4.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~1.0.4"

read-all-stream@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/read-all-stream/-/read-all-stream-3.1.0.tgz#35c3e177f2078ef789ee4bfafa4373074eaef4fa"
  dependencies:
    pinkie-promise "^2.0.0"
    readable-stream "^2.0.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

readable-stream@^2.0.0, "readable-stream@^2.0.0 || ^1.1.13", readable-stream@^2.0.2, readable-stream@^2.0.5, readable-stream@^2.1.5, readable-stream@^2.2.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.2.2.tgz#a9e6fec3c7dda85f8bb1b3ba7028604556fc825e"
  dependencies:
    buffer-shims "^1.0.0"
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readable-stream@~2.1.4:
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.1.5.tgz#66fa8b720e1438b364681f2ad1a63c618448c9d0"
  dependencies:
    buffer-shims "^1.0.0"
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readdirp@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/readdirp/-/readdirp-2.1.0.tgz#4ed0ad060df3073300c48440373f72d1cc642d78"
  dependencies:
    graceful-fs "^4.1.2"
    minimatch "^3.0.2"
    readable-stream "^2.0.2"
    set-immediate-shim "^1.0.1"

readline2@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/readline2/-/readline2-1.0.1.tgz#41059608ffc154757b715d9989d199ffbf372e35"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    mute-stream "0.0.5"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/rechoir/-/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/redent/-/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

regenerate@^1.2.1:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/regenerate/-/regenerate-1.3.2.tgz#d1941c67bad437e1be76433add5b385f95b19260"

regenerator-runtime@^0.10.0:
  version "0.10.1"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.10.1.tgz#257f41961ce44558b18f7814af48c17559f9faeb"

regex-cache@^0.4.2:
  version "0.4.3"
  resolved "http://registry.npmjs.org/regex-cache/-/regex-cache-0.4.3.tgz#9b1a6c35d4d0dfcef5711ae651e8e9d3d7114145"
  dependencies:
    is-equal-shallow "^0.1.3"
    is-primitive "^2.0.0"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/regexpu-core/-/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

registry-auth-token@^3.0.1:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/registry-auth-token/-/registry-auth-token-3.1.0.tgz#997c08256e0c7999837b90e944db39d8a790276b"
  dependencies:
    rc "^1.1.6"

registry-url@^3.0.3:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/registry-url/-/registry-url-3.1.0.tgz#3d4ef870f73dde1d77f0cf9a381432444e174942"
  dependencies:
    rc "^1.0.1"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/regjsgen/-/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/regjsparser/-/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  dependencies:
    jsesc "~0.5.0"

repeat-element@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/repeat-element/-/repeat-element-1.1.2.tgz#ef089a178d1483baae4d93eb98b4f9e4e11d990a"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "https://registry.yarnpkg.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/repeating/-/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

request@^2.79.0:
  version "2.79.0"
  resolved "https://registry.yarnpkg.com/request/-/request-2.79.0.tgz#4dfe5bf6be8b8cdc37fcf93e04b65577722710de"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.11.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~2.0.6"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    oauth-sign "~0.8.1"
    qs "~6.3.0"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "~0.4.1"
    uuid "^3.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/require-main-filename/-/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

require-precompiled@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/require-precompiled/-/require-precompiled-0.1.0.tgz#5a1b52eb70ebed43eb982e974c85ab59571e56fa"

require-uncached@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/require-uncached/-/require-uncached-1.0.3.tgz#4e0d56d6c9662fd31e43011c4b95aa49955421d3"
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

resolve-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/resolve-cwd/-/resolve-cwd-1.0.0.tgz#4eaeea41ed040d1702457df64a42b2b07d246f9f"
  dependencies:
    resolve-from "^2.0.0"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-1.0.1.tgz#26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226"

resolve-from@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-2.0.0.tgz#9480ab20e94ffa1d9e80a804c7ea147611966b57"

resolve@^1.1.6:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.2.0.tgz#9589c3f2f6149d1417a40becc1663db6ec6bc26c"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  dependencies:
    align-text "^0.1.1"

rimraf@2, rimraf@^2.2.8, rimraf@^2.3.3, rimraf@^2.4.3, rimraf@^2.4.4, rimraf@^2.5.4, rimraf@~2.5.1, rimraf@~2.5.4:
  version "2.5.4"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.5.4.tgz#96800093cbf1a0c86bd95b4625467535c29dfa04"
  dependencies:
    glob "^7.0.5"

run-async@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/run-async/-/run-async-0.1.0.tgz#c8ad4a5e110661e402a7d21b530e009f25f8e389"
  dependencies:
    once "^1.3.0"

run-parallel@^1.1.2:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/run-parallel/-/run-parallel-1.1.6.tgz#29003c9a2163e01e2d2dfc90575f2c6c1d61a039"

rx-lite@^3.1.2:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-3.1.2.tgz#19ce502ca572665f3b647b10939f97fd1615f102"

semver-diff@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/semver-diff/-/semver-diff-2.1.0.tgz#4bbb8437c8d37e4b0cf1a68fd726ec6d645d6d36"
  dependencies:
    semver "^5.0.3"

"semver@2 || 3 || 4 || 5", semver@^5.0.3, semver@^5.1.0, semver@^5.3.0, semver@~5.3.0:
  version "5.3.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/set-blocking/-/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-immediate-shim@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz#4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61"

shelljs@^0.7.5:
  version "0.7.6"
  resolved "https://registry.yarnpkg.com/shelljs/-/shelljs-0.7.6.tgz#379cccfb56b91c8601e4793356eb5382924de9ad"
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

signal-exit@^2.0.0:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-2.1.2.tgz#375879b1f92ebc3b334480d038dc546a6d558564"

signal-exit@^3.0.0, signal-exit@^3.0.1, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/slice-ansi/-/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"

slide@^1.1.5:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/slide/-/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"

sntp@1.x.x:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/sntp/-/sntp-1.0.9.tgz#6541184cc90aeea6c6e7b35e2659082443c66198"
  dependencies:
    hoek "2.x.x"

sort-keys@^1.1.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/sort-keys/-/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  dependencies:
    is-plain-obj "^1.0.0"

source-map-support@^0.4.0, source-map-support@^0.4.2:
  version "0.4.11"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.4.11.tgz#647f939978b38535909530885303daf23279f322"
  dependencies:
    source-map "^0.5.3"

source-map@^0.4.4:
  version "0.4.4"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@~0.5.1:
  version "0.5.6"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"

spawn-wrap@1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/spawn-wrap/-/spawn-wrap-1.2.4.tgz#920eb211a769c093eebfbd5b0e7a5d2e68ab2e40"
  dependencies:
    foreground-child "^1.3.3"
    mkdirp "^0.5.0"
    os-homedir "^1.0.1"
    rimraf "^2.3.3"
    signal-exit "^2.0.0"
    which "^1.2.4"

spdx-correct@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/spdx-correct/-/spdx-correct-1.0.2.tgz#4b3073d933ff51f3912f03ac5519498a4150db40"
  dependencies:
    spdx-license-ids "^1.0.2"

spdx-expression-parse@~1.0.0:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz#9bdf2f20e1f40ed447fbe273266191fced51626c"

spdx-license-ids@^1.0.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz#c9df7a3424594ade6bd11900d596696dc06bac57"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.10.2"
  resolved "https://registry.yarnpkg.com/sshpk/-/sshpk-1.10.2.tgz#d5a804ce22695515638e798dbe23273de070a5fa"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    dashdash "^1.12.0"
    getpass "^0.1.1"
  optionalDependencies:
    bcrypt-pbkdf "^1.0.0"
    ecc-jsbn "~0.1.1"
    jodid25519 "^1.0.0"
    jsbn "~0.1.0"
    tweetnacl "~0.14.0"

stack-utils@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/stack-utils/-/stack-utils-0.4.0.tgz#940cb82fccfa84e8ff2f3fdf293fe78016beccd1"

standard-engine@~5.2.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/standard-engine/-/standard-engine-5.2.0.tgz#400660ae5acce8afd4db60ff2214a9190ad790a3"
  dependencies:
    deglob "^2.0.0"
    find-root "^1.0.0"
    get-stdin "^5.0.1"
    home-or-tmp "^2.0.0"
    minimist "^1.1.0"
    pkg-config "^1.0.1"

standard@^8.6.0:
  version "8.6.0"
  resolved "https://registry.yarnpkg.com/standard/-/standard-8.6.0.tgz#635132be7bfb567c2921005f30f9e350e4752aad"
  dependencies:
    eslint "~3.10.2"
    eslint-config-standard "6.2.1"
    eslint-config-standard-jsx "3.2.0"
    eslint-plugin-promise "~3.4.0"
    eslint-plugin-react "~6.7.1"
    eslint-plugin-standard "~2.0.1"
    standard-engine "~5.2.0"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.0.0.tgz#635c5436cc72a6e0c387ceca278d4e2eec52687e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^3.0.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

stringstream@~0.0.4:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/stringstream/-/stringstream-0.0.5.tgz#4e484cd4de5a0bbbee18e46307710a8a81621878"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@~0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-0.1.1.tgz#39e8a98d044d150660abe4a6808acf70bb7bc991"

strip-bom-buf@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom-buf/-/strip-bom-buf-1.0.0.tgz#1cb45aaf57530f4caf86c7f75179d2c9a51dd572"
  dependencies:
    is-utf8 "^0.2.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/strip-eof/-/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/strip-indent/-/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@~1.0.1, strip-json-comments@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-1.0.4.tgz#1e15fbcac97d3ee99bf2d73b4c656b082bbafb91"

supports-color@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-0.2.0.tgz#d92de2694eb3f67323973d7ae3d8b55b4c22190a"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^3.1.2:
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  dependencies:
    has-flag "^1.0.0"

symbol-observable@^0.2.2:
  version "0.2.4"
  resolved "https://registry.yarnpkg.com/symbol-observable/-/symbol-observable-0.2.4.tgz#95a83db26186d6af7e7a18dbd9760a2f86d08f40"

table@^3.7.8:
  version "3.8.3"
  resolved "https://registry.yarnpkg.com/table/-/table-3.8.3.tgz#2bbc542f0fda9861a755d3947fefd8b3f513855f"
  dependencies:
    ajv "^4.7.0"
    ajv-keywords "^1.0.0"
    chalk "^1.1.1"
    lodash "^4.0.0"
    slice-ansi "0.0.4"
    string-width "^2.0.0"

tar-pack@~3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/tar-pack/-/tar-pack-3.3.0.tgz#30931816418f55afc4d21775afdd6720cee45dae"
  dependencies:
    debug "~2.2.0"
    fstream "~1.0.10"
    fstream-ignore "~1.0.5"
    once "~1.3.3"
    readable-stream "~2.1.4"
    rimraf "~2.5.1"
    tar "~2.2.1"
    uid-number "~0.0.6"

tar@~2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/tar/-/tar-2.2.1.tgz#8e4d2a256c0e2185c6b18ad694aec968b83cb1d1"
  dependencies:
    block-stream "*"
    fstream "^1.0.2"
    inherits "2"

test-exclude@^3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/test-exclude/-/test-exclude-3.3.0.tgz#7a17ca1239988c98367b0621456dbb7d4bc38977"
  dependencies:
    arrify "^1.0.1"
    micromatch "^2.3.11"
    object-assign "^4.1.0"
    read-pkg-up "^1.0.1"
    require-main-filename "^1.0.1"

text-table@^0.2.0, text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"

through2@^2.0.0:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/through2/-/through2-2.0.3.tgz#0004569b37c7c74ba39c43f3ced78d1ad94140be"
  dependencies:
    readable-stream "^2.1.5"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

time-require@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/time-require/-/time-require-0.1.2.tgz#f9e12cb370fc2605e11404582ba54ef5ca2b2d98"
  dependencies:
    chalk "^0.4.0"
    date-time "^0.1.1"
    pretty-ms "^0.2.1"
    text-table "^0.2.0"

timed-out@^3.0.0:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/timed-out/-/timed-out-3.1.3.tgz#95860bfcc5c76c277f8f8326fd0f5b2e20eba217"

to-fast-properties@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-1.0.2.tgz#f3f5c0c3ba7299a7ef99427e44633257ade43320"

tough-cookie@~2.3.0:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/tough-cookie/-/tough-cookie-2.3.2.tgz#f081f76e4c85720e6c37a5faced737150d84072a"
  dependencies:
    punycode "^1.4.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/trim-newlines/-/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

tryit@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/tryit/-/tryit-1.0.3.tgz#393be730a9446fd1ead6da59a014308f36c289cb"

tunnel-agent@~0.4.1:
  version "0.4.3"
  resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.4.3.tgz#6373db76909fe570e08d73583365ed828a74eeeb"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.yarnpkg.com/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  dependencies:
    prelude-ls "~1.1.2"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

uglify-js@^2.6:
  version "2.7.5"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.7.5.tgz#4612c0c7baaee2ba7c487de4904ae122079f2ca8"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

uid-number@~0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/uid-number/-/uid-number-0.0.6.tgz#0ea10e8035e8eb5b8e4449f06da1c730663baa81"

uid2@0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/uid2/-/uid2-0.0.3.tgz#483126e11774df2f71b8b639dcd799c376162b82"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/uniq/-/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"

unique-temp-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/unique-temp-dir/-/unique-temp-dir-1.0.0.tgz#6dce95b2681ca003eebfb304a415f9cbabcc5385"
  dependencies:
    mkdirp "^0.5.1"
    os-tmpdir "^1.0.1"
    uid2 "0.0.3"

unzip-response@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/unzip-response/-/unzip-response-1.0.2.tgz#b984f0877fc0a89c2c773cc1ef7b5b232b5b06fe"

update-notifier@^1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/update-notifier/-/update-notifier-1.0.3.tgz#8f92c515482bd6831b7c93013e70f87552c7cf5a"
  dependencies:
    boxen "^0.6.0"
    chalk "^1.0.0"
    configstore "^2.0.0"
    is-npm "^1.0.0"
    latest-version "^2.0.0"
    lazy-req "^1.1.0"
    semver-diff "^2.0.0"
    xdg-basedir "^2.0.0"

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/url-parse-lax/-/url-parse-lax-1.0.0.tgz#7af8f303645e9bd79a272e7a14ac68bc0609da73"
  dependencies:
    prepend-http "^1.0.1"

user-home@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/user-home/-/user-home-2.0.0.tgz#9c70bfd8169bc1dcbf48604e0f04b8b49cde9e9f"
  dependencies:
    os-homedir "^1.0.0"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

uuid@^2.0.1:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-2.0.3.tgz#67e2e863797215530dff318e5bf9dcebfd47b21a"

uuid@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/uuid/-/uuid-3.0.1.tgz#6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1"

validate-npm-package-license@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz#2804babe712ad3379459acfbe24746ab2c303fbc"
  dependencies:
    spdx-correct "~1.0.0"
    spdx-expression-parse "~1.0.0"

verror@1.3.6:
  version "1.3.6"
  resolved "https://registry.yarnpkg.com/verror/-/verror-1.3.6.tgz#cff5df12946d297d2baaefaa2689e25be01c005c"
  dependencies:
    extsprintf "1.0.2"

which-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/which-module/-/which-module-1.0.0.tgz#bba63ca861948994ff307736089e3b96026c2a4f"

which@^1.2.4, which@^1.2.9:
  version "1.2.12"
  resolved "https://registry.yarnpkg.com/which/-/which-1.2.12.tgz#de67b5e450269f194909ef23ece4ebe416fa1192"
  dependencies:
    isexe "^1.1.1"

wide-align@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/wide-align/-/wide-align-1.1.0.tgz#40edde802a71fea1f070da3e62dcda2e7add96ad"
  dependencies:
    string-width "^1.0.1"

widest-line@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/widest-line/-/widest-line-1.0.0.tgz#0c09c85c2a94683d0d7eaf8ee097d564bf0e105c"
  dependencies:
    string-width "^1.0.1"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/wrap-ansi/-/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write-file-atomic@^1.1.2, write-file-atomic@^1.1.4:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/write-file-atomic/-/write-file-atomic-1.3.1.tgz#7d45ba32316328dd1ec7d90f60ebc0d845bb759a"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    slide "^1.1.5"

write-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/write-json-file/-/write-json-file-2.0.0.tgz#0eaec981fcf9288dbc2806cbd26e06ab9bdca4ed"
  dependencies:
    graceful-fs "^4.1.2"
    mkdirp "^0.5.1"
    pify "^2.0.0"
    sort-keys "^1.1.1"
    write-file-atomic "^1.1.2"

write-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/write-pkg/-/write-pkg-2.0.0.tgz#93b922ee9a429f9bd74cdc69e549733c9e468156"
  dependencies:
    write-json-file "^2.0.0"

write@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/write/-/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
  dependencies:
    mkdirp "^0.5.1"

xdg-basedir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/xdg-basedir/-/xdg-basedir-2.0.0.tgz#edbc903cc385fc04523d966a335504b5504d1bd2"
  dependencies:
    os-homedir "^1.0.0"

xtend@^4.0.0, xtend@^4.0.1, xtend@~4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

y18n@^3.2.1:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/y18n/-/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"

yallist@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.0.0.tgz#306c543835f09ee1a4cb23b7bce9ab341c91cdd4"

yargs-parser@^4.0.2, yargs-parser@^4.2.0:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/yargs-parser/-/yargs-parser-4.2.1.tgz#29cceac0dc4f03c6c87b4a9f217dd18c9f74871c"
  dependencies:
    camelcase "^3.0.0"

yargs@^6.6.0:
  version "6.6.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-6.6.0.tgz#782ec21ef403345f830a808ca3d513af56065208"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^4.2.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"
