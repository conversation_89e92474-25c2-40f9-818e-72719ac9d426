{"name": "@expo/config", "version": "8.1.2", "description": "A library for interacting with the app.json", "main": "build/index.js", "scripts": {"watch": "tsc --watch --preserveWatchOutput", "build": "tsc --emitDeclarationOnly && babel src --out-dir build --extensions \".ts\" --source-maps --ignore \"src/**/__mocks__/*\",\"src/**/__tests__/*\"", "prepare": "yarn run clean && yarn build", "clean": "rimraf build ./tsconfig.tsbuildinfo", "lint": "eslint .", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/config"}, "keywords": ["json", "react-native", "expo", "react"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/config#readme", "files": ["build", "paths"], "dependencies": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~7.2.0", "@expo/config-types": "^49.0.0-alpha.1", "@expo/json-file": "^8.2.37", "getenv": "^1.0.0", "glob": "7.1.6", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "semver": "7.5.3", "slugify": "^1.3.4", "sucrase": "^3.20.0"}, "devDependencies": {"@types/require-from-string": "^1.2.1"}, "publishConfig": {"access": "public"}, "gitHead": "dce1880a2dc156f551847c78ac8bb4d2420e7ff3"}