{"version": 3, "file": "iconFontSources.js", "sourceRoot": "", "sources": ["../src/iconFontSources.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,uCAAuC;AACvC,qBAAqB;AACrB,EAAE;AACF,oCAAoC;AACpC,QAAQ;AACR,EAAE;AACF,mBAAmB;AACnB,IAAI", "sourcesContent": ["// TODO(brentvatne): implement this plis\n// export default function(fontNames) {\n//   let result = {};\n//\n//   fontNames.forEach(fontName => {\n//   });\n//\n//   return result;\n// }\n"]}