{"version": 3, "file": "createIconSetFromIcoMoon.js", "sourceRoot": "", "sources": ["../src/createIconSetFromIcoMoon.ts"], "names": [], "mappings": "AAAA,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAE5C,MAAM,CAAC,OAAO,WAAW,MAAM,EAAE,YAAY,EAAE,WAAW;IACxD,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACpD,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,UAAU,GACd,YAAY,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;IAElE,OAAO,aAAa,CAClB,QAAQ,EACR,UAAU,EACV,WAAW,IAAI,GAAG,UAAU,MAAM,CACnC,CAAC;AACJ,CAAC", "sourcesContent": ["import createIconSet from \"./createIconSet\";\n\nexport default function (config, expoFontName, expoAssetId) {\n  const glyphMap = {};\n  config.icons.forEach(icon => {\n    icon.properties.name.split(/\\s*,\\s*/g).forEach(name => {\n      glyphMap[name] = icon.properties.code;\n    });\n  });\n\n  const fontFamily =\n    expoFontName || config.preferences.fontPref.metadata.fontFamily;\n\n  return createIconSet<string, string>(\n    glyphMap,\n    fontFamily,\n    expoAssetId || `${fontFamily}.ttf`\n  );\n}\n"]}