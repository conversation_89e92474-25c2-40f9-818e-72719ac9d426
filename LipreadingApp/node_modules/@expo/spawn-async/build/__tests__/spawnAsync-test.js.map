{"version": 3, "file": "spawnAsync-test.js", "sourceRoot": "", "sources": ["../../src/__tests__/spawnAsync-test.ts"], "names": [], "mappings": ";;;;;AAAA,gDAAwB;AAExB,+DAAuC;AAEvC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;IACxD,IAAI,MAAM,GAAG,MAAM,oBAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAM,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAE/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAE7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;IACvE,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI;QACF,MAAM,oBAAU,CAAC,OAAO,CAAC,CAAC;KAC3B;IAAC,OAAO,CAAC,EAAE;QACV,QAAQ,GAAG,IAAI,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IACD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;IACvF,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI;QACF,MAAM,oBAAU,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC;KAC1D;IAAC,OAAO,CAAC,EAAE;QACV,QAAQ,GAAG,IAAI,CAAC;QAChB,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAClC;IACD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;IACxD,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI;QACF,MAAM,oBAAU,CAAC,qBAAqB,CAAC,CAAC;KACzC;IAAC,OAAO,CAAC,EAAE;QACV,QAAQ,GAAG,IAAI,CAAC;QAChB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IACD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;IAC1E,IAAI,SAAS,GAAG,oBAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,IAAI,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;IACnC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;IAEnC,IAAI,MAAM,GAAG,MAAM,SAAS,CAAC;IAC7B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;IAC/D,IAAI,SAAS,GAAG,oBAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3C,IAAI,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IACjC,IAAI,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IAClC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAC7C,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAE/C,MAAM,SAAS,CAAC;IAChB,MAAM,CAAC,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAClD,MAAM,CAAC,iBAAiB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,8EAA8E,EAAE,KAAK,IAAI,EAAE;IAC5F,IAAI,SAAS,GAAG,oBAAU,CAAC,qBAAqB,CAAC,CAAC;IAClD,IAAI,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IAClC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAE/C,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IAC/C,MAAM,CAAC,iBAAiB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;IACzD,IAAI,MAAM,GAAG,MAAM,oBAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,MAAM,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAE/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAE7C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;IAClE,6FAA6F;IAC7F,IAAI,UAAU,GAAG,oBAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAExD,wFAAwF;IACxF,IAAI,QAAQ,GAAG,oBAAU,CAAC,KAAK,CAAC,CAAC;IACjC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACnD,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IAE5B,yDAAyD;IACzD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAErD,gEAAgE;IAChE,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACxB,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IAEhD,0DAA0D;IAC1D,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IAC/B,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;AACvF,CAAC,CAAC,CAAC;AAEH,EAAE,CAAC,qFAAqF,EAAE,KAAK,IAAI,EAAE;IACnG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI;QACF,MAAM,oBAAU,CAAC,OAAO,CAAC,CAAC;KAC3B;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC1C,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;KAClD;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import path from 'path';\n\nimport spawnAsync from '../spawnAsync';\n\nit(`receives output from completed processes`, async () => {\n  let result = await spawnAsync('echo', ['hi']);\n  expect(typeof result.pid).toBe('number');\n  expect(result.stdout).toBe('hi\\n');\n  expect(result.stderr).toBe('');\n\n  expect(result.output[0]).toBe(result.stdout);\n  expect(result.output[1]).toBe(result.stderr);\n\n  expect(result.status).toBe(0);\n  expect(result.signal).toBe(null);\n});\n\nit(`throws errors when processes return non-zero exit codes`, async () => {\n  let didThrow = false;\n  try {\n    await spawnAsync('false');\n  } catch (e) {\n    didThrow = true;\n    expect(typeof e.pid).toBe('number');\n    expect(e.status).toBe(1);\n    expect(e.signal).toBe(null);\n  }\n  expect(didThrow).toBe(true);\n});\n\nit(`returns when processes are killed with signals with non-zero exit codes`, async () => {\n  let didThrow = false;\n  try {\n    await spawnAsync(path.join(__dirname, 'signal-self.sh'));\n  } catch (e) {\n    didThrow = true;\n    expect(typeof e.pid).toBe('number');\n    expect(e.status).toBe(null);\n    expect(e.signal).toBe('SIGKILL');\n  }\n  expect(didThrow).toBe(true);\n});\n\nit(`throws errors when processes don't exist`, async () => {\n  let didThrow = false;\n  try {\n    await spawnAsync('nonexistent-program');\n  } catch (e) {\n    didThrow = true;\n    expect(e.pid).not.toBeDefined();\n    expect(e.code).toBe('ENOENT');\n    expect(e.status).toBe(null);\n    expect(e.signal).toBe(null);\n  }\n  expect(didThrow).toBe(true);\n});\n\nit(`exposes the child process through a property named \"child\"`, async () => {\n  let spawnTask = spawnAsync('echo', ['hi']);\n  let childProcess = spawnTask.child;\n  expect(childProcess).toBeDefined();\n\n  let result = await spawnTask;\n  expect(result.pid).toBe(childProcess.pid);\n});\n\nit(`runs extra listeners added to the child process`, async () => {\n  let spawnTask = spawnAsync('echo', ['hi']);\n  let mockExitListener = jest.fn();\n  let mockCloseListener = jest.fn();\n  spawnTask.child.on('exit', mockExitListener);\n  spawnTask.child.on('close', mockCloseListener);\n\n  await spawnTask;\n  expect(mockExitListener).toHaveBeenCalledTimes(1);\n  expect(mockCloseListener).toHaveBeenCalledTimes(1);\n});\n\nit(`runs extra error listeners added to the child process when there is an error`, async () => {\n  let spawnTask = spawnAsync('nonexistent-program');\n  let mockErrorListener = jest.fn();\n  spawnTask.child.on('error', mockErrorListener);\n\n  await expect(spawnTask).rejects.toThrowError();\n  expect(mockErrorListener).toHaveBeenCalledTimes(1);\n});\n\nit(`returns empty strings when ignoring stdio`, async () => {\n  let result = await spawnAsync('echo', ['hi'], { ignoreStdio: true });\n  expect(typeof result.pid).toBe('number');\n  expect(result.stdout).toBe('');\n  expect(result.stderr).toBe('');\n\n  expect(result.output[0]).toBe(result.stdout);\n  expect(result.output[1]).toBe(result.stderr);\n\n  expect(result.status).toBe(0);\n  expect(result.signal).toBe(null);\n});\n\nit(`returns even if stdout is open when ignoring stdio`, async () => {\n  // Without ignoring stdio, the promise will never resolve as stdout remains open indefinitely\n  let sourceTask = spawnAsync('yes', [], { ignoreStdio: true });\n  expect(sourceTask.child.listenerCount('exit')).toBe(1);\n  expect(sourceTask.child.listenerCount('close')).toBe(0);\n\n  // Create a sink that keeps the source's stdout open even after the source process exits\n  let sinkTask = spawnAsync('cat');\n  sourceTask.child.stdout.pipe(sinkTask.child.stdin);\n  sinkTask.child.stdin.cork();\n\n  // Allow the source's stdout to buffer with a short delay\n  await new Promise(resolve => setTimeout(resolve, 5));\n\n  // The source's stdout stays open even after killing the process\n  sourceTask.child.kill();\n  await expect(sourceTask).rejects.toThrowError();\n\n  // Destroy the sink's stdin stream to let the process exit\n  sinkTask.child.stdin.destroy();\n  await expect(sinkTask).resolves.toMatchObject({ status: 0, stdout: '', stderr: '' });\n});\n\nit('throws errors with preserved stack traces when processes return non-zero exit codes', async () => {\n  expect.assertions(2);\n  try {\n    await spawnAsync('false');\n  } catch (e) {\n    expect(e.stack).toMatch(/\\n    \\.\\.\\.\\n/);\n    expect(e.stack).toMatch(/at Object\\.spawnAsync/);\n  }\n});\n"]}