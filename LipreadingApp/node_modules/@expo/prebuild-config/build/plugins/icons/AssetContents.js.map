{"version": 3, "file": "AssetContents.js", "names": ["_fsExtra", "data", "_interopRequireDefault", "require", "_path", "obj", "__esModule", "default", "createContentsJsonItem", "item", "writeContentsJsonAsync", "directory", "images", "fs", "ensureDir", "writeFile", "join", "JSON", "stringify", "info", "version", "author"], "sources": ["../../../src/plugins/icons/AssetContents.ts"], "sourcesContent": ["import fs from 'fs-extra';\nimport { join } from 'path';\n\nexport type ContentsJsonImageIdiom =\n  | 'iphone'\n  | 'ipad'\n  | 'watchos'\n  | 'ios'\n  | 'ios-marketing'\n  | 'universal';\n\nexport type ContentsJsonImageAppearance = {\n  appearance: 'luminosity';\n  value: 'dark';\n};\n\nexport type ContentsJsonImageScale = '1x' | '2x' | '3x';\n\nexport interface ContentsJsonImage {\n  appearances?: ContentsJsonImageAppearance[];\n  idiom: ContentsJsonImageIdiom;\n  size?: string;\n  scale?: ContentsJsonImageScale;\n  filename?: string;\n  platform?: ContentsJsonImageIdiom;\n}\n\nexport interface ContentsJson {\n  images: ContentsJsonImage[];\n  info: {\n    version: number;\n    author: string;\n  };\n}\n\nexport function createContentsJsonItem(item: ContentsJsonImage): ContentsJsonImage {\n  return item;\n}\n\n/**\n * Writes the Config.json which is used to assign images to their respective platform, dpi, and idiom.\n *\n * @param directory path to add the Contents.json to.\n * @param contents image json data\n */\nexport async function writeContentsJsonAsync(\n  directory: string,\n  { images }: Pick<ContentsJson, 'images'>\n): Promise<void> {\n  await fs.ensureDir(directory);\n\n  await fs.writeFile(\n    join(directory, 'Contents.json'),\n    JSON.stringify(\n      {\n        images,\n        info: {\n          version: 1,\n          // common practice is for the tool that generated the icons to be the \"author\"\n          author: 'expo',\n        },\n      },\n      null,\n      2\n    )\n  );\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAkCrB,SAASG,sBAAsBA,CAACC,IAAuB,EAAqB;EACjF,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeC,sBAAsBA,CAC1CC,SAAiB,EACjB;EAAEC;AAAqC,CAAC,EACzB;EACf,MAAMC,kBAAE,CAACC,SAAS,CAACH,SAAS,CAAC;EAE7B,MAAME,kBAAE,CAACE,SAAS,CAChB,IAAAC,YAAI,EAACL,SAAS,EAAE,eAAe,CAAC,EAChCM,IAAI,CAACC,SAAS,CACZ;IACEN,MAAM;IACNO,IAAI,EAAE;MACJC,OAAO,EAAE,CAAC;MACV;MACAC,MAAM,EAAE;IACV;EACF,CAAC,EACD,IAAI,EACJ,CAAC,CACF,CACF;AACH"}