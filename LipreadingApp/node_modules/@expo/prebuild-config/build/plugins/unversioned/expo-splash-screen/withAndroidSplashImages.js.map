{"version": 3, "file": "withAndroidSplashImages.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fsExtra", "_interopRequireDefault", "_path", "_getAndroidSplashConfig", "obj", "__esModule", "default", "IMAGE_CACHE_NAME", "SPLASH_SCREEN_FILENAME", "DRAWABLES_CONFIGS", "modes", "light", "path", "dark", "dimensionsMultiplier", "mdpi", "hdpi", "xhdpi", "xxhdpi", "xxxhdpi", "withAndroidSplashImages", "config", "withDangerousMod", "setSplashImageDrawablesAsync", "modRequest", "projectRoot", "exports", "clearAllExistingSplashImagesAsync", "splash", "getAndroidSplashConfig", "darkSplash", "getAndroidDarkSplashConfig", "Promise", "all", "setSplashImageDrawablesForThemeAsync", "and<PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "Object", "values", "map", "filePath", "fs", "pathExists", "resolve", "remove", "theme", "image<PERSON>ey", "image", "source", "generateImageAsync", "cacheType", "src", "outputPath", "folder", "dirname", "ensureDir", "writeFile"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashImages.ts"], "sourcesContent": ["import { ConfigPlugin, withDangerousMod } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport { generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs-extra';\nimport path from 'path';\n\nimport {\n  getAndroidDarkSplashConfig,\n  getAndroidSplashConfig,\n  SplashScreenConfig,\n} from './getAndroidSplashConfig';\n\ntype DRAWABLE_SIZE = 'default' | 'mdpi' | 'hdpi' | 'xhdpi' | 'xxhdpi' | 'xxxhdpi';\ntype THEME = 'light' | 'dark';\n\nconst IMAGE_CACHE_NAME = 'splash-android';\nconst SPLASH_SCREEN_FILENAME = 'splashscreen_image.png';\nconst DRAWABLES_CONFIGS: {\n  [key in DRAWABLE_SIZE]: {\n    modes: {\n      [key in THEME]: {\n        path: string;\n      };\n    };\n    dimensionsMultiplier: number;\n  };\n} = {\n  default: {\n    modes: {\n      light: {\n        path: `./res/drawable/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 1,\n  },\n  mdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-mdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-mdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 1,\n  },\n  hdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-hdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-hdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 1.5,\n  },\n  xhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 2,\n  },\n  xxhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 3,\n  },\n  xxxhdpi: {\n    modes: {\n      light: {\n        path: `./res/drawable-xxxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n      dark: {\n        path: `./res/drawable-night-xxxhdpi/${SPLASH_SCREEN_FILENAME}`,\n      },\n    },\n    dimensionsMultiplier: 4,\n  },\n};\n\nexport const withAndroidSplashImages: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await setSplashImageDrawablesAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\n/**\n * Deletes all previous splash_screen_images and copies new one to desired drawable directory.\n * If path isn't provided then no new image is placed in drawable directories.\n * @see https://developer.android.com/training/multiscreen/screendensities\n *\n * @param androidMainPath Absolute path to the main directory containing code and resources in Android project. In general that would be `android/app/src/main`.\n */\nexport async function setSplashImageDrawablesAsync(\n  config: Pick<ExpoConfig, 'android' | 'splash'>,\n  projectRoot: string\n) {\n  await clearAllExistingSplashImagesAsync(projectRoot);\n\n  const splash = getAndroidSplashConfig(config);\n  const darkSplash = getAndroidDarkSplashConfig(config);\n\n  await Promise.all([\n    setSplashImageDrawablesForThemeAsync(splash, 'light', projectRoot),\n    setSplashImageDrawablesForThemeAsync(darkSplash, 'dark', projectRoot),\n  ]);\n}\n\nasync function clearAllExistingSplashImagesAsync(projectRoot: string) {\n  const androidMainPath = path.join(projectRoot, 'android/app/src/main');\n\n  await Promise.all(\n    Object.values(DRAWABLES_CONFIGS).map(async ({ modes }) => {\n      await Promise.all(\n        Object.values(modes).map(async ({ path: filePath }) => {\n          if (await fs.pathExists(path.resolve(androidMainPath, filePath))) {\n            await fs.remove(path.resolve(androidMainPath, filePath));\n          }\n        })\n      );\n    })\n  );\n}\n\nexport async function setSplashImageDrawablesForThemeAsync(\n  config: SplashScreenConfig | null,\n  theme: 'dark' | 'light',\n  projectRoot: string\n) {\n  if (!config) return;\n  const androidMainPath = path.join(projectRoot, 'android/app/src/main');\n\n  await Promise.all(\n    ['mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'].map(async (imageKey) => {\n      // @ts-ignore\n      const image = config[imageKey];\n      if (image) {\n        // Using this method will cache the images in `.expo` based on the properties used to generate them.\n        // this method also supports remote URLs and using the global sharp instance.\n        const { source } = await generateImageAsync({ projectRoot, cacheType: IMAGE_CACHE_NAME }, {\n          src: image,\n        } as any);\n\n        // Get output path for drawable.\n        const outputPath = path.join(\n          androidMainPath,\n          // @ts-ignore\n          DRAWABLES_CONFIGS[imageKey].modes[theme].path\n        );\n        // Ensure directory exists.\n        const folder = path.dirname(outputPath);\n        await fs.ensureDir(folder);\n        // Write image buffer to the file system.\n        await fs.writeFile(outputPath, source);\n      }\n      return null;\n    })\n  );\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,wBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,uBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIkC,SAAAI,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAKlC,MAAMG,gBAAgB,GAAG,gBAAgB;AACzC,MAAMC,sBAAsB,GAAG,wBAAwB;AACvD,MAAMC,iBASL,GAAG;EACFH,OAAO,EAAE;IACPI,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,kBAAiBJ,sBAAuB;MACjD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,wBAAuBJ,sBAAuB;MACvD;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDC,IAAI,EAAE;IACJL,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,uBAAsBJ,sBAAuB;MACtD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,6BAA4BJ,sBAAuB;MAC5D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDE,IAAI,EAAE;IACJN,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,uBAAsBJ,sBAAuB;MACtD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,6BAA4BJ,sBAAuB;MAC5D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDG,KAAK,EAAE;IACLP,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,wBAAuBJ,sBAAuB;MACvD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,8BAA6BJ,sBAAuB;MAC7D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDI,MAAM,EAAE;IACNR,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,yBAAwBJ,sBAAuB;MACxD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,+BAA8BJ,sBAAuB;MAC9D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB,CAAC;EACDK,OAAO,EAAE;IACPT,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAG,0BAAyBJ,sBAAuB;MACzD,CAAC;MACDK,IAAI,EAAE;QACJD,IAAI,EAAG,gCAA+BJ,sBAAuB;MAC/D;IACF,CAAC;IACDM,oBAAoB,EAAE;EACxB;AACF,CAAC;AAEM,MAAMM,uBAAqC,GAAIC,MAAM,IAAK;EAC/D,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAME,4BAA4B,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IACzE,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAK,OAAA,CAAAN,uBAAA,GAAAA,uBAAA;AAOO,eAAeG,4BAA4BA,CAChDF,MAA8C,EAC9CI,WAAmB,EACnB;EACA,MAAME,iCAAiC,CAACF,WAAW,CAAC;EAEpD,MAAMG,MAAM,GAAG,IAAAC,gDAAsB,EAACR,MAAM,CAAC;EAC7C,MAAMS,UAAU,GAAG,IAAAC,oDAA0B,EAACV,MAAM,CAAC;EAErD,MAAMW,OAAO,CAACC,GAAG,CAAC,CAChBC,oCAAoC,CAACN,MAAM,EAAE,OAAO,EAAEH,WAAW,CAAC,EAClES,oCAAoC,CAACJ,UAAU,EAAE,MAAM,EAAEL,WAAW,CAAC,CACtE,CAAC;AACJ;AAEA,eAAeE,iCAAiCA,CAACF,WAAmB,EAAE;EACpE,MAAMU,eAAe,GAAGvB,eAAI,CAACwB,IAAI,CAACX,WAAW,EAAE,sBAAsB,CAAC;EAEtE,MAAMO,OAAO,CAACC,GAAG,CACfI,MAAM,CAACC,MAAM,CAAC7B,iBAAiB,CAAC,CAAC8B,GAAG,CAAC,OAAO;IAAE7B;EAAM,CAAC,KAAK;IACxD,MAAMsB,OAAO,CAACC,GAAG,CACfI,MAAM,CAACC,MAAM,CAAC5B,KAAK,CAAC,CAAC6B,GAAG,CAAC,OAAO;MAAE3B,IAAI,EAAE4B;IAAS,CAAC,KAAK;MACrD,IAAI,MAAMC,kBAAE,CAACC,UAAU,CAAC9B,eAAI,CAAC+B,OAAO,CAACR,eAAe,EAAEK,QAAQ,CAAC,CAAC,EAAE;QAChE,MAAMC,kBAAE,CAACG,MAAM,CAAChC,eAAI,CAAC+B,OAAO,CAACR,eAAe,EAAEK,QAAQ,CAAC,CAAC;MAC1D;IACF,CAAC,CAAC,CACH;EACH,CAAC,CAAC,CACH;AACH;AAEO,eAAeN,oCAAoCA,CACxDb,MAAiC,EACjCwB,KAAuB,EACvBpB,WAAmB,EACnB;EACA,IAAI,CAACJ,MAAM,EAAE;EACb,MAAMc,eAAe,GAAGvB,eAAI,CAACwB,IAAI,CAACX,WAAW,EAAE,sBAAsB,CAAC;EAEtE,MAAMO,OAAO,CAACC,GAAG,CACf,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACM,GAAG,CAAC,MAAOO,QAAQ,IAAK;IACrE;IACA,MAAMC,KAAK,GAAG1B,MAAM,CAACyB,QAAQ,CAAC;IAC9B,IAAIC,KAAK,EAAE;MACT;MACA;MACA,MAAM;QAAEC;MAAO,CAAC,GAAG,MAAM,IAAAC,gCAAkB,EAAC;QAAExB,WAAW;QAAEyB,SAAS,EAAE3C;MAAiB,CAAC,EAAE;QACxF4C,GAAG,EAAEJ;MACP,CAAC,CAAQ;;MAET;MACA,MAAMK,UAAU,GAAGxC,eAAI,CAACwB,IAAI,CAC1BD,eAAe;MACf;MACA1B,iBAAiB,CAACqC,QAAQ,CAAC,CAACpC,KAAK,CAACmC,KAAK,CAAC,CAACjC,IAAI,CAC9C;MACD;MACA,MAAMyC,MAAM,GAAGzC,eAAI,CAAC0C,OAAO,CAACF,UAAU,CAAC;MACvC,MAAMX,kBAAE,CAACc,SAAS,CAACF,MAAM,CAAC;MAC1B;MACA,MAAMZ,kBAAE,CAACe,SAAS,CAACJ,UAAU,EAAEJ,MAAM,CAAC;IACxC;IACA,OAAO,IAAI;EACb,CAAC,CAAC,CACH;AACH"}