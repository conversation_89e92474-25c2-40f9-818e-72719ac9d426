{"version": 3, "file": "withIosSplashScreenStoryboard.js", "names": ["_configPlugins", "data", "require", "fs", "_interopRequireWildcard", "path", "_xml2js", "_InterfaceBuilder", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "STORYBOARD_FILE_PATH", "exports", "STORYBOARD_MOD_NAME", "withIosSplashScreenStoryboard", "config", "action", "with<PERSON><PERSON>", "platform", "mod", "withIosSplashScreenStoryboardBaseMod", "BaseMods", "withGeneratedBaseMods", "saveToInternal", "skipEmptyMod", "providers", "provider", "isIntrospective", "getFilePath", "modRequest", "join", "platformProjectRoot", "projectName", "read", "filePath", "contents", "promises", "readFile", "xml", "<PERSON><PERSON><PERSON>", "parseStringPromise", "getTemplateAsync", "write", "modResults", "introspect", "writeFile", "toString"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashScreenStoryboard.ts"], "sourcesContent": ["import { BaseMods, ConfigPlugin, Mod, withMod } from '@expo/config-plugins';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { Parser } from 'xml2js';\n\nimport { IBSplashScreenDocument, toString } from './InterfaceBuilder';\n\nexport const STORYBOARD_FILE_PATH = './SplashScreen.storyboard';\n\nconst STORYBOARD_MOD_NAME = 'splashScreenStoryboard';\n\n/**\n * Provides the SplashScreen `.storyboard` xml data for modification.\n *\n * @param config\n * @param action\n */\nexport const withIosSplashScreenStoryboard: ConfigPlugin<Mod<IBSplashScreenDocument>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: STORYBOARD_MOD_NAME,\n    action,\n  });\n};\n\n/** Append a custom rule to supply SplashScreen `.storyboard` xml data to mods on `mods.ios.splashScreenStoryboard` */\nexport const withIosSplashScreenStoryboardBaseMod: ConfigPlugin = (config) => {\n  return BaseMods.withGeneratedBaseMods(config, {\n    platform: 'ios',\n    saveToInternal: true,\n    skipEmptyMod: false,\n    providers: {\n      // Append a custom rule to supply .storyboard xml data to mods on `mods.ios.splashScreenStoryboard`\n      [STORYBOARD_MOD_NAME]: BaseMods.provider<IBSplashScreenDocument>({\n        isIntrospective: true,\n        async getFilePath({ modRequest }) {\n          //: [root]/myapp/ios/MyApp/SplashScreen.storyboard\n          return path.join(\n            //: myapp/ios\n            modRequest.platformProjectRoot,\n            // ./MyApp\n            modRequest.projectName!,\n            // ./SplashScreen.storyboard\n            STORYBOARD_FILE_PATH\n          );\n        },\n        async read(filePath) {\n          try {\n            const contents = await fs.promises.readFile(filePath, 'utf8');\n            const xml = await new Parser().parseStringPromise(contents);\n            return xml;\n          } catch {\n            return getTemplateAsync();\n          }\n        },\n        async write(filePath, { modResults, modRequest: { introspect } }) {\n          if (introspect) {\n            return;\n          }\n          await fs.promises.writeFile(filePath, toString(modResults));\n        },\n      }),\n    },\n  });\n};\n\n/** Get a template splash screen storyboard file. */\nexport async function getTemplateAsync(): Promise<IBSplashScreenDocument> {\n  const contents = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n    <document\n      type=\"com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB\"\n      version=\"3.0\"\n      toolsVersion=\"16096\"\n      targetRuntime=\"iOS.CocoaTouch\"\n      propertyAccessControl=\"none\"\n      useAutolayout=\"YES\"\n      launchScreen=\"YES\"\n      useTraitCollections=\"YES\"\n      useSafeAreas=\"YES\"\n      colorMatched=\"YES\"\n      initialViewController=\"EXPO-VIEWCONTROLLER-1\"\n    >\n      <dependencies>\n        <deployment identifier=\"iOS\"/>\n        <plugIn identifier=\"com.apple.InterfaceBuilder.IBCocoaTouchPlugin\" version=\"16087\"/>\n        <capability name=\"Safe area layout guides\" minToolsVersion=\"9.0\"/>\n        <capability name=\"documents saved in the Xcode 8 format\" minToolsVersion=\"8.0\"/>\n      </dependencies>\n      <scenes>\n        <scene sceneID=\"EXPO-SCENE-1\">\n          <objects>\n            <viewController\n              storyboardIdentifier=\"SplashScreenViewController\"\n              id=\"EXPO-VIEWCONTROLLER-1\"\n              sceneMemberID=\"viewController\"\n            >\n              <view\n                key=\"view\"\n                userInteractionEnabled=\"NO\"\n                contentMode=\"scaleToFill\"\n                insetsLayoutMarginsFromSafeArea=\"NO\"\n                id=\"EXPO-ContainerView\"\n                userLabel=\"ContainerView\"\n              >\n                <rect key=\"frame\" x=\"0.0\" y=\"0.0\" width=\"414\" height=\"736\"/>\n                <autoresizingMask key=\"autoresizingMask\" flexibleMaxX=\"YES\" flexibleMaxY=\"YES\"/>\n                <subviews>\n                  <imageView\n                    userInteractionEnabled=\"NO\"\n                    contentMode=\"scaleAspectFill\"\n                    horizontalHuggingPriority=\"251\"\n                    verticalHuggingPriority=\"251\"\n                    insetsLayoutMarginsFromSafeArea=\"NO\"\n                    image=\"SplashScreenBackground\"\n                    translatesAutoresizingMaskIntoConstraints=\"NO\"\n                    id=\"EXPO-SplashScreenBackground\"\n                    userLabel=\"SplashScreenBackground\"\n                  >\n                    <rect key=\"frame\" x=\"0.0\" y=\"0.0\" width=\"414\" height=\"736\"/>\n                  </imageView>\n                </subviews>\n                <color key=\"backgroundColor\" systemColor=\"systemBackgroundColor\"/>\n                <constraints>\n                  <constraint firstItem=\"EXPO-SplashScreenBackground\" firstAttribute=\"top\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"top\" id=\"1gX-mQ-vu6\"/>\n                  <constraint firstItem=\"EXPO-SplashScreenBackground\" firstAttribute=\"leading\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"leading\" id=\"6tX-OG-Sck\"/>\n                  <constraint firstItem=\"EXPO-SplashScreenBackground\" firstAttribute=\"trailing\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"trailing\" id=\"ABX-8g-7v4\"/>\n                  <constraint firstItem=\"EXPO-SplashScreenBackground\" firstAttribute=\"bottom\" secondItem=\"EXPO-ContainerView\" secondAttribute=\"bottom\" id=\"jkI-2V-eW5\"/>\n                </constraints>\n                <viewLayoutGuide key=\"safeArea\" id=\"EXPO-SafeArea\"/>\n              </view>\n            </viewController>\n            <placeholder placeholderIdentifier=\"IBFirstResponder\" id=\"EXPO-PLACEHOLDER-1\" userLabel=\"First Responder\" sceneMemberID=\"firstResponder\"/>\n          </objects>\n        </scene>\n      </scenes>\n      <resources>\n        <image name=\"SplashScreenBackground\" width=\"1\" height=\"1\"/>\n      </resources>\n    </document>`;\n  return await new Parser().parseStringPromise(contents);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,GAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,EAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsE,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAE/D,MAAMW,oBAAoB,GAAG,2BAA2B;AAACC,OAAA,CAAAD,oBAAA,GAAAA,oBAAA;AAEhE,MAAME,mBAAmB,GAAG,wBAAwB;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,6BAAwE,GAAGA,CACtFC,MAAM,EACNC,MAAM,KACH;EACH,OAAO,IAAAC,wBAAO,EAACF,MAAM,EAAE;IACrBG,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAEN,mBAAmB;IACxBG;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAJ,OAAA,CAAAE,6BAAA,GAAAA,6BAAA;AACO,MAAMM,oCAAkD,GAAIL,MAAM,IAAK;EAC5E,OAAOM,yBAAQ,CAACC,qBAAqB,CAACP,MAAM,EAAE;IAC5CG,QAAQ,EAAE,KAAK;IACfK,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE;MACT;MACA,CAACZ,mBAAmB,GAAGQ,yBAAQ,CAACK,QAAQ,CAAyB;QAC/DC,eAAe,EAAE,IAAI;QACrB,MAAMC,WAAWA,CAAC;UAAEC;QAAW,CAAC,EAAE;UAChC;UACA,OAAO3C,IAAI,GAAC4C,IAAI;UACd;UACAD,UAAU,CAACE,mBAAmB;UAC9B;UACAF,UAAU,CAACG,WAAW;UACtB;UACArB,oBAAoB,CACrB;QACH,CAAC;QACD,MAAMsB,IAAIA,CAACC,QAAQ,EAAE;UACnB,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMnD,EAAE,GAACoD,QAAQ,CAACC,QAAQ,CAACH,QAAQ,EAAE,MAAM,CAAC;YAC7D,MAAMI,GAAG,GAAG,MAAM,KAAIC,gBAAM,GAAE,CAACC,kBAAkB,CAACL,QAAQ,CAAC;YAC3D,OAAOG,GAAG;UACZ,CAAC,CAAC,MAAM;YACN,OAAOG,gBAAgB,EAAE;UAC3B;QACF,CAAC;QACD,MAAMC,KAAKA,CAACR,QAAQ,EAAE;UAAES,UAAU;UAAEd,UAAU,EAAE;YAAEe;UAAW;QAAE,CAAC,EAAE;UAChE,IAAIA,UAAU,EAAE;YACd;UACF;UACA,MAAM5D,EAAE,GAACoD,QAAQ,CAACS,SAAS,CAACX,QAAQ,EAAE,IAAAY,4BAAQ,EAACH,UAAU,CAAC,CAAC;QAC7D;MACF,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA/B,OAAA,CAAAQ,oCAAA,GAAAA,oCAAA;AACO,eAAeqB,gBAAgBA,CAAA,EAAoC;EACxE,MAAMN,QAAQ,GAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;EACd,OAAO,MAAM,KAAII,gBAAM,GAAE,CAACC,kBAAkB,CAACL,QAAQ,CAAC;AACxD"}