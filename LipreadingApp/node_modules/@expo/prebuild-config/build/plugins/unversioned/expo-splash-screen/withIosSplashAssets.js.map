{"version": 3, "file": "withIosSplashAssets.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_debug", "_interopRequireDefault", "_fsExtra", "_jimpCompact", "path", "_interopRequireWildcard", "_AssetContents", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "debug", "Debug", "IMAGE_CACHE_NAME", "IMAGESET_PATH", "BACKGROUND_IMAGESET_PATH", "PNG_FILENAME", "DARK_PNG_FILENAME", "TABLET_PNG_FILENAME", "DARK_TABLET_PNG_FILENAME", "withIosSplashAssets", "config", "splash", "withDangerousMod", "_splash$dark", "_splash$dark2", "iosNamedProjectRoot", "IOSConfig", "Paths", "getSourceRoot", "modRequest", "projectRoot", "createSplashScreenBackgroundImageAsync", "configureImageAssets", "image", "darkImage", "dark", "tabletImage", "darkTabletImage", "exports", "imageSetPath", "resolve", "fs", "remove", "writeContentsJsonFileAsync", "assetPath", "copyImageFiles", "createPngFileAsync", "color", "filePath", "png", "<PERSON><PERSON>", "writeAsync", "createBackgroundImagesAsync", "darkColor", "tabletColor", "darkTabletColor", "generateImagesAssetsAsync", "generateImageAsset", "item", "fileName", "anyItem", "darkItem", "tabletItem", "darkTabletItem", "source", "generateImageAsync", "cacheType", "src", "writeFile", "items", "filter", "Promise", "all", "map", "_splash$dark3", "_splash$dark4", "backgroundColor", "tabletBackgroundColor", "imagesetPath", "join", "ensureDir", "dark<PERSON><PERSON><PERSON>ces", "appearance", "value", "buildContentsJsonImages", "createContentsJsonItem", "idiom", "filename", "scale", "appearances", "undefined", "Boolean", "images", "writeContentsJsonAsync"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashAssets.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, withDangerousMod } from '@expo/config-plugins';\nimport { generateImageAsync } from '@expo/image-utils';\nimport Debug from 'debug';\nimport fs from 'fs-extra';\n// @ts-ignore\nimport Jim<PERSON> from 'jimp-compact';\nimport * as path from 'path';\n\nimport {\n  ContentsJsonImage,\n  ContentsJsonImageAppearance,\n  createContentsJsonItem,\n  writeContentsJsonAsync,\n} from '../../icons/AssetContents';\nimport { IOSSplashConfig } from './getIosSplashConfig';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios:assets');\n\nconst IMAGE_CACHE_NAME = 'splash-ios';\nconst IMAGESET_PATH = 'Images.xcassets/SplashScreen.imageset';\nconst BACKGROUND_IMAGESET_PATH = 'Images.xcassets/SplashScreenBackground.imageset';\nconst PNG_FILENAME = 'image.png';\nconst DARK_PNG_FILENAME = 'dark_image.png';\nconst TABLET_PNG_FILENAME = 'tablet_image.png';\nconst DARK_TABLET_PNG_FILENAME = 'dark_tablet_image.png';\n\nexport const withIosSplashAssets: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  if (!splash) {\n    return config;\n  }\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      const iosNamedProjectRoot = IOSConfig.Paths.getSourceRoot(config.modRequest.projectRoot);\n\n      await createSplashScreenBackgroundImageAsync({\n        iosNamedProjectRoot,\n        splash,\n      });\n\n      await configureImageAssets({\n        projectRoot: config.modRequest.projectRoot,\n        iosNamedProjectRoot,\n        image: splash.image,\n        darkImage: splash.dark?.image,\n        tabletImage: splash.tabletImage,\n        darkTabletImage: splash.dark?.tabletImage,\n      });\n\n      return config;\n    },\n  ]);\n};\n\n/**\n * Creates imageset containing image for Splash/Launch Screen.\n */\nasync function configureImageAssets({\n  projectRoot,\n  iosNamedProjectRoot,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  projectRoot: string;\n  iosNamedProjectRoot: string;\n  image?: string | null;\n  darkImage?: string | null;\n  tabletImage: string | null;\n  darkTabletImage?: string | null;\n}) {\n  const imageSetPath = path.resolve(iosNamedProjectRoot, IMAGESET_PATH);\n\n  // ensure old SplashScreen imageSet is removed\n  await fs.remove(imageSetPath);\n\n  if (!image) {\n    return;\n  }\n\n  await writeContentsJsonFileAsync({\n    assetPath: imageSetPath,\n    image: PNG_FILENAME,\n    darkImage: darkImage ? DARK_PNG_FILENAME : null,\n    tabletImage: tabletImage ? TABLET_PNG_FILENAME : null,\n    darkTabletImage: darkTabletImage ? DARK_TABLET_PNG_FILENAME : null,\n  });\n\n  await copyImageFiles({\n    projectRoot,\n    iosNamedProjectRoot,\n    image,\n    darkImage,\n    tabletImage,\n    darkTabletImage,\n  });\n}\n\nasync function createPngFileAsync(color: string, filePath: string) {\n  const png = new Jimp(1, 1, color);\n  return png.writeAsync(filePath);\n}\n\nasync function createBackgroundImagesAsync({\n  iosNamedProjectRoot,\n  color,\n  darkColor,\n  tabletColor,\n  darkTabletColor,\n}: {\n  iosNamedProjectRoot: string;\n  color: string;\n  darkColor: string | null;\n  tabletColor: string | null;\n  darkTabletColor: string | null;\n}) {\n  await generateImagesAssetsAsync({\n    async generateImageAsset(item, fileName) {\n      await createPngFileAsync(\n        item,\n        path.resolve(iosNamedProjectRoot, BACKGROUND_IMAGESET_PATH, fileName)\n      );\n    },\n    anyItem: color,\n    darkItem: darkColor,\n    tabletItem: tabletColor,\n    darkTabletItem: darkTabletColor,\n  });\n}\n\nasync function copyImageFiles({\n  projectRoot,\n  iosNamedProjectRoot,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  projectRoot: string;\n  iosNamedProjectRoot: string;\n  image: string;\n  darkImage?: string | null;\n  tabletImage?: string | null;\n  darkTabletImage?: string | null;\n}) {\n  await generateImagesAssetsAsync({\n    async generateImageAsset(item, fileName) {\n      // Using this method will cache the images in `.expo` based on the properties used to generate them.\n      // this method also supports remote URLs and using the global sharp instance.\n      const { source } = await generateImageAsync({ projectRoot, cacheType: IMAGE_CACHE_NAME }, {\n        src: item,\n      } as any);\n      // Write image buffer to the file system.\n      // const assetPath = join(iosNamedProjectRoot, IMAGESET_PATH, filename);\n      await fs.writeFile(path.resolve(iosNamedProjectRoot, IMAGESET_PATH, fileName), source);\n    },\n    anyItem: image,\n    darkItem: darkImage,\n    tabletItem: tabletImage,\n    darkTabletItem: darkTabletImage,\n  });\n}\n\nasync function generateImagesAssetsAsync({\n  generateImageAsset,\n  anyItem,\n  darkItem,\n  tabletItem,\n  darkTabletItem,\n}: {\n  generateImageAsset: (item: string, fileName: string) => Promise<void>;\n  anyItem: string;\n  darkItem?: string | null;\n  tabletItem?: string | null;\n  darkTabletItem?: string | null;\n}) {\n  const items = [\n    [anyItem, PNG_FILENAME],\n    [darkItem, DARK_PNG_FILENAME],\n    [tabletItem, TABLET_PNG_FILENAME],\n    [darkTabletItem, DARK_TABLET_PNG_FILENAME],\n  ].filter(([item]) => !!item) as unknown as [string, string];\n\n  await Promise.all(items.map(([item, fileName]) => generateImageAsset(item, fileName)));\n}\n\nasync function createSplashScreenBackgroundImageAsync({\n  iosNamedProjectRoot,\n  splash,\n}: {\n  // Something like projectRoot/ios/MyApp/\n  iosNamedProjectRoot: string;\n  splash: IOSSplashConfig;\n}) {\n  const color = splash.backgroundColor;\n  const darkColor = splash.dark?.backgroundColor;\n  const tabletColor = splash.tabletBackgroundColor;\n  const darkTabletColor = splash.dark?.tabletBackgroundColor;\n\n  const imagesetPath = path.join(iosNamedProjectRoot, BACKGROUND_IMAGESET_PATH);\n  // Ensure the Images.xcassets/... path exists\n  await fs.remove(imagesetPath);\n  await fs.ensureDir(imagesetPath);\n\n  await createBackgroundImagesAsync({\n    iosNamedProjectRoot,\n    color,\n    darkColor: darkColor ? darkColor : null,\n    tabletColor: tabletColor ? tabletColor : null,\n    darkTabletColor: darkTabletColor ? darkTabletColor : null,\n  });\n\n  await writeContentsJsonFileAsync({\n    assetPath: path.resolve(iosNamedProjectRoot, BACKGROUND_IMAGESET_PATH),\n    image: PNG_FILENAME,\n    darkImage: darkColor ? DARK_PNG_FILENAME : null,\n    tabletImage: tabletColor ? TABLET_PNG_FILENAME : null,\n    darkTabletImage: darkTabletColor ? DARK_TABLET_PNG_FILENAME : null,\n  });\n}\n\nconst darkAppearances: ContentsJsonImageAppearance[] = [\n  {\n    appearance: 'luminosity',\n    value: 'dark',\n  } as ContentsJsonImageAppearance,\n];\n\nexport function buildContentsJsonImages({\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  image: string;\n  tabletImage: string | null;\n  darkImage: string | null;\n  darkTabletImage: string | null;\n}): ContentsJsonImage[] {\n  return [\n    // Phone light\n    createContentsJsonItem({\n      idiom: 'universal',\n      filename: image,\n      scale: '1x',\n    }),\n    createContentsJsonItem({\n      idiom: 'universal',\n      scale: '2x',\n    }),\n    createContentsJsonItem({\n      idiom: 'universal',\n      scale: '3x',\n    }),\n    // Phone dark\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        filename: darkImage,\n        scale: '1x',\n      }),\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        scale: '2x',\n      }),\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        scale: '3x',\n      }),\n    // Tablet light\n    tabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        filename: tabletImage,\n        scale: '1x',\n      }),\n    tabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        scale: '2x',\n      }),\n    // Phone dark\n    darkTabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        appearances: darkAppearances,\n        filename: darkTabletImage ?? undefined,\n        scale: '1x',\n      }),\n    darkTabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        appearances: darkAppearances,\n        scale: '2x',\n      }),\n  ].filter(Boolean) as ContentsJsonImage[];\n}\n\nasync function writeContentsJsonFileAsync({\n  assetPath,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  assetPath: string;\n  image: string;\n  darkImage: string | null;\n  tabletImage: string | null;\n  darkTabletImage: string | null;\n}) {\n  const images = buildContentsJsonImages({ image, darkImage, tabletImage, darkTabletImage });\n\n  debug(`create contents.json:`, assetPath);\n  debug(`use images:`, images);\n  await writeContentsJsonAsync(assetPath, { images });\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,SAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,KAAA;EAAA,MAAAP,IAAA,GAAAQ,uBAAA,CAAAP,OAAA;EAAAM,IAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAS,eAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,cAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKmC,SAAAU,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAjB,uBAAAW,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AATnC;;AAYA,MAAMiB,KAAK,GAAG,IAAAC,gBAAK,EAAC,oDAAoD,CAAC;AAEzE,MAAMC,gBAAgB,GAAG,YAAY;AACrC,MAAMC,aAAa,GAAG,uCAAuC;AAC7D,MAAMC,wBAAwB,GAAG,iDAAiD;AAClF,MAAMC,YAAY,GAAG,WAAW;AAChC,MAAMC,iBAAiB,GAAG,gBAAgB;AAC1C,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,MAAMC,wBAAwB,GAAG,uBAAuB;AAEjD,MAAMC,mBAAkD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACpF,IAAI,CAACA,MAAM,EAAE;IACX,OAAOD,MAAM;EACf;EACA,OAAO,IAAAE,iCAAgB,EAACF,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAAA,IAAAG,YAAA,EAAAC,aAAA;IAChB,MAAMC,mBAAmB,GAAGC,0BAAS,CAACC,KAAK,CAACC,aAAa,CAACR,MAAM,CAACS,UAAU,CAACC,WAAW,CAAC;IAExF,MAAMC,sCAAsC,CAAC;MAC3CN,mBAAmB;MACnBJ;IACF,CAAC,CAAC;IAEF,MAAMW,oBAAoB,CAAC;MACzBF,WAAW,EAAEV,MAAM,CAACS,UAAU,CAACC,WAAW;MAC1CL,mBAAmB;MACnBQ,KAAK,EAAEZ,MAAM,CAACY,KAAK;MACnBC,SAAS,GAAAX,YAAA,GAAEF,MAAM,CAACc,IAAI,cAAAZ,YAAA,uBAAXA,YAAA,CAAaU,KAAK;MAC7BG,WAAW,EAAEf,MAAM,CAACe,WAAW;MAC/BC,eAAe,GAAAb,aAAA,GAAEH,MAAM,CAACc,IAAI,cAAAX,aAAA,uBAAXA,aAAA,CAAaY;IAChC,CAAC,CAAC;IAEF,OAAOhB,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAkB,OAAA,CAAAnB,mBAAA,GAAAA,mBAAA;AAGA,eAAea,oBAAoBA,CAAC;EAClCF,WAAW;EACXL,mBAAmB;EACnBQ,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAQF,CAAC,EAAE;EACD,MAAME,YAAY,GAAGtD,IAAI,GAACuD,OAAO,CAACf,mBAAmB,EAAEZ,aAAa,CAAC;;EAErE;EACA,MAAM4B,kBAAE,CAACC,MAAM,CAACH,YAAY,CAAC;EAE7B,IAAI,CAACN,KAAK,EAAE;IACV;EACF;EAEA,MAAMU,0BAA0B,CAAC;IAC/BC,SAAS,EAAEL,YAAY;IACvBN,KAAK,EAAElB,YAAY;IACnBmB,SAAS,EAAEA,SAAS,GAAGlB,iBAAiB,GAAG,IAAI;IAC/CoB,WAAW,EAAEA,WAAW,GAAGnB,mBAAmB,GAAG,IAAI;IACrDoB,eAAe,EAAEA,eAAe,GAAGnB,wBAAwB,GAAG;EAChE,CAAC,CAAC;EAEF,MAAM2B,cAAc,CAAC;IACnBf,WAAW;IACXL,mBAAmB;IACnBQ,KAAK;IACLC,SAAS;IACTE,WAAW;IACXC;EACF,CAAC,CAAC;AACJ;AAEA,eAAeS,kBAAkBA,CAACC,KAAa,EAAEC,QAAgB,EAAE;EACjE,MAAMC,GAAG,GAAG,KAAIC,sBAAI,EAAC,CAAC,EAAE,CAAC,EAAEH,KAAK,CAAC;EACjC,OAAOE,GAAG,CAACE,UAAU,CAACH,QAAQ,CAAC;AACjC;AAEA,eAAeI,2BAA2BA,CAAC;EACzC3B,mBAAmB;EACnBsB,KAAK;EACLM,SAAS;EACTC,WAAW;EACXC;AAOF,CAAC,EAAE;EACD,MAAMC,yBAAyB,CAAC;IAC9B,MAAMC,kBAAkBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MACvC,MAAMb,kBAAkB,CACtBY,IAAI,EACJzE,IAAI,GAACuD,OAAO,CAACf,mBAAmB,EAAEX,wBAAwB,EAAE6C,QAAQ,CAAC,CACtE;IACH,CAAC;IACDC,OAAO,EAAEb,KAAK;IACdc,QAAQ,EAAER,SAAS;IACnBS,UAAU,EAAER,WAAW;IACvBS,cAAc,EAAER;EAClB,CAAC,CAAC;AACJ;AAEA,eAAeV,cAAcA,CAAC;EAC5Bf,WAAW;EACXL,mBAAmB;EACnBQ,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAQF,CAAC,EAAE;EACD,MAAMmB,yBAAyB,CAAC;IAC9B,MAAMC,kBAAkBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MACvC;MACA;MACA,MAAM;QAAEK;MAAO,CAAC,GAAG,MAAM,IAAAC,gCAAkB,EAAC;QAAEnC,WAAW;QAAEoC,SAAS,EAAEtD;MAAiB,CAAC,EAAE;QACxFuD,GAAG,EAAET;MACP,CAAC,CAAQ;MACT;MACA;MACA,MAAMjB,kBAAE,CAAC2B,SAAS,CAACnF,IAAI,GAACuD,OAAO,CAACf,mBAAmB,EAAEZ,aAAa,EAAE8C,QAAQ,CAAC,EAAEK,MAAM,CAAC;IACxF,CAAC;IACDJ,OAAO,EAAE3B,KAAK;IACd4B,QAAQ,EAAE3B,SAAS;IACnB4B,UAAU,EAAE1B,WAAW;IACvB2B,cAAc,EAAE1B;EAClB,CAAC,CAAC;AACJ;AAEA,eAAemB,yBAAyBA,CAAC;EACvCC,kBAAkB;EAClBG,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC;AAOF,CAAC,EAAE;EACD,MAAMM,KAAK,GAAG,CACZ,CAACT,OAAO,EAAE7C,YAAY,CAAC,EACvB,CAAC8C,QAAQ,EAAE7C,iBAAiB,CAAC,EAC7B,CAAC8C,UAAU,EAAE7C,mBAAmB,CAAC,EACjC,CAAC8C,cAAc,EAAE7C,wBAAwB,CAAC,CAC3C,CAACoD,MAAM,CAAC,CAAC,CAACZ,IAAI,CAAC,KAAK,CAAC,CAACA,IAAI,CAAgC;EAE3D,MAAMa,OAAO,CAACC,GAAG,CAACH,KAAK,CAACI,GAAG,CAAC,CAAC,CAACf,IAAI,EAAEC,QAAQ,CAAC,KAAKF,kBAAkB,CAACC,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAAC;AACxF;AAEA,eAAe5B,sCAAsCA,CAAC;EACpDN,mBAAmB;EACnBJ;AAKF,CAAC,EAAE;EAAA,IAAAqD,aAAA,EAAAC,aAAA;EACD,MAAM5B,KAAK,GAAG1B,MAAM,CAACuD,eAAe;EACpC,MAAMvB,SAAS,IAAAqB,aAAA,GAAGrD,MAAM,CAACc,IAAI,cAAAuC,aAAA,uBAAXA,aAAA,CAAaE,eAAe;EAC9C,MAAMtB,WAAW,GAAGjC,MAAM,CAACwD,qBAAqB;EAChD,MAAMtB,eAAe,IAAAoB,aAAA,GAAGtD,MAAM,CAACc,IAAI,cAAAwC,aAAA,uBAAXA,aAAA,CAAaE,qBAAqB;EAE1D,MAAMC,YAAY,GAAG7F,IAAI,GAAC8F,IAAI,CAACtD,mBAAmB,EAAEX,wBAAwB,CAAC;EAC7E;EACA,MAAM2B,kBAAE,CAACC,MAAM,CAACoC,YAAY,CAAC;EAC7B,MAAMrC,kBAAE,CAACuC,SAAS,CAACF,YAAY,CAAC;EAEhC,MAAM1B,2BAA2B,CAAC;IAChC3B,mBAAmB;IACnBsB,KAAK;IACLM,SAAS,EAAEA,SAAS,GAAGA,SAAS,GAAG,IAAI;IACvCC,WAAW,EAAEA,WAAW,GAAGA,WAAW,GAAG,IAAI;IAC7CC,eAAe,EAAEA,eAAe,GAAGA,eAAe,GAAG;EACvD,CAAC,CAAC;EAEF,MAAMZ,0BAA0B,CAAC;IAC/BC,SAAS,EAAE3D,IAAI,GAACuD,OAAO,CAACf,mBAAmB,EAAEX,wBAAwB,CAAC;IACtEmB,KAAK,EAAElB,YAAY;IACnBmB,SAAS,EAAEmB,SAAS,GAAGrC,iBAAiB,GAAG,IAAI;IAC/CoB,WAAW,EAAEkB,WAAW,GAAGrC,mBAAmB,GAAG,IAAI;IACrDoB,eAAe,EAAEkB,eAAe,GAAGrC,wBAAwB,GAAG;EAChE,CAAC,CAAC;AACJ;AAEA,MAAM+D,eAA8C,GAAG,CACrD;EACEC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,CACF;AAEM,SAASC,uBAAuBA,CAAC;EACtCnD,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAMF,CAAC,EAAuB;EACtB,OAAO;EACL;EACA,IAAAgD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAEtD,KAAK;IACfuD,KAAK,EAAE;EACT,CAAC,CAAC,EACF,IAAAH,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBE,KAAK,EAAE;EACT,CAAC,CAAC,EACF,IAAAH,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBE,KAAK,EAAE;EACT,CAAC,CAAC;EACF;EACAtD,SAAS,IACP,IAAAmD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BM,QAAQ,EAAErD,SAAS;IACnBsD,KAAK,EAAE;EACT,CAAC,CAAC,EACJtD,SAAS,IACP,IAAAmD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE;EACT,CAAC,CAAC,EACJtD,SAAS,IACP,IAAAmD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE;EACT,CAAC,CAAC;EACJ;EACApD,WAAW,IACT,IAAAiD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAEnD,WAAW;IACrBoD,KAAK,EAAE;EACT,CAAC,CAAC,EACJpD,WAAW,IACT,IAAAiD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbE,KAAK,EAAE;EACT,CAAC,CAAC;EACJ;EACAnD,eAAe,IACb,IAAAgD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbG,WAAW,EAAER,eAAe;IAC5BM,QAAQ,EAAElD,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAIqD,SAAS;IACtCF,KAAK,EAAE;EACT,CAAC,CAAC,EACJnD,eAAe,IACb,IAAAgD,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE;EACT,CAAC,CAAC,CACL,CAAClB,MAAM,CAACqB,OAAO,CAAC;AACnB;AAEA,eAAehD,0BAA0BA,CAAC;EACxCC,SAAS;EACTX,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAOF,CAAC,EAAE;EACD,MAAMuD,MAAM,GAAGR,uBAAuB,CAAC;IAAEnD,KAAK;IAAEC,SAAS;IAAEE,WAAW;IAAEC;EAAgB,CAAC,CAAC;EAE1F3B,KAAK,CAAE,uBAAsB,EAAEkC,SAAS,CAAC;EACzClC,KAAK,CAAE,aAAY,EAAEkF,MAAM,CAAC;EAC5B,MAAM,IAAAC,uCAAsB,EAACjD,SAAS,EAAE;IAAEgD;EAAO,CAAC,CAAC;AACrD"}