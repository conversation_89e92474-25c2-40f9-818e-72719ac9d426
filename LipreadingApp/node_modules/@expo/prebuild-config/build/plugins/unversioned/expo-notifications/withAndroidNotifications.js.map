{"version": 3, "file": "withAndroidNotifications.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_fsExtra", "_interopRequireDefault", "_path", "_withAndroidIcons", "obj", "__esModule", "default", "Colors", "AndroidConfig", "addMetaDataItemToMainApplication", "getMainApplicationOrThrow", "removeMetaDataItemFromMainApplication", "Manifest", "BASELINE_PIXEL_SIZE", "META_DATA_NOTIFICATION_ICON", "exports", "META_DATA_NOTIFICATION_ICON_COLOR", "NOTIFICATION_ICON", "NOTIFICATION_ICON_RESOURCE", "NOTIFICATION_ICON_COLOR", "NOTIFICATION_ICON_COLOR_RESOURCE", "withNotificationIcons", "config", "withDangerousMod", "setNotificationIconAsync", "modRequest", "projectRoot", "withNotificationIconColor", "withAndroidColors", "modResults", "setNotificationIconColor", "withNotificationManifest", "withAndroidManifest", "setNotificationConfig", "getNotificationIcon", "_config$notification", "notification", "icon", "getNotificationColor", "_config$notification2", "color", "writeNotificationIconImageFilesAsync", "removeNotificationIconImageFilesAsync", "manifest", "mainApplication", "colors", "assignColorValue", "name", "value", "Promise", "all", "Object", "values", "dpi<PERSON><PERSON><PERSON>", "map", "folderName", "scale", "drawableFolderName", "replace", "dpiFolderPath", "path", "resolve", "ANDROID_RES_PATH", "fs", "ensureDir", "iconSizePx", "resizedIcon", "generateImageAsync", "cacheType", "src", "width", "height", "resizeMode", "backgroundColor", "source", "writeFile", "e", "Error", "remove"], "sources": ["../../../../src/plugins/unversioned/expo-notifications/withAndroidNotifications.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  withAndroidColors,\n  withAndroidManifest,\n  withDangerousMod,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport { generateImageAsync } from '@expo/image-utils';\nimport fs from 'fs-extra';\nimport path from 'path';\n\nimport { ANDROID_RES_PATH, dpiValues } from '../../icons/withAndroidIcons';\n\nconst { Colors } = AndroidConfig;\nconst {\n  addMetaDataItemToMainApplication,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} = AndroidConfig.Manifest;\n\ntype AndroidManifest = AndroidConfig.Manifest.AndroidManifest;\nconst BASELINE_PIXEL_SIZE = 24;\nexport const META_DATA_NOTIFICATION_ICON = 'expo.modules.notifications.default_notification_icon';\nexport const META_DATA_NOTIFICATION_ICON_COLOR =\n  'expo.modules.notifications.default_notification_color';\nexport const NOTIFICATION_ICON = 'notification_icon';\nexport const NOTIFICATION_ICON_RESOURCE = `@drawable/${NOTIFICATION_ICON}`;\nexport const NOTIFICATION_ICON_COLOR = 'notification_icon_color';\nexport const NOTIFICATION_ICON_COLOR_RESOURCE = `@color/${NOTIFICATION_ICON_COLOR}`;\n\nexport const withNotificationIcons: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await setNotificationIconAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport const withNotificationIconColor: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setNotificationIconColor(config, config.modResults);\n    return config;\n  });\n};\n\nexport const withNotificationManifest: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, (config) => {\n    config.modResults = setNotificationConfig(config, config.modResults);\n    return config;\n  });\n};\n\nexport function getNotificationIcon(config: ExpoConfig) {\n  return config.notification?.icon || null;\n}\n\nexport function getNotificationColor(config: ExpoConfig) {\n  return config.notification?.color || null;\n}\n\n/**\n * Applies configuration for expo-notifications, including\n * the notification icon and notification color.\n */\nexport async function setNotificationIconAsync(config: ExpoConfig, projectRoot: string) {\n  const icon = getNotificationIcon(config);\n  if (icon) {\n    await writeNotificationIconImageFilesAsync(icon, projectRoot);\n  } else {\n    await removeNotificationIconImageFilesAsync(projectRoot);\n  }\n}\n\nexport function setNotificationConfig(config: ExpoConfig, manifest: AndroidManifest) {\n  const icon = getNotificationIcon(config);\n  const color = getNotificationColor(config);\n  const mainApplication = getMainApplicationOrThrow(manifest);\n  if (icon) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DATA_NOTIFICATION_ICON,\n      NOTIFICATION_ICON_RESOURCE,\n      'resource'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_DATA_NOTIFICATION_ICON);\n  }\n  if (color) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DATA_NOTIFICATION_ICON_COLOR,\n      NOTIFICATION_ICON_COLOR_RESOURCE,\n      'resource'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_DATA_NOTIFICATION_ICON_COLOR);\n  }\n  return manifest;\n}\n\nexport function setNotificationIconColor(\n  config: ExpoConfig,\n  colors: AndroidConfig.Resources.ResourceXML\n) {\n  return Colors.assignColorValue(colors, {\n    name: NOTIFICATION_ICON_COLOR,\n    value: getNotificationColor(config),\n  });\n}\n\nasync function writeNotificationIconImageFilesAsync(icon: string, projectRoot: string) {\n  await Promise.all(\n    Object.values(dpiValues).map(async ({ folderName, scale }) => {\n      const drawableFolderName = folderName.replace('mipmap', 'drawable');\n      const dpiFolderPath = path.resolve(projectRoot, ANDROID_RES_PATH, drawableFolderName);\n      await fs.ensureDir(dpiFolderPath);\n      const iconSizePx = BASELINE_PIXEL_SIZE * scale;\n\n      try {\n        const resizedIcon = (\n          await generateImageAsync(\n            { projectRoot, cacheType: 'android-notification' },\n            {\n              src: icon,\n              width: iconSizePx,\n              height: iconSizePx,\n              resizeMode: 'cover',\n              backgroundColor: 'transparent',\n            }\n          )\n        ).source;\n        await fs.writeFile(path.resolve(dpiFolderPath, NOTIFICATION_ICON + '.png'), resizedIcon);\n      } catch (e) {\n        throw new Error('Encountered an issue resizing Android notification icon: ' + e);\n      }\n    })\n  );\n}\n\nasync function removeNotificationIconImageFilesAsync(projectRoot: string) {\n  await Promise.all(\n    Object.values(dpiValues).map(async ({ folderName }) => {\n      const drawableFolderName = folderName.replace('mipmap', 'drawable');\n      const dpiFolderPath = path.resolve(projectRoot, ANDROID_RES_PATH, drawableFolderName);\n      await fs.remove(path.resolve(dpiFolderPath, NOTIFICATION_ICON + '.png'));\n    })\n  );\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2E,SAAAI,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3E,MAAM;EAAEG;AAAO,CAAC,GAAGC,8BAAa;AAChC,MAAM;EACJC,gCAAgC;EAChCC,yBAAyB;EACzBC;AACF,CAAC,GAAGH,8BAAa,CAACI,QAAQ;AAG1B,MAAMC,mBAAmB,GAAG,EAAE;AACvB,MAAMC,2BAA2B,GAAG,sDAAsD;AAACC,OAAA,CAAAD,2BAAA,GAAAA,2BAAA;AAC3F,MAAME,iCAAiC,GAC5C,uDAAuD;AAACD,OAAA,CAAAC,iCAAA,GAAAA,iCAAA;AACnD,MAAMC,iBAAiB,GAAG,mBAAmB;AAACF,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AAC9C,MAAMC,0BAA0B,GAAI,aAAYD,iBAAkB,EAAC;AAACF,OAAA,CAAAG,0BAAA,GAAAA,0BAAA;AACpE,MAAMC,uBAAuB,GAAG,yBAAyB;AAACJ,OAAA,CAAAI,uBAAA,GAAAA,uBAAA;AAC1D,MAAMC,gCAAgC,GAAI,UAASD,uBAAwB,EAAC;AAACJ,OAAA,CAAAK,gCAAA,GAAAA,gCAAA;AAE7E,MAAMC,qBAAmC,GAAIC,MAAM,IAAK;EAC7D,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAME,wBAAwB,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IACrE,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAM,qBAAA,GAAAA,qBAAA;AAEK,MAAMM,yBAAuC,GAAIL,MAAM,IAAK;EACjE,OAAO,IAAAM,kCAAiB,EAACN,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACO,UAAU,GAAGC,wBAAwB,CAACR,MAAM,EAAEA,MAAM,CAACO,UAAU,CAAC;IACvE,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAY,yBAAA,GAAAA,yBAAA;AAEK,MAAMI,wBAAsC,GAAIT,MAAM,IAAK;EAChE,OAAO,IAAAU,oCAAmB,EAACV,MAAM,EAAGA,MAAM,IAAK;IAC7CA,MAAM,CAACO,UAAU,GAAGI,qBAAqB,CAACX,MAAM,EAAEA,MAAM,CAACO,UAAU,CAAC;IACpE,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACP,OAAA,CAAAgB,wBAAA,GAAAA,wBAAA;AAEK,SAASG,mBAAmBA,CAACZ,MAAkB,EAAE;EAAA,IAAAa,oBAAA;EACtD,OAAO,EAAAA,oBAAA,GAAAb,MAAM,CAACc,YAAY,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,IAAI,KAAI,IAAI;AAC1C;AAEO,SAASC,oBAAoBA,CAAChB,MAAkB,EAAE;EAAA,IAAAiB,qBAAA;EACvD,OAAO,EAAAA,qBAAA,GAAAjB,MAAM,CAACc,YAAY,cAAAG,qBAAA,uBAAnBA,qBAAA,CAAqBC,KAAK,KAAI,IAAI;AAC3C;;AAEA;AACA;AACA;AACA;AACO,eAAehB,wBAAwBA,CAACF,MAAkB,EAAEI,WAAmB,EAAE;EACtF,MAAMW,IAAI,GAAGH,mBAAmB,CAACZ,MAAM,CAAC;EACxC,IAAIe,IAAI,EAAE;IACR,MAAMI,oCAAoC,CAACJ,IAAI,EAAEX,WAAW,CAAC;EAC/D,CAAC,MAAM;IACL,MAAMgB,qCAAqC,CAAChB,WAAW,CAAC;EAC1D;AACF;AAEO,SAASO,qBAAqBA,CAACX,MAAkB,EAAEqB,QAAyB,EAAE;EACnF,MAAMN,IAAI,GAAGH,mBAAmB,CAACZ,MAAM,CAAC;EACxC,MAAMkB,KAAK,GAAGF,oBAAoB,CAAChB,MAAM,CAAC;EAC1C,MAAMsB,eAAe,GAAGlC,yBAAyB,CAACiC,QAAQ,CAAC;EAC3D,IAAIN,IAAI,EAAE;IACR5B,gCAAgC,CAC9BmC,eAAe,EACf9B,2BAA2B,EAC3BI,0BAA0B,EAC1B,UAAU,CACX;EACH,CAAC,MAAM;IACLP,qCAAqC,CAACiC,eAAe,EAAE9B,2BAA2B,CAAC;EACrF;EACA,IAAI0B,KAAK,EAAE;IACT/B,gCAAgC,CAC9BmC,eAAe,EACf5B,iCAAiC,EACjCI,gCAAgC,EAChC,UAAU,CACX;EACH,CAAC,MAAM;IACLT,qCAAqC,CAACiC,eAAe,EAAE5B,iCAAiC,CAAC;EAC3F;EACA,OAAO2B,QAAQ;AACjB;AAEO,SAASb,wBAAwBA,CACtCR,MAAkB,EAClBuB,MAA2C,EAC3C;EACA,OAAOtC,MAAM,CAACuC,gBAAgB,CAACD,MAAM,EAAE;IACrCE,IAAI,EAAE5B,uBAAuB;IAC7B6B,KAAK,EAAEV,oBAAoB,CAAChB,MAAM;EACpC,CAAC,CAAC;AACJ;AAEA,eAAemB,oCAAoCA,CAACJ,IAAY,EAAEX,WAAmB,EAAE;EACrF,MAAMuB,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,MAAM,CAACC,6BAAS,CAAC,CAACC,GAAG,CAAC,OAAO;IAAEC,UAAU;IAAEC;EAAM,CAAC,KAAK;IAC5D,MAAMC,kBAAkB,GAAGF,UAAU,CAACG,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;IACnE,MAAMC,aAAa,GAAGC,eAAI,CAACC,OAAO,CAACnC,WAAW,EAAEoC,oCAAgB,EAAEL,kBAAkB,CAAC;IACrF,MAAMM,kBAAE,CAACC,SAAS,CAACL,aAAa,CAAC;IACjC,MAAMM,UAAU,GAAGpD,mBAAmB,GAAG2C,KAAK;IAE9C,IAAI;MACF,MAAMU,WAAW,GAAG,CAClB,MAAM,IAAAC,gCAAkB,EACtB;QAAEzC,WAAW;QAAE0C,SAAS,EAAE;MAAuB,CAAC,EAClD;QACEC,GAAG,EAAEhC,IAAI;QACTiC,KAAK,EAAEL,UAAU;QACjBM,MAAM,EAAEN,UAAU;QAClBO,UAAU,EAAE,OAAO;QACnBC,eAAe,EAAE;MACnB,CAAC,CACF,EACDC,MAAM;MACR,MAAMX,kBAAE,CAACY,SAAS,CAACf,eAAI,CAACC,OAAO,CAACF,aAAa,EAAE1C,iBAAiB,GAAG,MAAM,CAAC,EAAEiD,WAAW,CAAC;IAC1F,CAAC,CAAC,OAAOU,CAAC,EAAE;MACV,MAAM,IAAIC,KAAK,CAAC,2DAA2D,GAAGD,CAAC,CAAC;IAClF;EACF,CAAC,CAAC,CACH;AACH;AAEA,eAAelC,qCAAqCA,CAAChB,WAAmB,EAAE;EACxE,MAAMuB,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,MAAM,CAACC,6BAAS,CAAC,CAACC,GAAG,CAAC,OAAO;IAAEC;EAAW,CAAC,KAAK;IACrD,MAAME,kBAAkB,GAAGF,UAAU,CAACG,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;IACnE,MAAMC,aAAa,GAAGC,eAAI,CAACC,OAAO,CAACnC,WAAW,EAAEoC,oCAAgB,EAAEL,kBAAkB,CAAC;IACrF,MAAMM,kBAAE,CAACe,MAAM,CAAClB,eAAI,CAACC,OAAO,CAACF,aAAa,EAAE1C,iBAAiB,GAAG,MAAM,CAAC,CAAC;EAC1E,CAAC,CAAC,CACH;AACH"}