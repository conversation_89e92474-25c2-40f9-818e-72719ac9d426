{"version": 3, "file": "withAndroidAdMob.js", "names": ["_configPlugins", "data", "require", "addMetaDataItemToMainApplication", "getMainApplicationOrThrow", "removeMetaDataItemFromMainApplication", "AndroidConfig", "Manifest", "META_APPLICATION_ID", "META_DELAY_APP_MEASUREMENT_INIT", "withAndroidAdMob", "config", "withAndroidManifest", "modResults", "setAdMobConfig", "exports", "getGoogleMobileAdsAppId", "_config$android$confi", "_config$android", "_config$android$confi2", "android", "googleMobileAdsAppId", "getGoogleMobileAdsAutoInit", "_config$android$confi3", "_config$android2", "_config$android2$conf", "googleMobileAdsAutoInit", "androidManifest", "appId", "autoInit", "mainApplication", "String"], "sources": ["../../../../src/plugins/unversioned/expo-ads-admob/withAndroidAdMob.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, withAndroidManifest } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nconst {\n  addMetaDataItemToMainApplication,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} = AndroidConfig.Manifest;\n\nconst META_APPLICATION_ID = 'com.google.android.gms.ads.APPLICATION_ID';\nconst META_DELAY_APP_MEASUREMENT_INIT = 'com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT';\n\nexport const withAndroidAdMob: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, (config) => {\n    config.modResults = setAdMobConfig(config, config.modResults);\n    return config;\n  });\n};\n\nexport function getGoogleMobileAdsAppId(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMobileAdsAppId ?? null;\n}\n\nexport function getGoogleMobileAdsAutoInit(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMobileAdsAutoInit ?? false;\n}\n\nexport function setAdMobConfig(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidConfig.Manifest.AndroidManifest\n) {\n  const appId = getGoogleMobileAdsAppId(config);\n  const autoInit = getGoogleMobileAdsAutoInit(config);\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  if (appId) {\n    addMetaDataItemToMainApplication(mainApplication, META_APPLICATION_ID, appId);\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DELAY_APP_MEASUREMENT_INIT,\n      String(!autoInit)\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_APPLICATION_ID);\n    removeMetaDataItemFromMainApplication(mainApplication, META_DELAY_APP_MEASUREMENT_INIT);\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,MAAM;EACJE,gCAAgC;EAChCC,yBAAyB;EACzBC;AACF,CAAC,GAAGC,8BAAa,CAACC,QAAQ;AAE1B,MAAMC,mBAAmB,GAAG,2CAA2C;AACvE,MAAMC,+BAA+B,GAAG,uDAAuD;AAExF,MAAMC,gBAA8B,GAAIC,MAAM,IAAK;EACxD,OAAO,IAAAC,oCAAmB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC7CA,MAAM,CAACE,UAAU,GAAGC,cAAc,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACI,OAAA,CAAAL,gBAAA,GAAAA,gBAAA;AAEK,SAASM,uBAAuBA,CAACL,MAAmC,EAAE;EAAA,IAAAM,qBAAA,EAAAC,eAAA,EAAAC,sBAAA;EAC3E,QAAAF,qBAAA,IAAAC,eAAA,GAAOP,MAAM,CAACS,OAAO,cAAAF,eAAA,wBAAAC,sBAAA,GAAdD,eAAA,CAAgBP,MAAM,cAAAQ,sBAAA,uBAAtBA,sBAAA,CAAwBE,oBAAoB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC7D;AAEO,SAASK,0BAA0BA,CAACX,MAAmC,EAAE;EAAA,IAAAY,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAC9E,QAAAF,sBAAA,IAAAC,gBAAA,GAAOb,MAAM,CAACS,OAAO,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,MAAM,cAAAc,qBAAA,uBAAtBA,qBAAA,CAAwBC,uBAAuB,cAAAH,sBAAA,cAAAA,sBAAA,GAAI,KAAK;AACjE;AAEO,SAAST,cAAcA,CAC5BH,MAAmC,EACnCgB,eAAuD,EACvD;EACA,MAAMC,KAAK,GAAGZ,uBAAuB,CAACL,MAAM,CAAC;EAC7C,MAAMkB,QAAQ,GAAGP,0BAA0B,CAACX,MAAM,CAAC;EACnD,MAAMmB,eAAe,GAAG1B,yBAAyB,CAACuB,eAAe,CAAC;EAElE,IAAIC,KAAK,EAAE;IACTzB,gCAAgC,CAAC2B,eAAe,EAAEtB,mBAAmB,EAAEoB,KAAK,CAAC;IAC7EzB,gCAAgC,CAC9B2B,eAAe,EACfrB,+BAA+B,EAC/BsB,MAAM,CAAC,CAACF,QAAQ,CAAC,CAClB;EACH,CAAC,MAAM;IACLxB,qCAAqC,CAACyB,eAAe,EAAEtB,mBAAmB,CAAC;IAC3EH,qCAAqC,CAACyB,eAAe,EAAErB,+BAA+B,CAAC;EACzF;EAEA,OAAOkB,eAAe;AACxB"}