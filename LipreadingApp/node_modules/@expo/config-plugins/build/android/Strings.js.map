{"version": 3, "file": "Strings.js", "names": ["_Paths", "data", "require", "getProjectStringsXMLPathAsync", "projectRoot", "kind", "getResourceXMLPathAsync", "name", "setStringItem", "itemToAdd", "stringFileContentsJSON", "_stringFileContentsJS", "resources", "string", "newItem", "stringNameExists", "findIndex", "e", "$", "concat", "removeStringItem", "named", "_stringFileContentsJS2", "splice"], "sources": ["../../src/android/Strings.ts"], "sourcesContent": ["import { getResourceXMLPathAsync } from './Paths';\nimport { ResourceItemXML, ResourceKind, ResourceXML } from './Resources';\n\nexport async function getProjectStringsXMLPathAsync(\n  projectRoot: string,\n  { kind }: { kind?: ResourceKind } = {}\n): Promise<string> {\n  return getResourceXMLPathAsync(projectRoot, { kind, name: 'strings' });\n}\n\nexport function setStringItem(\n  itemToAdd: ResourceItemXML[],\n  stringFileContentsJSON: ResourceXML\n): ResourceXML {\n  if (!stringFileContentsJSON?.resources?.string) {\n    if (!stringFileContentsJSON.resources || typeof stringFileContentsJSON.resources === 'string') {\n      // file was empty and JSON is `{resources : ''}`\n      stringFileContentsJSON.resources = {};\n    }\n    stringFileContentsJSON.resources.string = itemToAdd;\n    return stringFileContentsJSON;\n  }\n\n  for (const newItem of itemToAdd) {\n    const stringNameExists = stringFileContentsJSON.resources.string.findIndex(\n      (e: ResourceItemXML) => e.$.name === newItem.$.name\n    );\n    if (stringNameExists > -1) {\n      // replace the previous item\n      stringFileContentsJSON.resources.string[stringNameExists] = newItem;\n    } else {\n      stringFileContentsJSON.resources.string =\n        stringFileContentsJSON.resources.string.concat(newItem);\n    }\n  }\n  return stringFileContentsJSON;\n}\n\nexport function removeStringItem(named: string, stringFileContentsJSON: ResourceXML): ResourceXML {\n  if (stringFileContentsJSON?.resources?.string) {\n    const stringNameExists = stringFileContentsJSON.resources.string.findIndex(\n      (e: ResourceItemXML) => e.$.name === named\n    );\n    if (stringNameExists > -1) {\n      // replace the previous value\n      stringFileContentsJSON.resources.string.splice(stringNameExists, 1);\n    }\n  }\n  return stringFileContentsJSON;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,eAAeE,6BAA6BA,CACjDC,WAAmB,EACnB;EAAEC;AAA8B,CAAC,GAAG,CAAC,CAAC,EACrB;EACjB,OAAO,IAAAC,gCAAuB,EAACF,WAAW,EAAE;IAAEC,IAAI;IAAEE,IAAI,EAAE;EAAU,CAAC,CAAC;AACxE;AAEO,SAASC,aAAaA,CAC3BC,SAA4B,EAC5BC,sBAAmC,EACtB;EAAA,IAAAC,qBAAA;EACb,IAAI,EAACD,sBAAsB,aAAtBA,sBAAsB,gBAAAC,qBAAA,GAAtBD,sBAAsB,CAAEE,SAAS,cAAAD,qBAAA,eAAjCA,qBAAA,CAAmCE,MAAM,GAAE;IAC9C,IAAI,CAACH,sBAAsB,CAACE,SAAS,IAAI,OAAOF,sBAAsB,CAACE,SAAS,KAAK,QAAQ,EAAE;MAC7F;MACAF,sBAAsB,CAACE,SAAS,GAAG,CAAC,CAAC;IACvC;IACAF,sBAAsB,CAACE,SAAS,CAACC,MAAM,GAAGJ,SAAS;IACnD,OAAOC,sBAAsB;EAC/B;EAEA,KAAK,MAAMI,OAAO,IAAIL,SAAS,EAAE;IAC/B,MAAMM,gBAAgB,GAAGL,sBAAsB,CAACE,SAAS,CAACC,MAAM,CAACG,SAAS,CACvEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACX,IAAI,KAAKO,OAAO,CAACI,CAAC,CAACX,IAAI,CACpD;IACD,IAAIQ,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB;MACAL,sBAAsB,CAACE,SAAS,CAACC,MAAM,CAACE,gBAAgB,CAAC,GAAGD,OAAO;IACrE,CAAC,MAAM;MACLJ,sBAAsB,CAACE,SAAS,CAACC,MAAM,GACrCH,sBAAsB,CAACE,SAAS,CAACC,MAAM,CAACM,MAAM,CAACL,OAAO,CAAC;IAC3D;EACF;EACA,OAAOJ,sBAAsB;AAC/B;AAEO,SAASU,gBAAgBA,CAACC,KAAa,EAAEX,sBAAmC,EAAe;EAAA,IAAAY,sBAAA;EAChG,IAAIZ,sBAAsB,aAAtBA,sBAAsB,gBAAAY,sBAAA,GAAtBZ,sBAAsB,CAAEE,SAAS,cAAAU,sBAAA,eAAjCA,sBAAA,CAAmCT,MAAM,EAAE;IAC7C,MAAME,gBAAgB,GAAGL,sBAAsB,CAACE,SAAS,CAACC,MAAM,CAACG,SAAS,CACvEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACX,IAAI,KAAKc,KAAK,CAC3C;IACD,IAAIN,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB;MACAL,sBAAsB,CAACE,SAAS,CAACC,MAAM,CAACU,MAAM,CAACR,gBAAgB,EAAE,CAAC,CAAC;IACrE;EACF;EACA,OAAOL,sBAAsB;AAC/B"}