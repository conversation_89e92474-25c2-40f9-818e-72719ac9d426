{"version": 3, "file": "Xcodeproj.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_path", "_slugify", "_xcode", "_pbxFile", "_warnings", "Paths", "_interopRequireWildcard", "_string", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "getProjectName", "projectRoot", "sourceRoot", "getSourceRoot", "path", "basename", "resolvePathOrProject", "projectRootOrProject", "getPbxproj", "sanitizedName", "name", "sanitizedNameForProjects", "slugify", "replace", "normalize", "getHackyProjectName", "config", "projectName", "assert", "createProjectFileForGroup", "filepath", "group", "file", "pbxFile", "conflictingFile", "children", "find", "child", "comment", "addResourceFileToGroup", "groupName", "isBuildFile", "project", "verbose", "targetUuid", "addFileToGroupAndLink", "addFileToProject", "addToPbxFileReferenceSection", "addToPbxBuildFileSection", "addToPbxResourcesBuildPhase", "addBuildSourceFileToGroup", "addToPbxSourcesBuildPhase", "pbxGroupByPathOrAssert", "addWarningIOS", "target", "applicationNativeTarget", "get<PERSON><PERSON><PERSON>", "uuid", "generateUuid", "fileRef", "push", "value", "getApplicationNativeTarget", "String", "addFramework", "framework", "splitPath", "split", "findGroup", "undefined", "findGroupInsideGroup", "foundGroup", "_project$getPBXGroupB", "getPBXGroupByKey", "firstProject", "getFirstProject", "mainGroup", "components", "nextGroup", "Error", "ensureGroupRecursively", "_topMostGroup", "<PERSON><PERSON><PERSON><PERSON>", "topMostGroup", "pathComponent", "pbxCreateGroup", "pbxGroupByName", "projectPath", "getPBXProjectPath", "xcode", "parseSync", "getProductName", "productName", "_project$getFirstTarg", "_project$getFirstTarg2", "targetName", "get<PERSON><PERSON>t<PERSON>arget", "firstTarget", "getProjectSection", "pbxProjectSection", "getXCConfigurationListEntries", "lists", "pbxXCConfigurationList", "entries", "filter", "isNotComment", "getBuildConfigurationsForListId", "configurationListId", "configurationListEntries", "configurationList", "buildConfigurations", "map", "i", "pbxXCBuildConfigurationSection", "isBuildConfig", "includes", "getBuildConfigurationForListIdAndName", "buildConfiguration", "xcBuildConfigurationEntry", "trimQuotes", "sectionItem", "isa", "isNotTestHost", "buildSettings", "TEST_HOST", "endsWith", "unquote", "_value$match$", "_value$match", "match"], "sources": ["../../../src/ios/utils/Xcodeproj.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport assert from 'assert';\nimport path from 'path';\nimport slugify from 'slugify';\nimport xcode, {\n  PBXFile,\n  PBXGroup,\n  PBXNativeTarget,\n  PBXProject,\n  UUID,\n  XCBuildConfiguration,\n  XCConfigurationList,\n  XcodeProject,\n} from 'xcode';\nimport pbxFile from 'xcode/lib/pbxFile';\n\nimport { addWarningIOS } from '../../utils/warnings';\nimport * as Paths from '../Paths';\nimport { trimQuotes } from './string';\n\nexport type ProjectSectionEntry = [string, PBXProject];\n\nexport type NativeTargetSection = Record<string, PBXNativeTarget>;\n\nexport type NativeTargetSectionEntry = [string, PBXNativeTarget];\n\nexport type ConfigurationLists = Record<string, XCConfigurationList>;\n\nexport type ConfigurationListEntry = [string, XCConfigurationList];\n\nexport type ConfigurationSectionEntry = [string, XCBuildConfiguration];\n\nexport function getProjectName(projectRoot: string) {\n  const sourceRoot = Paths.getSourceRoot(projectRoot);\n  return path.basename(sourceRoot);\n}\n\nexport function resolvePathOrProject(\n  projectRootOrProject: string | XcodeProject\n): XcodeProject | null {\n  if (typeof projectRootOrProject === 'string') {\n    try {\n      return getPbxproj(projectRootOrProject);\n    } catch {\n      return null;\n    }\n  }\n  return projectRootOrProject;\n}\n\n// TODO: come up with a better solution for using app.json expo.name in various places\nexport function sanitizedName(name: string) {\n  // Default to the name `app` when every safe character has been sanitized\n  return sanitizedNameForProjects(name) || sanitizedNameForProjects(slugify(name)) || 'app';\n}\n\nfunction sanitizedNameForProjects(name: string) {\n  return name\n    .replace(/[\\W_]+/g, '')\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '');\n}\n\n// TODO: it's silly and kind of fragile that we look at app config to determine\n// the ios project paths. Overall this function needs to be revamped, just a\n// placeholder for now! Make this more robust when we support applying config\n// at any time (currently it's only applied on eject).\nexport function getHackyProjectName(projectRoot: string, config: ExpoConfig): string {\n  // Attempt to get the current ios folder name (apply).\n  try {\n    return getProjectName(projectRoot);\n  } catch {\n    // If no iOS project exists then create a new one (eject).\n    const projectName = config.name;\n    assert(projectName, 'Your project needs a name in app.json/app.config.js.');\n    return sanitizedName(projectName);\n  }\n}\n\nfunction createProjectFileForGroup({ filepath, group }: { filepath: string; group: PBXGroup }) {\n  const file = new pbxFile(filepath);\n\n  const conflictingFile = group.children.find((child) => child.comment === file.basename);\n  if (conflictingFile) {\n    // This can happen when a file like the GoogleService-Info.plist needs to be added and the eject command is run twice.\n    // Not much we can do here since it might be a conflicting file.\n    return null;\n  }\n  return file;\n}\n\n/**\n * Add a resource file (ex: `SplashScreen.storyboard`, `Images.xcassets`) to an Xcode project.\n * This is akin to creating a new code file in Xcode with `⌘+n`.\n */\nexport function addResourceFileToGroup({\n  filepath,\n  groupName,\n  // Should add to `PBXBuildFile Section`\n  isBuildFile,\n  project,\n  verbose,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  isBuildFile?: boolean;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n}): XcodeProject {\n  return addFileToGroupAndLink({\n    filepath,\n    groupName,\n    project,\n    verbose,\n    targetUuid,\n    addFileToProject({ project, file }) {\n      project.addToPbxFileReferenceSection(file);\n      if (isBuildFile) {\n        project.addToPbxBuildFileSection(file);\n      }\n      project.addToPbxResourcesBuildPhase(file);\n    },\n  });\n}\n\n/**\n * Add a build source file (ex: `AppDelegate.m`, `ViewController.swift`) to an Xcode project.\n * This is akin to creating a new code file in Xcode with `⌘+n`.\n */\nexport function addBuildSourceFileToGroup({\n  filepath,\n  groupName,\n  project,\n  verbose,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n}): XcodeProject {\n  return addFileToGroupAndLink({\n    filepath,\n    groupName,\n    project,\n    verbose,\n    targetUuid,\n    addFileToProject({ project, file }) {\n      project.addToPbxFileReferenceSection(file);\n      project.addToPbxBuildFileSection(file);\n      project.addToPbxSourcesBuildPhase(file);\n    },\n  });\n}\n\n// TODO(brentvatne): I couldn't figure out how to do this with an existing\n// higher level function exposed by the xcode library, but we should find out how to do\n// that and replace this with it\nexport function addFileToGroupAndLink({\n  filepath,\n  groupName,\n  project,\n  verbose,\n  addFileToProject,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n  addFileToProject: (props: { file: PBXFile; project: XcodeProject }) => void;\n}): XcodeProject {\n  const group = pbxGroupByPathOrAssert(project, groupName);\n\n  const file = createProjectFileForGroup({ filepath, group });\n\n  if (!file) {\n    if (verbose) {\n      // This can happen when a file like the GoogleService-Info.plist needs to be added and the eject command is run twice.\n      // Not much we can do here since it might be a conflicting file.\n      addWarningIOS(\n        'ios-xcode-project',\n        `Skipped adding duplicate file \"${filepath}\" to PBXGroup named \"${groupName}\"`\n      );\n    }\n    return project;\n  }\n\n  if (targetUuid != null) {\n    file.target = targetUuid;\n  } else {\n    const applicationNativeTarget = project.getTarget('com.apple.product-type.application');\n    file.target = applicationNativeTarget?.uuid;\n  }\n\n  file.uuid = project.generateUuid();\n  file.fileRef = project.generateUuid();\n\n  addFileToProject({ project, file });\n\n  group.children.push({\n    value: file.fileRef,\n    comment: file.basename,\n  });\n  return project;\n}\n\nexport function getApplicationNativeTarget({\n  project,\n  projectName,\n}: {\n  project: XcodeProject;\n  projectName: string;\n}) {\n  const applicationNativeTarget = project.getTarget('com.apple.product-type.application');\n  assert(\n    applicationNativeTarget,\n    `Couldn't locate application PBXNativeTarget in '.xcodeproj' file.`\n  );\n  assert(\n    String(applicationNativeTarget.target.name) === projectName,\n    `Application native target name mismatch. Expected ${projectName}, but found ${applicationNativeTarget.target.name}.`\n  );\n  return applicationNativeTarget;\n}\n\n/**\n * Add a framework to the default app native target.\n *\n * @param projectName Name of the PBX project.\n * @param framework String ending in `.framework`, i.e. `StoreKit.framework`\n */\nexport function addFramework({\n  project,\n  projectName,\n  framework,\n}: {\n  project: XcodeProject;\n  projectName: string;\n  framework: string;\n}) {\n  const target = getApplicationNativeTarget({ project, projectName });\n  return project.addFramework(framework, { target: target.uuid });\n}\n\nfunction splitPath(path: string): string[] {\n  // TODO: Should we account for other platforms that may not use `/`\n  return path.split('/');\n}\n\nconst findGroup = (\n  group: PBXGroup | undefined,\n  name: string\n):\n  | {\n      value: UUID;\n      comment?: string;\n    }\n  | undefined => {\n  if (!group) {\n    return undefined;\n  }\n\n  return group.children.find((group) => group.comment === name);\n};\n\nfunction findGroupInsideGroup(\n  project: XcodeProject,\n  group: PBXGroup | undefined,\n  name: string\n): null | PBXGroup {\n  const foundGroup = findGroup(group, name);\n  if (foundGroup) {\n    return project.getPBXGroupByKey(foundGroup.value) ?? null;\n  }\n  return null;\n}\n\nfunction pbxGroupByPathOrAssert(project: XcodeProject, path: string): PBXGroup {\n  const { firstProject } = project.getFirstProject();\n\n  let group = project.getPBXGroupByKey(firstProject.mainGroup);\n\n  const components = splitPath(path);\n  for (const name of components) {\n    const nextGroup = findGroupInsideGroup(project, group, name);\n    if (nextGroup) {\n      group = nextGroup;\n    } else {\n      break;\n    }\n  }\n\n  if (!group) {\n    throw Error(`Xcode PBXGroup with name \"${path}\" could not be found in the Xcode project.`);\n  }\n\n  return group;\n}\n\nexport function ensureGroupRecursively(project: XcodeProject, filepath: string): PBXGroup | null {\n  const components = splitPath(filepath);\n  const hasChild = (group: PBXGroup, name: string) =>\n    group.children.find(({ comment }) => comment === name);\n  const { firstProject } = project.getFirstProject();\n\n  let topMostGroup = project.getPBXGroupByKey(firstProject.mainGroup);\n\n  for (const pathComponent of components) {\n    if (topMostGroup && !hasChild(topMostGroup, pathComponent)) {\n      topMostGroup.children.push({\n        comment: pathComponent,\n        value: project.pbxCreateGroup(pathComponent, '\"\"'),\n      });\n    }\n    topMostGroup = project.pbxGroupByName(pathComponent);\n  }\n  return topMostGroup ?? null;\n}\n\n/**\n * Get the pbxproj for the given path\n */\nexport function getPbxproj(projectRoot: string): XcodeProject {\n  const projectPath = Paths.getPBXProjectPath(projectRoot);\n  const project = xcode.project(projectPath);\n  project.parseSync();\n  return project;\n}\n\n/**\n * Get the productName for a project, if the name is using a variable `$(TARGET_NAME)`, then attempt to get the value of that variable.\n *\n * @param project\n */\nexport function getProductName(project: XcodeProject): string {\n  let productName = '$(TARGET_NAME)';\n  try {\n    // If the product name is numeric, this will fail (it's a getter).\n    // If the bundle identifier' final component is only numeric values, then the PRODUCT_NAME\n    // will be a numeric value, this results in a bug where the product name isn't useful,\n    // i.e. `com.bacon.001` -> `1` -- in this case, use the first target name.\n    productName = project.productName;\n  } catch {}\n\n  if (productName === '$(TARGET_NAME)') {\n    const targetName = project.getFirstTarget()?.firstTarget?.productName;\n    productName = targetName ?? productName;\n  }\n\n  return productName;\n}\n\nexport function getProjectSection(project: XcodeProject) {\n  return project.pbxProjectSection();\n}\n\nexport function getXCConfigurationListEntries(project: XcodeProject): ConfigurationListEntry[] {\n  const lists = project.pbxXCConfigurationList();\n  return Object.entries(lists).filter(isNotComment);\n}\n\nexport function getBuildConfigurationsForListId(\n  project: XcodeProject,\n  configurationListId: string\n): ConfigurationSectionEntry[] {\n  const configurationListEntries = getXCConfigurationListEntries(project);\n  const [, configurationList] = configurationListEntries.find(\n    ([key]) => key === configurationListId\n  ) as ConfigurationListEntry;\n\n  const buildConfigurations = configurationList.buildConfigurations.map((i) => i.value);\n\n  return Object.entries(project.pbxXCBuildConfigurationSection())\n    .filter(isNotComment)\n    .filter(isBuildConfig)\n    .filter(([key]: ConfigurationSectionEntry) => buildConfigurations.includes(key));\n}\n\nexport function getBuildConfigurationForListIdAndName(\n  project: XcodeProject,\n  {\n    configurationListId,\n    buildConfiguration,\n  }: { configurationListId: string; buildConfiguration: string }\n): ConfigurationSectionEntry {\n  const xcBuildConfigurationEntry = getBuildConfigurationsForListId(\n    project,\n    configurationListId\n  ).find((i) => trimQuotes(i[1].name) === buildConfiguration);\n  if (!xcBuildConfigurationEntry) {\n    throw new Error(\n      `Build configuration '${buildConfiguration}' does not exist in list with id '${configurationListId}'`\n    );\n  }\n  return xcBuildConfigurationEntry;\n}\n\nexport function isBuildConfig([, sectionItem]: ConfigurationSectionEntry): boolean {\n  return sectionItem.isa === 'XCBuildConfiguration';\n}\n\nexport function isNotTestHost([, sectionItem]: ConfigurationSectionEntry): boolean {\n  return !sectionItem.buildSettings.TEST_HOST;\n}\n\nexport function isNotComment([key]:\n  | ConfigurationSectionEntry\n  | ProjectSectionEntry\n  | ConfigurationListEntry\n  | NativeTargetSectionEntry): boolean {\n  return !key.endsWith(`_comment`);\n}\n\n// Remove surrounding double quotes if they exist.\nexport function unquote(value: string): string {\n  // projects with numeric names will fail due to a bug in the xcode package.\n  if (typeof value === 'number') {\n    value = String(value);\n  }\n  return value.match(/^\"(.*)\"$/)?.[1] ?? value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,OAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,MAAA;EAAA,MAAAR,IAAA,GAAAS,uBAAA,CAAAP,OAAA;EAAAM,KAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,QAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,OAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsC,SAAAW,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAArB,uBAAAe,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAc/B,SAASiB,cAAcA,CAACC,WAAmB,EAAE;EAClD,MAAMC,UAAU,GAAG3B,KAAK,GAAC4B,aAAa,CAACF,WAAW,CAAC;EACnD,OAAOG,eAAI,CAACC,QAAQ,CAACH,UAAU,CAAC;AAClC;AAEO,SAASI,oBAAoBA,CAClCC,oBAA2C,EACtB;EACrB,IAAI,OAAOA,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,IAAI;MACF,OAAOC,UAAU,CAACD,oBAAoB,CAAC;IACzC,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;EACA,OAAOA,oBAAoB;AAC7B;;AAEA;AACO,SAASE,aAAaA,CAACC,IAAY,EAAE;EAC1C;EACA,OAAOC,wBAAwB,CAACD,IAAI,CAAC,IAAIC,wBAAwB,CAAC,IAAAC,kBAAO,EAACF,IAAI,CAAC,CAAC,IAAI,KAAK;AAC3F;AAEA,SAASC,wBAAwBA,CAACD,IAAY,EAAE;EAC9C,OAAOA,IAAI,CACRG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBC,SAAS,CAAC,KAAK,CAAC,CAChBD,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACO,SAASE,mBAAmBA,CAACd,WAAmB,EAAEe,MAAkB,EAAU;EACnF;EACA,IAAI;IACF,OAAOhB,cAAc,CAACC,WAAW,CAAC;EACpC,CAAC,CAAC,MAAM;IACN;IACA,MAAMgB,WAAW,GAAGD,MAAM,CAACN,IAAI;IAC/B,IAAAQ,iBAAM,EAACD,WAAW,EAAE,sDAAsD,CAAC;IAC3E,OAAOR,aAAa,CAACQ,WAAW,CAAC;EACnC;AACF;AAEA,SAASE,yBAAyBA,CAAC;EAAEC,QAAQ;EAAEC;AAA6C,CAAC,EAAE;EAC7F,MAAMC,IAAI,GAAG,KAAIC,kBAAO,EAACH,QAAQ,CAAC;EAElC,MAAMI,eAAe,GAAGH,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,OAAO,KAAKN,IAAI,CAACjB,QAAQ,CAAC;EACvF,IAAImB,eAAe,EAAE;IACnB;IACA;IACA,OAAO,IAAI;EACb;EACA,OAAOF,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACO,SAASO,sBAAsBA,CAAC;EACrCT,QAAQ;EACRU,SAAS;EACT;EACAC,WAAW;EACXC,OAAO;EACPC,OAAO;EACPC;AAQF,CAAC,EAAgB;EACf,OAAOC,qBAAqB,CAAC;IAC3Bf,QAAQ;IACRU,SAAS;IACTE,OAAO;IACPC,OAAO;IACPC,UAAU;IACVE,gBAAgBA,CAAC;MAAEJ,OAAO;MAAEV;IAAK,CAAC,EAAE;MAClCU,OAAO,CAACK,4BAA4B,CAACf,IAAI,CAAC;MAC1C,IAAIS,WAAW,EAAE;QACfC,OAAO,CAACM,wBAAwB,CAAChB,IAAI,CAAC;MACxC;MACAU,OAAO,CAACO,2BAA2B,CAACjB,IAAI,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASkB,yBAAyBA,CAAC;EACxCpB,QAAQ;EACRU,SAAS;EACTE,OAAO;EACPC,OAAO;EACPC;AAOF,CAAC,EAAgB;EACf,OAAOC,qBAAqB,CAAC;IAC3Bf,QAAQ;IACRU,SAAS;IACTE,OAAO;IACPC,OAAO;IACPC,UAAU;IACVE,gBAAgBA,CAAC;MAAEJ,OAAO;MAAEV;IAAK,CAAC,EAAE;MAClCU,OAAO,CAACK,4BAA4B,CAACf,IAAI,CAAC;MAC1CU,OAAO,CAACM,wBAAwB,CAAChB,IAAI,CAAC;MACtCU,OAAO,CAACS,yBAAyB,CAACnB,IAAI,CAAC;IACzC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASa,qBAAqBA,CAAC;EACpCf,QAAQ;EACRU,SAAS;EACTE,OAAO;EACPC,OAAO;EACPG,gBAAgB;EAChBF;AAQF,CAAC,EAAgB;EACf,MAAMb,KAAK,GAAGqB,sBAAsB,CAACV,OAAO,EAAEF,SAAS,CAAC;EAExD,MAAMR,IAAI,GAAGH,yBAAyB,CAAC;IAAEC,QAAQ;IAAEC;EAAM,CAAC,CAAC;EAE3D,IAAI,CAACC,IAAI,EAAE;IACT,IAAIW,OAAO,EAAE;MACX;MACA;MACA,IAAAU,yBAAa,EACX,mBAAmB,EAClB,kCAAiCvB,QAAS,wBAAuBU,SAAU,GAAE,CAC/E;IACH;IACA,OAAOE,OAAO;EAChB;EAEA,IAAIE,UAAU,IAAI,IAAI,EAAE;IACtBZ,IAAI,CAACsB,MAAM,GAAGV,UAAU;EAC1B,CAAC,MAAM;IACL,MAAMW,uBAAuB,GAAGb,OAAO,CAACc,SAAS,CAAC,oCAAoC,CAAC;IACvFxB,IAAI,CAACsB,MAAM,GAAGC,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEE,IAAI;EAC7C;EAEAzB,IAAI,CAACyB,IAAI,GAAGf,OAAO,CAACgB,YAAY,EAAE;EAClC1B,IAAI,CAAC2B,OAAO,GAAGjB,OAAO,CAACgB,YAAY,EAAE;EAErCZ,gBAAgB,CAAC;IAAEJ,OAAO;IAAEV;EAAK,CAAC,CAAC;EAEnCD,KAAK,CAACI,QAAQ,CAACyB,IAAI,CAAC;IAClBC,KAAK,EAAE7B,IAAI,CAAC2B,OAAO;IACnBrB,OAAO,EAAEN,IAAI,CAACjB;EAChB,CAAC,CAAC;EACF,OAAO2B,OAAO;AAChB;AAEO,SAASoB,0BAA0BA,CAAC;EACzCpB,OAAO;EACPf;AAIF,CAAC,EAAE;EACD,MAAM4B,uBAAuB,GAAGb,OAAO,CAACc,SAAS,CAAC,oCAAoC,CAAC;EACvF,IAAA5B,iBAAM,EACJ2B,uBAAuB,EACtB,mEAAkE,CACpE;EACD,IAAA3B,iBAAM,EACJmC,MAAM,CAACR,uBAAuB,CAACD,MAAM,CAAClC,IAAI,CAAC,KAAKO,WAAW,EAC1D,qDAAoDA,WAAY,eAAc4B,uBAAuB,CAACD,MAAM,CAAClC,IAAK,GAAE,CACtH;EACD,OAAOmC,uBAAuB;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASS,YAAYA,CAAC;EAC3BtB,OAAO;EACPf,WAAW;EACXsC;AAKF,CAAC,EAAE;EACD,MAAMX,MAAM,GAAGQ,0BAA0B,CAAC;IAAEpB,OAAO;IAAEf;EAAY,CAAC,CAAC;EACnE,OAAOe,OAAO,CAACsB,YAAY,CAACC,SAAS,EAAE;IAAEX,MAAM,EAAEA,MAAM,CAACG;EAAK,CAAC,CAAC;AACjE;AAEA,SAASS,SAASA,CAACpD,IAAY,EAAY;EACzC;EACA,OAAOA,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC;AACxB;AAEA,MAAMC,SAAS,GAAGA,CAChBrC,KAA2B,EAC3BX,IAAY,KAMG;EACf,IAAI,CAACW,KAAK,EAAE;IACV,OAAOsC,SAAS;EAClB;EAEA,OAAOtC,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAEL,KAAK,IAAKA,KAAK,CAACO,OAAO,KAAKlB,IAAI,CAAC;AAC/D,CAAC;AAED,SAASkD,oBAAoBA,CAC3B5B,OAAqB,EACrBX,KAA2B,EAC3BX,IAAY,EACK;EACjB,MAAMmD,UAAU,GAAGH,SAAS,CAACrC,KAAK,EAAEX,IAAI,CAAC;EACzC,IAAImD,UAAU,EAAE;IAAA,IAAAC,qBAAA;IACd,QAAAA,qBAAA,GAAO9B,OAAO,CAAC+B,gBAAgB,CAACF,UAAU,CAACV,KAAK,CAAC,cAAAW,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EAC3D;EACA,OAAO,IAAI;AACb;AAEA,SAASpB,sBAAsBA,CAACV,OAAqB,EAAE5B,IAAY,EAAY;EAC7E,MAAM;IAAE4D;EAAa,CAAC,GAAGhC,OAAO,CAACiC,eAAe,EAAE;EAElD,IAAI5C,KAAK,GAAGW,OAAO,CAAC+B,gBAAgB,CAACC,YAAY,CAACE,SAAS,CAAC;EAE5D,MAAMC,UAAU,GAAGX,SAAS,CAACpD,IAAI,CAAC;EAClC,KAAK,MAAMM,IAAI,IAAIyD,UAAU,EAAE;IAC7B,MAAMC,SAAS,GAAGR,oBAAoB,CAAC5B,OAAO,EAAEX,KAAK,EAAEX,IAAI,CAAC;IAC5D,IAAI0D,SAAS,EAAE;MACb/C,KAAK,GAAG+C,SAAS;IACnB,CAAC,MAAM;MACL;IACF;EACF;EAEA,IAAI,CAAC/C,KAAK,EAAE;IACV,MAAMgD,KAAK,CAAE,6BAA4BjE,IAAK,4CAA2C,CAAC;EAC5F;EAEA,OAAOiB,KAAK;AACd;AAEO,SAASiD,sBAAsBA,CAACtC,OAAqB,EAAEZ,QAAgB,EAAmB;EAAA,IAAAmD,aAAA;EAC/F,MAAMJ,UAAU,GAAGX,SAAS,CAACpC,QAAQ,CAAC;EACtC,MAAMoD,QAAQ,GAAGA,CAACnD,KAAe,EAAEX,IAAY,KAC7CW,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAC,CAAC;IAAEE;EAAQ,CAAC,KAAKA,OAAO,KAAKlB,IAAI,CAAC;EACxD,MAAM;IAAEsD;EAAa,CAAC,GAAGhC,OAAO,CAACiC,eAAe,EAAE;EAElD,IAAIQ,YAAY,GAAGzC,OAAO,CAAC+B,gBAAgB,CAACC,YAAY,CAACE,SAAS,CAAC;EAEnE,KAAK,MAAMQ,aAAa,IAAIP,UAAU,EAAE;IACtC,IAAIM,YAAY,IAAI,CAACD,QAAQ,CAACC,YAAY,EAAEC,aAAa,CAAC,EAAE;MAC1DD,YAAY,CAAChD,QAAQ,CAACyB,IAAI,CAAC;QACzBtB,OAAO,EAAE8C,aAAa;QACtBvB,KAAK,EAAEnB,OAAO,CAAC2C,cAAc,CAACD,aAAa,EAAE,IAAI;MACnD,CAAC,CAAC;IACJ;IACAD,YAAY,GAAGzC,OAAO,CAAC4C,cAAc,CAACF,aAAa,CAAC;EACtD;EACA,QAAAH,aAAA,GAAOE,YAAY,cAAAF,aAAA,cAAAA,aAAA,GAAI,IAAI;AAC7B;;AAEA;AACA;AACA;AACO,SAAS/D,UAAUA,CAACP,WAAmB,EAAgB;EAC5D,MAAM4E,WAAW,GAAGtG,KAAK,GAACuG,iBAAiB,CAAC7E,WAAW,CAAC;EACxD,MAAM+B,OAAO,GAAG+C,gBAAK,CAAC/C,OAAO,CAAC6C,WAAW,CAAC;EAC1C7C,OAAO,CAACgD,SAAS,EAAE;EACnB,OAAOhD,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASiD,cAAcA,CAACjD,OAAqB,EAAU;EAC5D,IAAIkD,WAAW,GAAG,gBAAgB;EAClC,IAAI;IACF;IACA;IACA;IACA;IACAA,WAAW,GAAGlD,OAAO,CAACkD,WAAW;EACnC,CAAC,CAAC,MAAM,CAAC;EAET,IAAIA,WAAW,KAAK,gBAAgB,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACpC,MAAMC,UAAU,IAAAF,qBAAA,GAAGnD,OAAO,CAACsD,cAAc,EAAE,cAAAH,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BI,WAAW,cAAAH,sBAAA,uBAArCA,sBAAA,CAAuCF,WAAW;IACrEA,WAAW,GAAGG,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAIH,WAAW;EACzC;EAEA,OAAOA,WAAW;AACpB;AAEO,SAASM,iBAAiBA,CAACxD,OAAqB,EAAE;EACvD,OAAOA,OAAO,CAACyD,iBAAiB,EAAE;AACpC;AAEO,SAASC,6BAA6BA,CAAC1D,OAAqB,EAA4B;EAC7F,MAAM2D,KAAK,GAAG3D,OAAO,CAAC4D,sBAAsB,EAAE;EAC9C,OAAOrG,MAAM,CAACsG,OAAO,CAACF,KAAK,CAAC,CAACG,MAAM,CAACC,YAAY,CAAC;AACnD;AAEO,SAASC,+BAA+BA,CAC7ChE,OAAqB,EACrBiE,mBAA2B,EACE;EAC7B,MAAMC,wBAAwB,GAAGR,6BAA6B,CAAC1D,OAAO,CAAC;EACvE,MAAM,GAAGmE,iBAAiB,CAAC,GAAGD,wBAAwB,CAACxE,IAAI,CACzD,CAAC,CAAChC,GAAG,CAAC,KAAKA,GAAG,KAAKuG,mBAAmB,CACb;EAE3B,MAAMG,mBAAmB,GAAGD,iBAAiB,CAACC,mBAAmB,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACnD,KAAK,CAAC;EAErF,OAAO5D,MAAM,CAACsG,OAAO,CAAC7D,OAAO,CAACuE,8BAA8B,EAAE,CAAC,CAC5DT,MAAM,CAACC,YAAY,CAAC,CACpBD,MAAM,CAACU,aAAa,CAAC,CACrBV,MAAM,CAAC,CAAC,CAACpG,GAAG,CAA4B,KAAK0G,mBAAmB,CAACK,QAAQ,CAAC/G,GAAG,CAAC,CAAC;AACpF;AAEO,SAASgH,qCAAqCA,CACnD1E,OAAqB,EACrB;EACEiE,mBAAmB;EACnBU;AAC2D,CAAC,EACnC;EAC3B,MAAMC,yBAAyB,GAAGZ,+BAA+B,CAC/DhE,OAAO,EACPiE,mBAAmB,CACpB,CAACvE,IAAI,CAAE4E,CAAC,IAAK,IAAAO,oBAAU,EAACP,CAAC,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC,KAAKiG,kBAAkB,CAAC;EAC3D,IAAI,CAACC,yBAAyB,EAAE;IAC9B,MAAM,IAAIvC,KAAK,CACZ,wBAAuBsC,kBAAmB,qCAAoCV,mBAAoB,GAAE,CACtG;EACH;EACA,OAAOW,yBAAyB;AAClC;AAEO,SAASJ,aAAaA,CAAC,GAAGM,WAAW,CAA4B,EAAW;EACjF,OAAOA,WAAW,CAACC,GAAG,KAAK,sBAAsB;AACnD;AAEO,SAASC,aAAaA,CAAC,GAAGF,WAAW,CAA4B,EAAW;EACjF,OAAO,CAACA,WAAW,CAACG,aAAa,CAACC,SAAS;AAC7C;AAEO,SAASnB,YAAYA,CAAC,CAACrG,GAAG,CAIL,EAAW;EACrC,OAAO,CAACA,GAAG,CAACyH,QAAQ,CAAE,UAAS,CAAC;AAClC;;AAEA;AACO,SAASC,OAAOA,CAACjE,KAAa,EAAU;EAAA,IAAAkE,aAAA,EAAAC,YAAA;EAC7C;EACA,IAAI,OAAOnE,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGE,MAAM,CAACF,KAAK,CAAC;EACvB;EACA,QAAAkE,aAAA,IAAAC,YAAA,GAAOnE,KAAK,CAACoE,KAAK,CAAC,UAAU,CAAC,cAAAD,YAAA,uBAAvBA,YAAA,CAA0B,CAAC,CAAC,cAAAD,aAAA,cAAAA,aAAA,GAAIlE,KAAK;AAC9C"}