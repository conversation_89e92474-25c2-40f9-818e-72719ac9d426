{"version": 3, "file": "getInfoPlistPath.js", "names": ["_Target", "data", "require", "_Xcodeproj", "getInfoPlistPathFromPbxproj", "projectRootOrProject", "targetName", "buildConfiguration", "project", "resolvePathOrProject", "xcBuildConfiguration", "getXCBuildConfigurationFromPbxproj", "sanitizeInfoPlistBuildProperty", "buildSettings", "INFOPLIST_FILE", "infoPlist", "_infoPlist$replace$re", "replace"], "sources": ["../../../src/ios/utils/getInfoPlistPath.ts"], "sourcesContent": ["import { XcodeProject } from 'xcode';\n\nimport { getXCBuildConfigurationFromPbxproj } from '../Target';\nimport { resolvePathOrProject } from './Xcodeproj';\n\n/**\n * Find the Info.plist path linked to a specific build configuration.\n *\n * @param projectRoot\n * @param param1\n * @returns\n */\nexport function getInfoPlistPathFromPbxproj(\n  projectRootOrProject: string | XcodeProject,\n  {\n    targetName,\n    buildConfiguration = 'Release',\n  }: { targetName?: string; buildConfiguration?: string | 'Release' | 'Debug' } = {}\n): string | null {\n  const project = resolvePathOrProject(projectRootOrProject);\n  if (!project) {\n    return null;\n  }\n\n  const xcBuildConfiguration = getXCBuildConfigurationFromPbxproj(project, {\n    targetName,\n    buildConfiguration,\n  });\n  if (!xcBuildConfiguration) {\n    return null;\n  }\n  // The `INFOPLIST_FILE` is relative to the project folder, ex: app/Info.plist.\n  return sanitizeInfoPlistBuildProperty(xcBuildConfiguration.buildSettings.INFOPLIST_FILE);\n}\n\nfunction sanitizeInfoPlistBuildProperty(infoPlist?: string): string | null {\n  return infoPlist?.replace(/\"/g, '').replace('$(SRCROOT)', '') ?? null;\n}\n"], "mappings": ";;;;;;AAEA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,2BAA2BA,CACzCC,oBAA2C,EAC3C;EACEC,UAAU;EACVC,kBAAkB,GAAG;AACqD,CAAC,GAAG,CAAC,CAAC,EACnE;EACf,MAAMC,OAAO,GAAG,IAAAC,iCAAoB,EAACJ,oBAAoB,CAAC;EAC1D,IAAI,CAACG,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAME,oBAAoB,GAAG,IAAAC,4CAAkC,EAACH,OAAO,EAAE;IACvEF,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI,CAACG,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb;EACA;EACA,OAAOE,8BAA8B,CAACF,oBAAoB,CAACG,aAAa,CAACC,cAAc,CAAC;AAC1F;AAEA,SAASF,8BAA8BA,CAACG,SAAkB,EAAiB;EAAA,IAAAC,qBAAA;EACzE,QAAAA,qBAAA,GAAOD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AACvE"}