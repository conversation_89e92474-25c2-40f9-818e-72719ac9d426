{"version": 3, "file": "BundleIdentifier.js", "names": ["_plist", "data", "_interopRequireDefault", "require", "_assert", "_fs", "_xcode", "_withDangerousMod", "_Paths", "_Target", "_Xcodeproj", "_string", "obj", "__esModule", "default", "withBundleIdentifier", "config", "bundleIdentifier", "withDangerousMod", "_config$ios", "bundleId", "ios", "assert", "setBundleIdentifierForPbxproj", "modRequest", "projectRoot", "exports", "getBundleIdentifier", "_config$ios$bundleIde", "_config$ios2", "setBundleIdentifier", "infoPlist", "CFBundleIdentifier", "getBundleIdentifierFromPbxproj", "targetName", "buildConfiguration", "pbxproj<PERSON><PERSON>", "getPBXProjectPath", "project", "xcode", "parseSync", "xcBuildConfiguration", "getXCBuildConfigurationFromPbxproj", "getProductBundleIdentifierFromBuildConfiguration", "bundleIdentifierRaw", "buildSettings", "PRODUCT_BUNDLE_IDENTIFIER", "trimQuotes", "bundleIdentifierParts", "split", "length", "PRODUCT_NAME", "replace", "join", "updateBundleIdentifierForPbxproj", "updateProductName", "nativeTarget", "findFirstNativeTarget", "getBuildConfigurationsForListId", "buildConfigurationList", "for<PERSON>ach", "item", "productName", "pop", "includes", "fs", "writeFileSync", "writeSync", "pbxproj<PERSON><PERSON><PERSON>", "getAllPBXProjectPaths", "defaultBundleId", "resetAllPlistBundleIdentifiers", "infoPlistPaths", "getAllInfoPlistPaths", "plist<PERSON><PERSON>", "resetPlistBundleIdentifier", "rawPlist", "readFileSync", "plistObject", "plist", "parse", "format", "pretty", "indent", "xml", "build"], "sources": ["../../src/ios/BundleIdentifier.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport plist, { PlistObject } from '@expo/plist';\nimport assert from 'assert';\nimport fs from 'fs';\nimport xcode, { XCBuildConfiguration } from 'xcode';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withDangerousMod } from '../plugins/withDangerousMod';\nimport { InfoPlist } from './IosConfig.types';\nimport { getAllInfoPlistPaths, getAllPBXProjectPaths, getPBXProjectPath } from './Paths';\nimport { findFirstNativeTarget, getXCBuildConfigurationFromPbxproj } from './Target';\nimport { ConfigurationSectionEntry, getBuildConfigurationsForListId } from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\n\nexport const withBundleIdentifier: ConfigPlugin<{ bundleIdentifier?: string }> = (\n  config,\n  { bundleIdentifier }\n) => {\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      const bundleId = bundleIdentifier ?? config.ios?.bundleIdentifier;\n      assert(\n        bundleId,\n        '`bundleIdentifier` must be defined in the app config (`expo.ios.bundleIdentifier`) or passed to the plugin `withBundleIdentifier`.'\n      );\n      await setBundleIdentifierForPbxproj(config.modRequest.projectRoot, bundleId!);\n      return config;\n    },\n  ]);\n};\n\nfunction getBundleIdentifier(config: Pick<ExpoConfig, 'ios'>): string | null {\n  return config.ios?.bundleIdentifier ?? null;\n}\n\n/**\n * In Turtle v1 we set the bundleIdentifier directly on Info.plist rather\n * than in pbxproj\n */\nfunction setBundleIdentifier(config: ExpoConfig, infoPlist: InfoPlist): InfoPlist {\n  const bundleIdentifier = getBundleIdentifier(config);\n\n  if (!bundleIdentifier) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleIdentifier: bundleIdentifier,\n  };\n}\n\n/**\n * Gets the bundle identifier defined in the Xcode project found in the project directory.\n *\n * A bundle identifier is stored as a value in XCBuildConfiguration entry.\n * Those entries exist for every pair (build target, build configuration).\n * Unless target name is passed, the first target defined in the pbxproj is used\n * (to keep compatibility with the inaccurate legacy implementation of this function).\n * The build configuration is usually 'Release' or 'Debug'. However, it could be any arbitrary string.\n * Defaults to 'Release'.\n *\n * @param {string} projectRoot Path to project root containing the ios directory\n * @param {string} targetName Target name\n * @param {string} buildConfiguration Build configuration. Defaults to 'Release'.\n * @returns {string | null} bundle identifier of the Xcode project or null if the project is not configured\n */\nfunction getBundleIdentifierFromPbxproj(\n  projectRoot: string,\n  {\n    targetName,\n    buildConfiguration = 'Release',\n  }: { targetName?: string; buildConfiguration?: string } = {}\n): string | null {\n  let pbxprojPath: string;\n  try {\n    pbxprojPath = getPBXProjectPath(projectRoot);\n  } catch {\n    return null;\n  }\n  const project = xcode.project(pbxprojPath);\n  project.parseSync();\n\n  const xcBuildConfiguration = getXCBuildConfigurationFromPbxproj(project, {\n    targetName,\n    buildConfiguration,\n  });\n  if (!xcBuildConfiguration) {\n    return null;\n  }\n  return getProductBundleIdentifierFromBuildConfiguration(xcBuildConfiguration);\n}\n\nfunction getProductBundleIdentifierFromBuildConfiguration(\n  xcBuildConfiguration: XCBuildConfiguration\n): string | null {\n  const bundleIdentifierRaw = xcBuildConfiguration.buildSettings.PRODUCT_BUNDLE_IDENTIFIER;\n  if (bundleIdentifierRaw) {\n    const bundleIdentifier = trimQuotes(bundleIdentifierRaw);\n    // it's possible to use interpolation for the bundle identifier\n    // the most common case is when the last part of the id is set to `$(PRODUCT_NAME:rfc1034identifier)`\n    // in this case, PRODUCT_NAME should be replaced with its value\n    // the `rfc1034identifier` modifier replaces all non-alphanumeric characters with dashes\n    const bundleIdentifierParts = bundleIdentifier.split('.');\n    if (\n      bundleIdentifierParts[bundleIdentifierParts.length - 1] ===\n        '$(PRODUCT_NAME:rfc1034identifier)' &&\n      xcBuildConfiguration.buildSettings.PRODUCT_NAME\n    ) {\n      bundleIdentifierParts[bundleIdentifierParts.length - 1] =\n        xcBuildConfiguration.buildSettings.PRODUCT_NAME.replace(/[^a-zA-Z0-9]/g, '-');\n    }\n    return bundleIdentifierParts.join('.');\n  } else {\n    return null;\n  }\n}\n\n/**\n * Updates the bundle identifier for a given pbxproj\n *\n * @param {string} pbxprojPath Path to pbxproj file\n * @param {string} bundleIdentifier Bundle identifier to set in the pbxproj\n * @param {boolean} [updateProductName=true]  Whether to update PRODUCT_NAME\n */\nfunction updateBundleIdentifierForPbxproj(\n  pbxprojPath: string,\n  bundleIdentifier: string,\n  updateProductName: boolean = true\n): void {\n  const project = xcode.project(pbxprojPath);\n  project.parseSync();\n\n  const [, nativeTarget] = findFirstNativeTarget(project);\n\n  getBuildConfigurationsForListId(project, nativeTarget.buildConfigurationList).forEach(\n    ([, item]: ConfigurationSectionEntry) => {\n      if (item.buildSettings.PRODUCT_BUNDLE_IDENTIFIER === bundleIdentifier) {\n        return;\n      }\n\n      item.buildSettings.PRODUCT_BUNDLE_IDENTIFIER = `\"${bundleIdentifier}\"`;\n\n      if (updateProductName) {\n        const productName = bundleIdentifier.split('.').pop();\n        if (!productName?.includes('$')) {\n          item.buildSettings.PRODUCT_NAME = productName;\n        }\n      }\n    }\n  );\n  fs.writeFileSync(pbxprojPath, project.writeSync());\n}\n\n/**\n * Updates the bundle identifier for pbx projects inside the ios directory of the given project root\n *\n * @param {string} projectRoot Path to project root containing the ios directory\n * @param {string} bundleIdentifier Desired bundle identifier\n * @param {boolean} [updateProductName=true]  Whether to update PRODUCT_NAME\n */\nfunction setBundleIdentifierForPbxproj(\n  projectRoot: string,\n  bundleIdentifier: string,\n  updateProductName: boolean = true\n): void {\n  // Get all pbx projects in the ${projectRoot}/ios directory\n  let pbxprojPaths: string[] = [];\n  try {\n    pbxprojPaths = getAllPBXProjectPaths(projectRoot);\n  } catch {}\n\n  for (const pbxprojPath of pbxprojPaths) {\n    updateBundleIdentifierForPbxproj(pbxprojPath, bundleIdentifier, updateProductName);\n  }\n}\n\n/**\n * Reset bundle identifier field in Info.plist to use PRODUCT_BUNDLE_IDENTIFIER, as recommended by Apple.\n */\n\nconst defaultBundleId = '$(PRODUCT_BUNDLE_IDENTIFIER)';\n\nfunction resetAllPlistBundleIdentifiers(projectRoot: string): void {\n  const infoPlistPaths = getAllInfoPlistPaths(projectRoot);\n\n  for (const plistPath of infoPlistPaths) {\n    resetPlistBundleIdentifier(plistPath);\n  }\n}\n\nfunction resetPlistBundleIdentifier(plistPath: string): void {\n  const rawPlist = fs.readFileSync(plistPath, 'utf8');\n  const plistObject = plist.parse(rawPlist) as PlistObject;\n\n  if (plistObject.CFBundleIdentifier) {\n    if (plistObject.CFBundleIdentifier === defaultBundleId) return;\n\n    // attempt to match default Info.plist format\n    const format = { pretty: true, indent: `\\t` };\n\n    const xml = plist.build(\n      {\n        ...plistObject,\n        CFBundleIdentifier: defaultBundleId,\n      },\n      format\n    );\n\n    if (xml !== rawPlist) {\n      fs.writeFileSync(plistPath, xml);\n    }\n  }\n}\n\nexport {\n  getBundleIdentifier,\n  setBundleIdentifier,\n  getBundleIdentifierFromPbxproj,\n  updateBundleIdentifierForPbxproj,\n  setBundleIdentifierForPbxproj,\n  resetAllPlistBundleIdentifiers,\n  resetPlistBundleIdentifier,\n};\n"], "mappings": ";;;;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,OAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,OAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,MAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,WAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,UAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,QAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,OAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4C,SAAAC,uBAAAU,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErC,MAAMG,oBAAiE,GAAGA,CAC/EC,MAAM,EACN;EAAEC;AAAiB,CAAC,KACjB;EACH,OAAO,IAAAC,oCAAgB,EAACF,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAAA,IAAAG,WAAA;IAChB,MAAMC,QAAQ,GAAGH,gBAAgB,aAAhBA,gBAAgB,cAAhBA,gBAAgB,IAAAE,WAAA,GAAIH,MAAM,CAACK,GAAG,cAAAF,WAAA,uBAAVA,WAAA,CAAYF,gBAAgB;IACjE,IAAAK,iBAAM,EACJF,QAAQ,EACR,oIAAoI,CACrI;IACD,MAAMG,6BAA6B,CAACP,MAAM,CAACQ,UAAU,CAACC,WAAW,EAAEL,QAAQ,CAAE;IAC7E,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACU,OAAA,CAAAX,oBAAA,GAAAA,oBAAA;AAEF,SAASY,mBAAmBA,CAACX,MAA+B,EAAiB;EAAA,IAAAY,qBAAA,EAAAC,YAAA;EAC3E,QAAAD,qBAAA,IAAAC,YAAA,GAAOb,MAAM,CAACK,GAAG,cAAAQ,YAAA,uBAAVA,YAAA,CAAYZ,gBAAgB,cAAAW,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC7C;;AAEA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACd,MAAkB,EAAEe,SAAoB,EAAa;EAChF,MAAMd,gBAAgB,GAAGU,mBAAmB,CAACX,MAAM,CAAC;EAEpD,IAAI,CAACC,gBAAgB,EAAE;IACrB,OAAOc,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZC,kBAAkB,EAAEf;EACtB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,8BAA8BA,CACrCR,WAAmB,EACnB;EACES,UAAU;EACVC,kBAAkB,GAAG;AAC+B,CAAC,GAAG,CAAC,CAAC,EAC7C;EACf,IAAIC,WAAmB;EACvB,IAAI;IACFA,WAAW,GAAG,IAAAC,0BAAiB,EAACZ,WAAW,CAAC;EAC9C,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;EACA,MAAMa,OAAO,GAAGC,gBAAK,CAACD,OAAO,CAACF,WAAW,CAAC;EAC1CE,OAAO,CAACE,SAAS,EAAE;EAEnB,MAAMC,oBAAoB,GAAG,IAAAC,4CAAkC,EAACJ,OAAO,EAAE;IACvEJ,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI,CAACM,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb;EACA,OAAOE,gDAAgD,CAACF,oBAAoB,CAAC;AAC/E;AAEA,SAASE,gDAAgDA,CACvDF,oBAA0C,EAC3B;EACf,MAAMG,mBAAmB,GAAGH,oBAAoB,CAACI,aAAa,CAACC,yBAAyB;EACxF,IAAIF,mBAAmB,EAAE;IACvB,MAAM3B,gBAAgB,GAAG,IAAA8B,oBAAU,EAACH,mBAAmB,CAAC;IACxD;IACA;IACA;IACA;IACA,MAAMI,qBAAqB,GAAG/B,gBAAgB,CAACgC,KAAK,CAAC,GAAG,CAAC;IACzD,IACED,qBAAqB,CAACA,qBAAqB,CAACE,MAAM,GAAG,CAAC,CAAC,KACrD,mCAAmC,IACrCT,oBAAoB,CAACI,aAAa,CAACM,YAAY,EAC/C;MACAH,qBAAqB,CAACA,qBAAqB,CAACE,MAAM,GAAG,CAAC,CAAC,GACrDT,oBAAoB,CAACI,aAAa,CAACM,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;IACjF;IACA,OAAOJ,qBAAqB,CAACK,IAAI,CAAC,GAAG,CAAC;EACxC,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgCA,CACvClB,WAAmB,EACnBnB,gBAAwB,EACxBsC,iBAA0B,GAAG,IAAI,EAC3B;EACN,MAAMjB,OAAO,GAAGC,gBAAK,CAACD,OAAO,CAACF,WAAW,CAAC;EAC1CE,OAAO,CAACE,SAAS,EAAE;EAEnB,MAAM,GAAGgB,YAAY,CAAC,GAAG,IAAAC,+BAAqB,EAACnB,OAAO,CAAC;EAEvD,IAAAoB,4CAA+B,EAACpB,OAAO,EAAEkB,YAAY,CAACG,sBAAsB,CAAC,CAACC,OAAO,CACnF,CAAC,GAAGC,IAAI,CAA4B,KAAK;IACvC,IAAIA,IAAI,CAAChB,aAAa,CAACC,yBAAyB,KAAK7B,gBAAgB,EAAE;MACrE;IACF;IAEA4C,IAAI,CAAChB,aAAa,CAACC,yBAAyB,GAAI,IAAG7B,gBAAiB,GAAE;IAEtE,IAAIsC,iBAAiB,EAAE;MACrB,MAAMO,WAAW,GAAG7C,gBAAgB,CAACgC,KAAK,CAAC,GAAG,CAAC,CAACc,GAAG,EAAE;MACrD,IAAI,EAACD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,QAAQ,CAAC,GAAG,CAAC,GAAE;QAC/BH,IAAI,CAAChB,aAAa,CAACM,YAAY,GAAGW,WAAW;MAC/C;IACF;EACF,CAAC,CACF;EACDG,aAAE,CAACC,aAAa,CAAC9B,WAAW,EAAEE,OAAO,CAAC6B,SAAS,EAAE,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5C,6BAA6BA,CACpCE,WAAmB,EACnBR,gBAAwB,EACxBsC,iBAA0B,GAAG,IAAI,EAC3B;EACN;EACA,IAAIa,YAAsB,GAAG,EAAE;EAC/B,IAAI;IACFA,YAAY,GAAG,IAAAC,8BAAqB,EAAC5C,WAAW,CAAC;EACnD,CAAC,CAAC,MAAM,CAAC;EAET,KAAK,MAAMW,WAAW,IAAIgC,YAAY,EAAE;IACtCd,gCAAgC,CAAClB,WAAW,EAAEnB,gBAAgB,EAAEsC,iBAAiB,CAAC;EACpF;AACF;;AAEA;AACA;AACA;;AAEA,MAAMe,eAAe,GAAG,8BAA8B;AAEtD,SAASC,8BAA8BA,CAAC9C,WAAmB,EAAQ;EACjE,MAAM+C,cAAc,GAAG,IAAAC,6BAAoB,EAAChD,WAAW,CAAC;EAExD,KAAK,MAAMiD,SAAS,IAAIF,cAAc,EAAE;IACtCG,0BAA0B,CAACD,SAAS,CAAC;EACvC;AACF;AAEA,SAASC,0BAA0BA,CAACD,SAAiB,EAAQ;EAC3D,MAAME,QAAQ,GAAGX,aAAE,CAACY,YAAY,CAACH,SAAS,EAAE,MAAM,CAAC;EACnD,MAAMI,WAAW,GAAGC,gBAAK,CAACC,KAAK,CAACJ,QAAQ,CAAgB;EAExD,IAAIE,WAAW,CAAC9C,kBAAkB,EAAE;IAClC,IAAI8C,WAAW,CAAC9C,kBAAkB,KAAKsC,eAAe,EAAE;;IAExD;IACA,MAAMW,MAAM,GAAG;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAG;IAAI,CAAC;IAE7C,MAAMC,GAAG,GAAGL,gBAAK,CAACM,KAAK,CACrB;MACE,GAAGP,WAAW;MACd9C,kBAAkB,EAAEsC;IACtB,CAAC,EACDW,MAAM,CACP;IAED,IAAIG,GAAG,KAAKR,QAAQ,EAAE;MACpBX,aAAE,CAACC,aAAa,CAACQ,SAAS,EAAEU,GAAG,CAAC;IAClC;EACF;AACF"}