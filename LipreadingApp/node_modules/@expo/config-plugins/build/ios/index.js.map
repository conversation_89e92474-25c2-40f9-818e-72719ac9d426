{"version": 3, "file": "index.js", "names": ["Bitcode", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "BuildProperties", "BuildScheme", "BundleIdentifier", "DeviceFamily", "Entitlements", "Google", "_IosConfig", "Locales", "Maps", "Name", "Orientation", "Paths", "Permissions", "ProvisioningProfile", "RequiresFullScreen", "Scheme", "Swift", "Target", "Updates", "UsesNonExemptEncryption", "Version", "XcodeProjectFile", "XcodeUtils", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set"], "sources": ["../../src/ios/index.ts"], "sourcesContent": ["import * as Bitcode from './Bitcode';\nimport * as BuildProperties from './BuildProperties';\nimport * as BuildScheme from './BuildScheme';\nimport * as BundleIdentifier from './BundleIdentifier';\nimport * as DeviceFamily from './DeviceFamily';\nimport * as Entitlements from './Entitlements';\nimport * as Google from './Google';\nimport { ExpoPlist, InfoPlist } from './IosConfig.types';\nimport * as Locales from './Locales';\nimport * as Maps from './Maps';\nimport * as Name from './Name';\nimport * as Orientation from './Orientation';\nimport * as Paths from './Paths';\nimport * as Permissions from './Permissions';\nimport * as ProvisioningProfile from './ProvisioningProfile';\nimport * as RequiresFullScreen from './RequiresFullScreen';\nimport * as Scheme from './Scheme';\nimport * as Swift from './Swift';\nimport * as Target from './Target';\nimport * as Updates from './Updates';\nimport * as UsesNonExemptEncryption from './UsesNonExemptEncryption';\nimport * as Version from './Version';\nimport * as XcodeProjectFile from './XcodeProjectFile';\nimport * as XcodeUtils from './utils/Xcodeproj';\n\n// We can change this to export * as X with TypeScript 3.8+\n// https://devblogs.microsoft.com/typescript/announcing-typescript-3-8-beta/#export-star-as-namespace-syntax\n// .. but we should wait for this to be the default VSCode version.\nexport { InfoPlist, ExpoPlist, Entitlements, Paths, Permissions, XcodeUtils };\n\nexport {\n  Bitcode,\n  BundleIdentifier,\n  BuildProperties,\n  BuildScheme,\n  DeviceFamily,\n  Google,\n  Maps,\n  Locales,\n  Name,\n  Orientation,\n  ProvisioningProfile,\n  RequiresFullScreen,\n  Scheme,\n  Swift,\n  Target,\n  Updates,\n  UsesNonExemptEncryption,\n  Version,\n  XcodeProjectFile,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,OAAA;EAAA;AAAA;AACrC,SAAAS,gBAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,eAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,eAAA;EAAA;AAAA;AACrD,SAAAC,YAAA;EAAA,MAAAT,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAO,WAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAE,WAAA;EAAA;AAAA;AAC7C,SAAAC,iBAAA;EAAA,MAAAV,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAQ,gBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAG,gBAAA;EAAA;AAAA;AACvD,SAAAC,aAAA;EAAA,MAAAX,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAS,YAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAI,YAAA;EAAA;AAAA;AAC/C,SAAAC,aAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,YAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,YAAA;EAAA;AAAA;AAC/C,SAAAC,OAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,MAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,MAAA;EAAA;AAAA;AACnC,SAAAC,WAAA;EAAA,MAAAd,IAAA,GAAAE,OAAA;EAAAY,UAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAe,QAAA;EAAA,MAAAf,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAa,OAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAQ,OAAA;EAAA;AAAA;AACrC,SAAAC,KAAA;EAAA,MAAAhB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAc,IAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAS,IAAA;EAAA;AAAA;AAC/B,SAAAC,KAAA;EAAA,MAAAjB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAe,IAAA,YAAAA,CAAA;IAAA,OAAAjB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAU,IAAA;EAAA;AAAA;AAC/B,SAAAC,YAAA;EAAA,MAAAlB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAgB,WAAA,YAAAA,CAAA;IAAA,OAAAlB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAW,WAAA;EAAA;AAAA;AAC7C,SAAAC,MAAA;EAAA,MAAAnB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAiB,KAAA,YAAAA,CAAA;IAAA,OAAAnB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAY,KAAA;EAAA;AAAA;AACjC,SAAAC,YAAA;EAAA,MAAApB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAkB,WAAA,YAAAA,CAAA;IAAA,OAAApB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAa,WAAA;EAAA;AAAA;AAC7C,SAAAC,oBAAA;EAAA,MAAArB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAmB,mBAAA,YAAAA,CAAA;IAAA,OAAArB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAc,mBAAA;EAAA;AAAA;AAC7D,SAAAC,mBAAA;EAAA,MAAAtB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAoB,kBAAA,YAAAA,CAAA;IAAA,OAAAtB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAe,kBAAA;EAAA;AAAA;AAC3D,SAAAC,OAAA;EAAA,MAAAvB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAqB,MAAA,YAAAA,CAAA;IAAA,OAAAvB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAgB,MAAA;EAAA;AAAA;AACnC,SAAAC,MAAA;EAAA,MAAAxB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAsB,KAAA,YAAAA,CAAA;IAAA,OAAAxB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAiB,KAAA;EAAA;AAAA;AACjC,SAAAC,OAAA;EAAA,MAAAzB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAuB,MAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAkB,MAAA;EAAA;AAAA;AACnC,SAAAC,QAAA;EAAA,MAAA1B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAwB,OAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAmB,OAAA;EAAA;AAAA;AACrC,SAAAC,wBAAA;EAAA,MAAA3B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAyB,uBAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqEG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAoB,uBAAA;EAAA;AAAA;AACrE,SAAAC,QAAA;EAAA,MAAA5B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA0B,OAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAqB,OAAA;EAAA;AAAA;AACrC,SAAAC,iBAAA;EAAA,MAAA7B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA2B,gBAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAsB,gBAAA;EAAA;AAAA;AACvD,SAAAC,WAAA;EAAA,MAAA9B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA4B,UAAA,YAAAA,CAAA;IAAA,OAAA9B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAuB,UAAA;EAAA;AAAA;AAAA,SAAAC,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAA/B,wBAAAmC,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAhC,GAAA,CAAA6B,GAAA,SAAAK,MAAA,WAAAC,qBAAA,GAAAvC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAwC,wBAAA,WAAAC,GAAA,IAAAR,GAAA,QAAAQ,GAAA,kBAAAzC,MAAA,CAAA0C,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAX,GAAA,EAAAQ,GAAA,SAAAI,IAAA,GAAAN,qBAAA,GAAAvC,MAAA,CAAAwC,wBAAA,CAAAP,GAAA,EAAAQ,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAzC,GAAA,IAAAyC,IAAA,CAAAC,GAAA,KAAA9C,MAAA,CAAAC,cAAA,CAAAqC,MAAA,EAAAG,GAAA,EAAAI,IAAA,YAAAP,MAAA,CAAAG,GAAA,IAAAR,GAAA,CAAAQ,GAAA,SAAAH,MAAA,CAAAH,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAU,GAAA,CAAAb,GAAA,EAAAK,MAAA,YAAAA,MAAA"}