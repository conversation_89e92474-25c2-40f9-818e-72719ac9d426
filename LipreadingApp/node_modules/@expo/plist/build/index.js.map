{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAEA,mCAAgC;AAChC,mCAAgC;AAahC,kBAAe,EAAE,KAAK,EAAL,aAAK,EAAE,KAAK,EAAL,aAAK,EAAE,CAAC", "sourcesContent": ["import { XMLToStringOptions } from 'xmlbuilder';\n\nimport { build } from './build';\nimport { parse } from './parse';\n\n// PlistValue\nexport type PlistValue = string | number | boolean | Date | Buffer | PlistObject | PlistArray;\nexport interface PlistObject {\n  readonly [x: string]: PlistValue;\n}\nexport interface PlistArray extends ReadonlyArray<PlistValue> {}\n\n// PlistBuildOptions\n// The instance of this type is passed to 'xmlbuilder' module as it is.\nexport type PlistBuildOptions = XMLToStringOptions;\n\nexport default { parse, build };\n"]}