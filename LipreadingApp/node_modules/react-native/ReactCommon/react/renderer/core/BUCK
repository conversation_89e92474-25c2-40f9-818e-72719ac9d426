load("@fbsource//tools/build_defs:fb_xplat_cxx_binary.bzl", "fb_xplat_cxx_binary")
load(
    "//tools/build_defs/oss:rn_defs.bzl",
    "ANDROID",
    "APPLE",
    "CXX",
    "fb_xplat_cxx_test",
    "get_apple_compiler_flags",
    "get_apple_inspector_flags",
    "get_preprocessor_flags_for_build_mode",
    "react_native_xplat_target",
    "rn_xplat_cxx_library",
    "subdir_glob",
)

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "core",
    srcs = glob(
        ["**/*.cpp"],
        exclude = glob(["tests/**/*.cpp"]),
    ),
    headers = glob(
        ["**/*.h"],
        exclude = glob(["tests/**/*.h"]),
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        prefix = "react/renderer/core",
    ),
    compiler_flags_pedantic = True,
    fbandroid_tests_override = [],
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    tests = [":tests"],
    visibility = ["PUBLIC"],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/folly:dynamic",
        "//xplat/folly:hash",
        "//xplat/folly:likely",
        "//xplat/jsi:JSIDynamic",
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/utils:utils"),
        react_native_xplat_target("react/renderer/debug:debug"),
        react_native_xplat_target("react/renderer/graphics:graphics"),
        react_native_xplat_target("cxxreact:bridge"),
    ],
    exported_deps = [
        "//xplat/jsi:jsi",
    ],
)

fb_xplat_cxx_test(
    name = "tests",
    srcs = glob(["tests/*.cpp"]),
    headers = glob(["tests/*.h"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
    ],
    contacts = ["<EMAIL>"],
    platforms = (APPLE, CXX),
    deps = [
        ":core",
        "//xplat/hermes/API:HermesAPI",
        "//xplat/third-party/gmock:gtest",
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("react/renderer/components/scrollview:scrollview"),
        react_native_xplat_target("react/renderer/components/text:text"),
        react_native_xplat_target("react/renderer/element:element"),
        react_native_xplat_target("react/renderer/components/view:view"),
        react_native_xplat_target("cxxreact:bridge"),
    ],
)

fb_xplat_cxx_binary(
    name = "benchmarks",
    srcs = glob(["tests/benchmarks/*.cpp"]),
    compiler_flags = [
        "-fexceptions",
        "-frtti",
        "-std=c++17",
        "-Wall",
        "-Wno-unused-variable",
    ],
    contacts = ["<EMAIL>"],
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    platforms = (ANDROID, APPLE, CXX),
    visibility = ["PUBLIC"],
    deps = [
        "//xplat/third-party/benchmark:benchmark",
        react_native_xplat_target("react/utils:utils"),
        react_native_xplat_target("react/renderer/components/view:view"),
        ":core",
    ],
)
