load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "instrumentation",
    srcs = ["HermesMemoryDumper.java"],
    autoglob = False,
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
)

rn_android_library(
    name = "hermes_samplingprofiler",
    srcs = ["HermesSamplingProfiler.java"],
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = ["PUBLIC"],
    deps = [
        react_native_dep("java/com/facebook/proguard/annotations:annotations"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("libraries/fbjni:java"),
        react_native_target("jni/react/hermes/instrumentation:jni_hermes_samplingprofiler"),
    ],
)
