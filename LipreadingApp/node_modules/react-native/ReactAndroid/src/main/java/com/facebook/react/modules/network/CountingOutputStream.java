/*
 * Copyright (C) 2007 The Guava Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.facebook.react.modules.network;

import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * An OutputStream that counts the number of bytes written.
 *
 * <AUTHOR>
 * @since 1.0
 */
public class CountingOutputStream extends FilterOutputStream {

  private long mCount;

  /**
   * Constructs a new {@code FilterOutputStream} with {@code out} as its target stream.
   *
   * @param out the target stream that this stream writes to.
   */
  public CountingOutputStream(OutputStream out) {
    super(out);
    mCount = 0;
  }

  /** Returns the number of bytes written. */
  public long getCount() {
    return mCount;
  }

  @Override
  public void write(byte[] b, int off, int len) throws IOException {
    out.write(b, off, len);
    mCount += len;
  }

  @Override
  public void write(int b) throws IOException {
    out.write(b);
    mCount++;
  }

  // Overriding close() because FilterOutputStream's close() method pre-JDK8 has bad behavior:
  // it silently ignores any exception thrown by flush(). Instead, just close the delegate stream.
  // It should flush itself if necessary.
  @Override
  public void close() throws IOException {
    out.close();
  }
}
