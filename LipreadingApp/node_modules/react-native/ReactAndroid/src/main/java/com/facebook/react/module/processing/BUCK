load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_java_annotation_processor", "rn_java_library")

rn_java_annotation_processor(
    name = "processing",
    does_not_affect_abi = True,
    processor_class = "com.facebook.react.module.processing.ReactModuleSpecProcessor",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        ":processing-lib",
    ],
)

rn_java_library(
    name = "processing-lib",
    srcs = glob(["*.java"]),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    source = "8",
    target = "8",
    deps = [
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/javapoet:javapoet"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/module/model:model"),
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
    ],
)
