load("//tools/build_defs/oss:rn_defs.bzl", "FBJNI_TARGET", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "mapbuffer",
    srcs = glob([
        "*.java",
        "*.kt",
    ]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "KOTLIN",
    pure_kotlin = False,
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        FBJNI_TARGET,
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_target("jni/react/mapbuffer:jni"),
        react_native_dep("libraries/fbjni:java"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        # dependencies used for systraces
        react_native_dep("java/com/facebook/systrace:systrace"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
    ],
)
