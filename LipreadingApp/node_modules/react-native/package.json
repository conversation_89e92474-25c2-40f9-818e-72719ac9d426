{"name": "react-native", "version": "0.72.6", "bin": "./cli.js", "description": "A framework for building native apps using React", "license": "MIT", "repository": "github:facebook/react-native", "engines": {"node": ">=16"}, "types": "types", "jest-junit": {"outputDirectory": "reports/junit", "outputName": "js-test-results.xml"}, "files": ["android", "build.gradle.kts", "cli.js", "flow", "flow-typed", "gradle.properties", "index.js", "interface.js", "jest-preset.js", "jest", "Libraries", "LICENSE", "local-cli", "React-Core.podspec", "react-native.config.js", "React.podspec", "React", "ReactAndroid", "ReactCommon", "README.md", "rn-get-polyfills.js", "scripts/compose-source-maps.js", "scripts/find-node-for-xcode.sh", "scripts/generate-codegen-artifacts.js", "scripts/generate-provider-cli.js", "scripts/generate-specs-cli.js", "scripts/codegen", "!scripts/codegen/__tests__", "!scripts/codegen/__test_fixtures__", "scripts/hermes/hermes-utils.js", "scripts/hermes/prepare-hermes-for-build.js", "scripts/ios-configure-glog.sh", "scripts/xcode/with-environment.sh", "scripts/launchPackager.bat", "scripts/launchPackager.command", "scripts/native_modules.rb", "scripts/node-binary.sh", "scripts/packager.sh", "scripts/packager-reporter.js", "scripts/react_native_pods_utils/script_phases.rb", "scripts/react_native_pods_utils/script_phases.sh", "scripts/react_native_pods.rb", "scripts/cocoapods", "!scripts/cocoapods/__tests__", "scripts/react-native-xcode.sh", "sdks/.hermesversion", "sdks/hermes-engine", "sdks/hermesc", "settings.gradle.kts", "template.config.js", "template", "!template/node_modules", "!template/package-lock.json", "!template/yarn.lock", "third-party-podspecs", "types"], "scripts": {"prepack": "cp ../../README.md ."}, "peerDependencies": {"react": "18.2.0"}, "dependencies": {"@jest/create-cache-key-function": "^29.2.1", "@react-native-community/cli": "11.3.7", "@react-native-community/cli-platform-android": "11.3.7", "@react-native-community/cli-platform-ios": "11.3.7", "@react-native/assets-registry": "^0.72.0", "@react-native/codegen": "^0.72.7", "@react-native/gradle-plugin": "^0.72.11", "@react-native/js-polyfills": "^0.72.1", "@react-native/normalize-colors": "^0.72.0", "@react-native/virtualized-lists": "^0.72.8", "abort-controller": "^3.0.0", "anser": "^1.4.9", "base64-js": "^1.1.2", "deprecated-react-native-prop-types": "4.1.0", "event-target-shim": "^5.0.1", "flow-enums-runtime": "^0.0.5", "invariant": "^2.2.4", "jest-environment-node": "^29.2.1", "jsc-android": "^250231.0.0", "memoize-one": "^5.0.0", "metro-runtime": "0.76.8", "metro-source-map": "0.76.8", "mkdirp": "^0.5.1", "nullthrows": "^1.1.1", "pretty-format": "^26.5.2", "promise": "^8.3.0", "react-devtools-core": "^4.27.2", "react-refresh": "^0.4.0", "react-shallow-renderer": "^16.15.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.24.0-canary-efb381bbf-20230505", "stacktrace-parser": "^0.1.10", "use-sync-external-store": "^1.0.0", "whatwg-fetch": "^3.0.0", "ws": "^6.2.2", "yargs": "^17.6.2"}, "codegenConfig": {"libraries": [{"name": "FBReactNativeSpec", "type": "modules", "ios": {}, "android": {}, "jsSrcsDir": "Libraries"}, {"name": "rncore", "type": "components", "ios": {}, "android": {}, "jsSrcsDir": "Libraries"}]}}