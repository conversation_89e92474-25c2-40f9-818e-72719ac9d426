{"name": "blueimp-md5", "version": "2.19.0", "title": "JavaScript MD5", "description": "JavaScript MD5 implementation. Compatible with server-side environments like Node.js, module loaders like RequireJS, Browserify or webpack and all web browsers.", "keywords": ["javascript", "md5"], "homepage": "https://github.com/blueimp/JavaScript-MD5", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "contributors": [{"name": "<PERSON>", "url": "http://pajhome.org.uk/crypt/md5"}], "repository": {"type": "git", "url": "git://github.com/blueimp/JavaScript-MD5.git"}, "license": "MIT", "devDependencies": {"chai": "4", "eslint": "7", "eslint-config-blueimp": "2", "eslint-config-prettier": "8", "eslint-plugin-jsdoc": "36", "eslint-plugin-prettier": "4", "mocha": "9", "prettier": "2", "uglify-js": "3"}, "eslintConfig": {"extends": ["blueimp", "plugin:jsdoc/recommended", "plugin:prettier/recommended"], "env": {"browser": true, "node": true}}, "eslintIgnore": ["js/*.min.js", "test/vendor"], "prettier": {"arrowParens": "avoid", "proseWrap": "always", "semi": false, "singleQuote": true, "trailingComma": "none"}, "scripts": {"lint": "eslint .", "unit": "mocha", "test": "npm run lint && npm run unit", "prebuild": "bin/sync-vendor-libs.sh", "build": "cd js && uglifyjs md5.js -c -m -o md5.min.js --source-map url=md5.min.js.map", "preversion": "npm test", "version": "npm run build && git add -A js", "postversion": "git push --tags origin master master:gh-pages && npm publish"}, "files": ["js/*.js", "js/*.js.map"], "main": "js/md5.js"}