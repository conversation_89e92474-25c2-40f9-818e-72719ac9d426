{"name": "wonka", "description": "A fast push & pull stream library for Reason/OCaml, Flow, and TypeScript", "version": "4.0.15", "author": "<PERSON> <<EMAIL>>", "source": "./src/Wonka.ts", "main": "./dist/wonka", "module": "./dist/wonka.mjs", "types": "./dist/types/src/Wonka.d.ts", "exports": {".": {"import": "./dist/wonka.mjs", "require": "./dist/wonka.js", "types": "./dist/types/src/Wonka.d.ts", "source": "./src/Wonka.ts"}, "./package.json": "./package.json", "./": "./"}, "sideEffects": false, "files": ["src", "dist", "docs/*.md", "*.md", "index.js.flow", "dune-project", "wonka.opam", "esy.json", "bsconfig.json"], "scripts": {"docs:dev": "gatsby develop", "docs:build": "gatsby build", "check:ts": "tsc --noEmit", "check:flow": "flow focus-check ./src/helpers/Wonka_flow_test.js", "check": "run-s check:ts check:flow", "bs:clean": "bsb -clean-world", "bs:build": "bsb -make-world", "bs:watch": "bsb -make-world -w", "test": "jest", "refmt": "bsrefmt --in-place **/**/*.{re,rei}", "flowgen": "./scripts/generate-flow-files.js", "bundle:clean": "rimraf dist node_modules/.cache", "bundle": "rollup -c rollup.config.js", "clean": "run-s bs:clean bundle:clean", "build": "run-s bs:build bundle flowgen", "prepublishOnly": "run-s clean build check test"}, "keywords": ["wonka", "reason", "bucklescript", "callbag", "callback", "observable", "iterable", "stream"], "repository": "https://github.com/kitten/wonka", "homepage": "https://wonka.kitten.sh", "bugs": {"url": "https://github.com/kitten/wonka/issues"}, "license": "MIT", "dependencies": {}, "devDependencies": {"@ampproject/rollup-plugin-closure-compiler": "^0.25.2", "@babel/core": "^7.10.2", "@babel/plugin-syntax-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1", "@babel/preset-env": "^7.10.2", "@glennsl/bs-jest": "^0.5.1", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^12.0.0", "@rollup/plugin-node-resolve": "^8.0.0", "@types/jest": "^25.2.3", "@types/zen-observable": "^0.8.0", "babel-plugin-closure-elimination": "^1.3.1", "bs-platform": "^7.3.2", "callbag-from-iter": "^1.2.0", "callbag-iterate": "^1.0.0", "callbag-take": "^1.5.0", "coveralls": "^3.1.0", "flow-bin": "^0.125.1", "flowgen": "^1.10.0", "gatsby": "^2.18.17", "gatsby-plugin-netlify": "^2.1.30", "gatsby-theme-docs-system": "^0.2.2", "gentype": "^3.23.0", "globby": "^11.0.0", "husky": "^4.2.5", "lint-staged": "^10.2.7", "npm-run-all": "^4.1.5", "prettier": "^2.0.5", "react": "^16.13.1", "react-dom": "^16.13.1", "rimraf": "^3.0.2", "rollup": "^2.12.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-prettier": "^2.1.0", "rollup-plugin-terser": "^6.1.0", "rollup-plugin-typescript2": "^0.27.1", "ts-jest": "^26.1.0", "typescript": "^3.9.2", "zen-observable": "^0.8.15"}, "lint-staged": {"*.{d.ts,js}": ["prettier --write"], "*.{re,rei}": ["bsrefmt --in-place"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "prettier": {"singleQuote": true, "printWidth": 100}, "jest": {"testEnvironment": "node", "testRegex": "(src/.*(\\.|/)(test|spec))\\.(t|j)sx?$", "moduleFileExtensions": ["js", "ts", "tsx"], "transform": {"\\.jsx?$": "<rootDir>/scripts/jest-transform-esm.js", "\\.tsx?$": "ts-jest"}, "transformIgnorePatterns": ["/node_modules/(?!bs-platform)"], "collectCoverageFrom": ["!<rootDir>/src/wonka.{ts,bs.js}", "!<rootDir>/src/wonka_types.{ts,bs.js}", "!<rootDir>/src/include/**", "!<rootDir>/src/helpers/**", "!<rootDir>/src/**/*.{shim,gen}.{ts,tsx}", "<rootDir>/src/**/*.{js,ts,tsx}"]}}