{"/Users/<USER>/projects/npm/validate-npm-package-name/index.js": {"path": "/Users/<USER>/projects/npm/validate-npm-package-name/index.js", "statementMap": {"0": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 68}}, "1": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 34}}, "2": {"start": {"line": 5, "column": 16}, "end": {"line": 8, "column": 1}}, "3": {"start": {"line": 10, "column": 15}, "end": {"line": 91, "column": 1}}, "4": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 19}}, "5": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 17}}, "6": {"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 3}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 38}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 33}}, "9": {"start": {"line": 19, "column": 2}, "end": {"line": 22, "column": 3}}, "10": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 43}}, "11": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 33}}, "12": {"start": {"line": 24, "column": 2}, "end": {"line": 27, "column": 3}}, "13": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 40}}, "14": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 33}}, "15": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "16": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 56}}, "17": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "18": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 50}}, "19": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "20": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 55}}, "21": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "22": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 65}}, "23": {"start": {"line": 46, "column": 2}, "end": {"line": 50, "column": 4}}, "24": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "25": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 61}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 59, "column": 4}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "28": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 55}}, "29": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "30": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 72}}, "31": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "32": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 63}}, "33": {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}, "34": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 78}}, "35": {"start": {"line": 76, "column": 2}, "end": {"line": 88, "column": 3}}, "36": {"start": {"line": 78, "column": 20}, "end": {"line": 78, "column": 52}}, "37": {"start": {"line": 79, "column": 4}, "end": {"line": 85, "column": 5}}, "38": {"start": {"line": 80, "column": 17}, "end": {"line": 80, "column": 29}}, "39": {"start": {"line": 81, "column": 16}, "end": {"line": 81, "column": 28}}, "40": {"start": {"line": 82, "column": 6}, "end": {"line": 84, "column": 7}}, "41": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 37}}, "42": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 64}}, "43": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 31}}, "44": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 52}}, "45": {"start": {"line": 95, "column": 11}, "end": {"line": 105, "column": 1}}, "46": {"start": {"line": 96, "column": 15}, "end": {"line": 101, "column": 3}}, "47": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 53}}, "48": {"start": {"line": 102, "column": 31}, "end": {"line": 102, "column": 53}}, "49": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 49}}, "50": {"start": {"line": 103, "column": 29}, "end": {"line": 103, "column": 49}}, "51": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 15}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 33}}, "loc": {"start": {"line": 10, "column": 48}, "end": {"line": 91, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 46, "column": 20}, "end": {"line": 46, "column": 21}}, "loc": {"start": {"line": 46, "column": 47}, "end": {"line": 50, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 20}}, "loc": {"start": {"line": 55, "column": 38}, "end": {"line": 59, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 95, "column": 11}, "end": {"line": 95, "column": 12}}, "loc": {"start": {"line": 95, "column": 39}, "end": {"line": 105, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 3}}, {"start": {"line": 14, "column": 2}, "end": {"line": 17, "column": 3}}]}, "1": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 22, "column": 3}}, "type": "if", "locations": [{"start": {"line": 19, "column": 2}, "end": {"line": 22, "column": 3}}, {"start": {"line": 19, "column": 2}, "end": {"line": 22, "column": 3}}]}, "2": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 27, "column": 3}}, "type": "if", "locations": [{"start": {"line": 24, "column": 2}, "end": {"line": 27, "column": 3}}, {"start": {"line": 24, "column": 2}, "end": {"line": 27, "column": 3}}]}, "3": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}, {"start": {"line": 29, "column": 2}, "end": {"line": 31, "column": 3}}]}, "4": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}, {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 3}}]}, "5": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}]}, "6": {"loc": {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {"line": 41, "column": 2}, "end": {"line": 43, "column": 3}}]}, "7": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}]}, "8": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}]}, "9": {"loc": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "type": "if", "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}]}, "10": {"loc": {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, "type": "if", "locations": [{"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}, {"start": {"line": 68, "column": 2}, "end": {"line": 70, "column": 3}}]}, "11": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}, "type": "if", "locations": [{"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}, {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 3}}]}, "12": {"loc": {"start": {"line": 76, "column": 2}, "end": {"line": 88, "column": 3}}, "type": "if", "locations": [{"start": {"line": 76, "column": 2}, "end": {"line": 88, "column": 3}}, {"start": {"line": 76, "column": 2}, "end": {"line": 88, "column": 3}}]}, "13": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 85, "column": 5}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 85, "column": 5}}, {"start": {"line": 79, "column": 4}, "end": {"line": 85, "column": 5}}]}, "14": {"loc": {"start": {"line": 82, "column": 6}, "end": {"line": 84, "column": 7}}, "type": "if", "locations": [{"start": {"line": 82, "column": 6}, "end": {"line": 84, "column": 7}}, {"start": {"line": 82, "column": 6}, "end": {"line": 84, "column": 7}}]}, "15": {"loc": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 43}}, {"start": {"line": 82, "column": 47}, "end": {"line": 82, "column": 78}}]}, "16": {"loc": {"start": {"line": 97, "column": 25}, "end": {"line": 97, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 25}, "end": {"line": 97, "column": 44}}, {"start": {"line": 97, "column": 48}, "end": {"line": 97, "column": 69}}]}, "17": {"loc": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 53}}, "type": "if", "locations": [{"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 53}}, {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 53}}]}, "18": {"loc": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 49}}, "type": "if", "locations": [{"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 49}}, {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 49}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 22, "5": 22, "6": 22, "7": 0, "8": 0, "9": 22, "10": 0, "11": 0, "12": 22, "13": 0, "14": 0, "15": 22, "16": 2, "17": 22, "18": 1, "19": 22, "20": 1, "21": 22, "22": 2, "23": 22, "24": 44, "25": 2, "26": 22, "27": 726, "28": 1, "29": 22, "30": 1, "31": 22, "32": 1, "33": 22, "34": 2, "35": 22, "36": 6, "37": 6, "38": 5, "39": 5, "40": 5, "41": 2, "42": 4, "43": 20, "44": 1, "45": 1, "46": 22, "47": 22, "48": 17, "49": 22, "50": 12, "51": 22}, "f": {"0": 22, "1": 44, "2": 726, "3": 22}, "b": {"0": [0, 22], "1": [0, 22], "2": [0, 22], "3": [2, 20], "4": [1, 21], "5": [1, 21], "6": [2, 20], "7": [2, 42], "8": [1, 725], "9": [1, 21], "10": [1, 21], "11": [2, 20], "12": [6, 16], "13": [5, 1], "14": [2, 3], "15": [5, 2], "16": [22, 12], "17": [17, 5], "18": [12, 10]}, "_coverageSchema": "332fd63041d2c1bcb487cc26dd0d5f7d97098a6c", "hash": "7a01205bc1fdf589bdf194d23f1405400131fa00", "contentHash": "8b2210ff664cab8b0916540357b1d2f9_10.1.2"}}