{"version": 3, "file": "AssetSources.js", "sourceRoot": "", "sources": ["../src/AssetSources.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,IAAI,MAAM,iBAAiB,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,GAAG,MAAM,WAAW,CAAC;AAE5B,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAiB7E,mEAAmE;AACnE,MAAM,gBAAgB,GAAG,WAAW,EAAE,CAAC,gBAAgB,CAAC;AAExD;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAmB;IACnD,uDAAuD;IACvD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAClE,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;KACpD;IAED,kGAAkG;IAClG,2BAA2B;IAC3B,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IAExF,6DAA6D;IAC7D,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IAChF,IAAI,GAAG,EAAE;QACP,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;KACvC;IAED,uDAAuD;IACvD,MAAM,gBAAgB,GAAG,WAAW,EAAE,CAAC,gBAAgB,CAAC;IACxD,IAAI,gBAAgB,EAAE;QACpB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;KACvC;IAED,MAAM,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC;IAClD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,MAAM,MAAM,GAAG,IAAI,kBAAkB,CACnC,IAAI,CAAC,IAAI,CACV,GAAG,SAAS,GAAG,aAAa,aAAa,kBAAkB,CAC1D,QAAQ,CAAC,EAAE,CACZ,SAAS,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAE1C,iGAAiG;IACjG,kDAAkD;IAClD,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE;QAChD,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;QAC7C,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;KACtB;IAED,4FAA4F;IAC5F,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAEjC,IAAI,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;QACvC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,UAAU,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAC;QAE1D,OAAO;YACL,GAAG,EAAE,OAAO,CAAC,IAAI;YACjB,IAAI;SACL,CAAC;KACH;IAED,4EAA4E;IAC5E,IAAI,WAAW,EAAE,CAAC,SAAS,EAAE;QAC3B,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,CAAC;QAC1D,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;KACpC;IAED,wDAAwD;IACxD,OAAO;QACL,GAAG,EAAE,6CAA6C,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAC5E,IAAI;KACL,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,GAAG,CAAC;KACZ;IAED,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,QAAQ,KAAK,EAAE,EAAE;QACnB,OAAO,GAAG,CAAC;KACZ;IAED,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC;IACzC,MAAM,YAAY,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAClF,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IACtC,OAAO,OAAO,CAAC,IAAI,CAAC;AACtB,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport path from 'path-browserify';\nimport { PixelRatio } from 'react-native';\nimport { PackagerAsset } from 'react-native/Libraries/Image/AssetRegistry';\nimport URL from 'url-parse';\n\nimport AssetSourceResolver from './AssetSourceResolver';\nimport { getManifest, getManifest2, manifestBaseUrl } from './PlatformUtils';\n\n// @docsMissing\nexport type AssetMetadata = Pick<\n  PackagerAsset,\n  'httpServerLocation' | 'name' | 'hash' | 'type' | 'scales' | 'width' | 'height'\n> & {\n  uri?: string;\n  fileHashes?: string[];\n  fileUris?: string[];\n};\n\nexport type AssetSource = {\n  uri: string;\n  hash: string;\n};\n\n// Fast lookup check if asset map has any overrides in the manifest\nconst assetMapOverride = getManifest().assetMapOverride;\n\n/**\n * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n * a { uri, hash } pair for the specific asset file.\n *\n * If the asset isn't an image with multiple scales, the first file is selected.\n */\nexport function selectAssetSource(meta: AssetMetadata): AssetSource {\n  // Override with the asset map in manifest if available\n  if (assetMapOverride && assetMapOverride.hasOwnProperty(meta.hash)) {\n    meta = { ...meta, ...assetMapOverride[meta.hash] };\n  }\n\n  // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n  // explicitly provided URIs\n  const scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  const index = meta.scales.findIndex((s) => s === scale);\n  const hash = meta.fileHashes ? meta.fileHashes[index] || meta.fileHashes[0] : meta.hash;\n\n  // Allow asset processors to directly provide the URL to load\n  const uri = meta.fileUris ? meta.fileUris[index] || meta.fileUris[0] : meta.uri;\n  if (uri) {\n    return { uri: resolveUri(uri), hash };\n  }\n\n  // Check if the assetUrl was overridden in the manifest\n  const assetUrlOverride = getManifest().assetUrlOverride;\n  if (assetUrlOverride) {\n    const uri = path.join(assetUrlOverride, hash);\n    return { uri: resolveUri(uri), hash };\n  }\n\n  const fileScale = scale === 1 ? '' : `@${scale}x`;\n  const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  const suffix = `/${encodeURIComponent(\n    meta.name\n  )}${fileScale}${fileExtension}?platform=${encodeURIComponent(\n    Platform.OS\n  )}&hash=${encodeURIComponent(meta.hash)}`;\n\n  // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n  // development server or production CDN URL origin\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    const uri = meta.httpServerLocation + suffix;\n    return { uri, hash };\n  }\n\n  // For assets during development using manifest2, we use the development server's URL origin\n  const manifest2 = getManifest2();\n\n  if (manifest2?.extra?.expoGo?.developer) {\n    const baseUrl = new URL(`http://${manifest2.extra.expoGo.debuggerHost}`);\n    baseUrl.set('pathname', meta.httpServerLocation + suffix);\n\n    return {\n      uri: baseUrl.href,\n      hash,\n    };\n  }\n\n  // For assets during development, we use the development server's URL origin\n  if (getManifest().developer) {\n    const baseUrl = new URL(getManifest().bundleUrl);\n    baseUrl.set('pathname', meta.httpServerLocation + suffix);\n    return { uri: baseUrl.href, hash };\n  }\n\n  // Production CDN URIs are based on each asset file hash\n  return {\n    uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`,\n    hash,\n  };\n}\n\n/**\n * Resolves the given URI to an absolute URI. If the given URI is already an absolute URI, it is\n * simply returned. Otherwise, if it is a relative URI, it is resolved relative to the manifest's\n * base URI.\n */\nexport function resolveUri(uri: string): string {\n  if (!manifestBaseUrl) {\n    return uri;\n  }\n\n  const { protocol } = new URL(uri);\n  if (protocol !== '') {\n    return uri;\n  }\n\n  const baseUrl = new URL(manifestBaseUrl);\n  const resolvedPath = uri.startsWith('/') ? uri : path.join(baseUrl.pathname, uri);\n  baseUrl.set('pathname', resolvedPath);\n  return baseUrl.href;\n}\n"]}