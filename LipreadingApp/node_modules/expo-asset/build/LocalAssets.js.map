{"version": 3, "file": "LocalAssets.js", "sourceRoot": "", "sources": ["../src/LocalAssets.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,KAAK,UAAU,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEjD,gFAAgF;AAChF,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;AAE9D,sDAAsD;AACtD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AAErC;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAY,EAAE,IAAmB;IAChE,MAAM,cAAc,GAAG,IAAI,CAAC;IAC5B,MAAM,oBAAoB,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;IAErD,QAAQ,IAAI,EAAE;QACZ,KAAK,cAAc,IAAI,WAAW,CAAC,CAAC;YAClC,OAAO,WAAW,CAAC,cAAc,CAAC,CAAC;SACpC;QACD,KAAK,oBAAoB,IAAI,WAAW,CAAC,CAAC;YACxC,gDAAgD;YAChD,OAAO,WAAW,CAAC,oBAAoB,CAAC,CAAC;SAC1C;QACD,KAAK,CAAC,OAAO,CAAC,CAAC;YACb,sEAAsE;YACtE,mFAAmF;YACnF,MAAM,SAAS,GAAG,SAAS,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3D,IAAI,SAAS,CAAC,YAAY,KAAK,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC5E,OAAO,IAAI,CAAC;aACb;YACD,OAAO,GAAG,UAAU,CAAC,eAAe,GAAG,SAAS,EAAE,CAAC;SACpD;QACD;YACE,OAAO,IAAI,CAAC;KACf;AACH,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\nimport * as FileSystem from 'expo-file-system';\n\nimport { getLocalAssets } from './PlatformUtils';\n\n// Fast lookup check if assets are available in the local bundle in managed apps\nconst bundledAssets = new Set(FileSystem.bundledAssets || []);\n\n// localAssets are provided by the expo-updates module\nconst localAssets = getLocalAssets();\n\n/**\n * Returns the URI of a local asset from its hash, or null if the asset is not available locally\n */\nexport function getLocalAssetUri(hash: string, type: string | null): string | null {\n  const localAssetsKey = hash;\n  const legacyLocalAssetsKey = `${hash}.${type ?? ''}`;\n\n  switch (true) {\n    case localAssetsKey in localAssets: {\n      return localAssets[localAssetsKey];\n    }\n    case legacyLocalAssetsKey in localAssets: {\n      // legacy updates store assets with an extension\n      return localAssets[legacyLocalAssetsKey];\n    }\n    case !__DEV__: {\n      // check legacy location in case we're in Expo client/managed workflow\n      // TODO(eric): remove this once bundledAssets is no longer exported from FileSystem\n      const assetName = `asset_${hash}${type ? `.${type}` : ''}`;\n      if (Constants.appOwnership !== 'standalone' || !bundledAssets.has(assetName)) {\n        return null;\n      }\n      return `${FileSystem.bundleDirectory}${assetName}`;\n    }\n    default:\n      return null;\n  }\n}\n"]}