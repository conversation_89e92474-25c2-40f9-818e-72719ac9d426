{"version": 3, "file": "Asset.js", "sourceRoot": "", "sources": ["../src/Asset.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAE1E,OAAO,EAAiB,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AAClE,OAAO,KAAK,SAAS,MAAM,aAAa,CAAC;AACzC,OAAO,KAAK,WAAW,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAC7E,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAmBtD,cAAc;AACd;;;GAGG;AACH,MAAM,OAAO,KAAK;IAChB;;OAEG;IACH,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB;;OAEG;IACH,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;IAElB;;;OAGG;IACH,IAAI,CAAS;IACb;;OAEG;IACH,IAAI,CAAS;IACb;;OAEG;IACH,IAAI,GAAkB,IAAI,CAAC;IAC3B;;;;;;;OAOG;IACH,GAAG,CAAS;IACZ;;;OAGG;IACH,QAAQ,GAAkB,IAAI,CAAC;IAC/B;;;OAGG;IACH,KAAK,GAAkB,IAAI,CAAC;IAC5B;;OAEG;IACH,MAAM,GAAkB,IAAI,CAAC;IAC7B,eAAe;IACf,WAAW,GAAY,KAAK,CAAC;IAC7B,eAAe;IACf,UAAU,GAAY,KAAK,CAAC;IAE5B;;OAEG;IACH,kBAAkB,GAA+B,EAAE,CAAC;IAEpD,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAmB;QAC1E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;QAED,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;SACF;QAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;YACzB,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,IAAI,EAAE;gBACT,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;aAC7C;SACF;IACH,CAAC;IAED,cAAc;IACd;;;;;;;;;;OAUG;IACH,MAAM,CAAC,SAAS,CAAC,QAA+C;QAC9D,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,cAAc;IACd;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,kBAAmC;QACnD,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;YAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SAC1C;QAED,MAAM,IAAI,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,WAAW,kBAAkB,sCAAsC,CAAC,CAAC;SACtF;QAED,0EAA0E;QAC1E,2CAA2C;QAC3C,IAAI,CAAC,2BAA2B,EAAE;YAChC,MAAM,EAAE,GAAG,EAAE,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YACvD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG;gBACH,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YAEH,uEAAuE;YACvE,wEAAwE;YACxE,mEAAmE;YACnE,UAAU;YACV,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;gBAClF,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;gBAC3B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;aACzB;YAED,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAChC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;IACf,MAAM,CAAC,YAAY,CAAC,IAAmB;QACrC,4FAA4F;QAC5F,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC/B;QAED,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI;YACJ,GAAG;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QACH,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAe;IACf,MAAM,CAAC,OAAO,CAAC,GAAW;QACxB,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACzB;QAED,gCAAgC;QAChC,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YAC/B,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACxC;aAAM;YACL,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SACvE;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,EAAE;YACR,IAAI;YACJ,IAAI,EAAE,IAAI;YACV,GAAG;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAEzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,cAAc;IACd;;;;;;;OAOG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI;YACF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;gBACzB,IAAI,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACtC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC9E,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;oBACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;iBAClB;qBAAM;oBACL,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAC7C;aACF;YACD,IAAI,CAAC,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/E,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;SAC7D;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,CAAC;SACT;gBAAS;YACR,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IACd,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport { getAssetByID } from 'react-native/Libraries/Image/AssetRegistry';\n\nimport { AssetMetadata, selectAssetSource } from './AssetSources';\nimport * as AssetUris from './AssetUris';\nimport * as ImageAssets from './ImageAssets';\nimport { getLocalAssetUri } from './LocalAssets';\nimport { downloadAsync, IS_ENV_WITH_UPDATES_ENABLED } from './PlatformUtils';\nimport resolveAssetSource from './resolveAssetSource';\n\n// @docsMissing\nexport type AssetDescriptor = {\n  name: string;\n  type: string;\n  hash?: string | null;\n  uri: string;\n  width?: number | null;\n  height?: number | null;\n};\n\ntype DownloadPromiseCallbacks = {\n  resolve: () => void;\n  reject: (error: Error) => void;\n};\n\nexport { AssetMetadata };\n\n// @needsAudit\n/**\n * The `Asset` class represents an asset in your app. It gives metadata about the asset (such as its\n * name and type) and provides facilities to load the asset data.\n */\nexport class Asset {\n  /**\n   * @private\n   */\n  static byHash = {};\n  /**\n   * @private\n   */\n  static byUri = {};\n\n  /**\n   * The name of the asset file without the extension. Also without the part from `@` onward in the\n   * filename (used to specify scale factor for images).\n   */\n  name: string;\n  /**\n   * The extension of the asset filename.\n   */\n  type: string;\n  /**\n   * The MD5 hash of the asset's data.\n   */\n  hash: string | null = null;\n  /**\n   * A URI that points to the asset's data on the remote server. When running the published version\n   * of your app, this refers to the location on Expo's asset server where Expo has stored your\n   * asset. When running the app from Expo CLI during development, this URI points to Expo CLI's\n   * server running on your computer and the asset is served directly from your computer. If you\n   * are not using Classic Updates (legacy), this field should be ignored as we ensure your assets\n   * are on device before before running your application logic.\n   */\n  uri: string;\n  /**\n   * If the asset has been downloaded (by calling [`downloadAsync()`](#downloadasync)), the\n   * `file://` URI pointing to the local file on the device that contains the asset data.\n   */\n  localUri: string | null = null;\n  /**\n   * If the asset is an image, the width of the image data divided by the scale factor. The scale\n   * factor is the number after `@` in the filename, or `1` if not present.\n   */\n  width: number | null = null;\n  /**\n   * If the asset is an image, the height of the image data divided by the scale factor. The scale factor is the number after `@` in the filename, or `1` if not present.\n   */\n  height: number | null = null;\n  // @docsMissing\n  downloading: boolean = false;\n  // @docsMissing\n  downloaded: boolean = false;\n\n  /**\n   * @private\n   */\n  _downloadCallbacks: DownloadPromiseCallbacks[] = [];\n\n  constructor({ name, type, hash = null, uri, width, height }: AssetDescriptor) {\n    this.name = name;\n    this.type = type;\n    this.hash = hash;\n    this.uri = uri;\n\n    if (typeof width === 'number') {\n      this.width = width;\n    }\n    if (typeof height === 'number') {\n      this.height = height;\n    }\n\n    if (hash) {\n      this.localUri = getLocalAssetUri(hash, type);\n      if (this.localUri) {\n        this.downloaded = true;\n      }\n    }\n\n    if (Platform.OS === 'web') {\n      if (!name) {\n        this.name = AssetUris.getFilename(uri);\n      }\n      if (!type) {\n        this.type = AssetUris.getFileExtension(uri);\n      }\n    }\n  }\n\n  // @needsAudit\n  /**\n   * A helper that wraps `Asset.fromModule(module).downloadAsync` for convenience.\n   * @param moduleId An array of `require('path/to/file')` or external network URLs. Can also be\n   * just one module or URL without an Array.\n   * @return Returns a Promise that fulfills with an array of `Asset`s when the asset(s) has been\n   * saved to disk.\n   * @example\n   * ```ts\n   * const [{ localUri }] = await Asset.loadAsync(require('./assets/snack-icon.png'));\n   * ```\n   */\n  static loadAsync(moduleId: number | number[] | string | string[]): Promise<Asset[]> {\n    const moduleIds = Array.isArray(moduleId) ? moduleId : [moduleId];\n    return Promise.all(moduleIds.map((moduleId) => Asset.fromModule(moduleId).downloadAsync()));\n  }\n\n  // @needsAudit\n  /**\n   * Returns the [`Asset`](#asset) instance representing an asset given its module or URL.\n   * @param virtualAssetModule The value of `require('path/to/file')` for the asset or external\n   * network URL\n   * @return The [`Asset`](#asset) instance for the asset.\n   */\n  static fromModule(virtualAssetModule: number | string): Asset {\n    if (typeof virtualAssetModule === 'string') {\n      return Asset.fromURI(virtualAssetModule);\n    }\n\n    const meta = getAssetByID(virtualAssetModule);\n    if (!meta) {\n      throw new Error(`Module \"${virtualAssetModule}\" is missing from the asset registry`);\n    }\n\n    // Outside of the managed env we need the moduleId to initialize the asset\n    // because resolveAssetSource depends on it\n    if (!IS_ENV_WITH_UPDATES_ENABLED) {\n      const { uri } = resolveAssetSource(virtualAssetModule);\n      const asset = new Asset({\n        name: meta.name,\n        type: meta.type,\n        hash: meta.hash,\n        uri,\n        width: meta.width,\n        height: meta.height,\n      });\n\n      // TODO: FileSystem should probably support 'downloading' from drawable\n      // resources But for now it doesn't (it only supports raw resources) and\n      // React Native's Image works fine with drawable resource names for\n      // images.\n      if (Platform.OS === 'android' && !uri.includes(':') && (meta.width || meta.height)) {\n        asset.localUri = asset.uri;\n        asset.downloaded = true;\n      }\n\n      Asset.byHash[meta.hash] = asset;\n      return asset;\n    }\n\n    return Asset.fromMetadata(meta);\n  }\n\n  // @docsMissing\n  static fromMetadata(meta: AssetMetadata): Asset {\n    // The hash of the whole asset, not to be confused with the hash of a specific file returned\n    // from `selectAssetSource`\n    const metaHash = meta.hash;\n    if (Asset.byHash[metaHash]) {\n      return Asset.byHash[metaHash];\n    }\n\n    const { uri, hash } = selectAssetSource(meta);\n    const asset = new Asset({\n      name: meta.name,\n      type: meta.type,\n      hash,\n      uri,\n      width: meta.width,\n      height: meta.height,\n    });\n    Asset.byHash[metaHash] = asset;\n    return asset;\n  }\n\n  // @docsMissing\n  static fromURI(uri: string): Asset {\n    if (Asset.byUri[uri]) {\n      return Asset.byUri[uri];\n    }\n\n    // Possibly a Base64-encoded URI\n    let type = '';\n    if (uri.indexOf(';base64') > -1) {\n      type = uri.split(';')[0].split('/')[1];\n    } else {\n      const extension = AssetUris.getFileExtension(uri);\n      type = extension.startsWith('.') ? extension.substring(1) : extension;\n    }\n\n    const asset = new Asset({\n      name: '',\n      type,\n      hash: null,\n      uri,\n    });\n\n    Asset.byUri[uri] = asset;\n\n    return asset;\n  }\n\n  // @needsAudit\n  /**\n   * Downloads the asset data to a local file in the device's cache directory. Once the returned\n   * promise is fulfilled without error, the [`localUri`](#assetlocaluri) field of this asset points\n   * to a local file containing the asset data. The asset is only downloaded if an up-to-date local\n   * file for the asset isn't already present due to an earlier download. The downloaded `Asset`\n   * will be returned when the promise is resolved.\n   * @return Returns a Promise which fulfills with an `Asset` instance.\n   */\n  async downloadAsync(): Promise<this> {\n    if (this.downloaded) {\n      return this;\n    }\n    if (this.downloading) {\n      await new Promise<void>((resolve, reject) => {\n        this._downloadCallbacks.push({ resolve, reject });\n      });\n      return this;\n    }\n    this.downloading = true;\n\n    try {\n      if (Platform.OS === 'web') {\n        if (ImageAssets.isImageType(this.type)) {\n          const { width, height, name } = await ImageAssets.getImageInfoAsync(this.uri);\n          this.width = width;\n          this.height = height;\n          this.name = name;\n        } else {\n          this.name = AssetUris.getFilename(this.uri);\n        }\n      }\n      this.localUri = await downloadAsync(this.uri, this.hash, this.type, this.name);\n\n      this.downloaded = true;\n      this._downloadCallbacks.forEach(({ resolve }) => resolve());\n    } catch (e) {\n      this._downloadCallbacks.forEach(({ reject }) => reject(e));\n      throw e;\n    } finally {\n      this.downloading = false;\n      this._downloadCallbacks = [];\n    }\n    return this;\n  }\n}\n"]}