{"version": 3, "file": "Asset.fx.js", "sourceRoot": "", "sources": ["../src/Asset.fx.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,EAAE,0BAA0B,EAAE,MAAM,sBAAsB,CAAC;AAElE,+FAA+F;AAC/F,IAAI,2BAA2B,EAAE;IAC/B,0BAA0B,CAAC,CAAC,QAAQ,EAAE,EAAE;QACtC,IAAI;YACF,2FAA2F;YAC3F,IAAI,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE;gBAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACjD,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC5E;iBAAM;gBACL,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;aAChC;SACF;QAAC,MAAM;YACN,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;SAChC;IACH,CAAC,CAAC,CAAC;CACJ", "sourcesContent": ["import { Asset } from './Asset';\nimport { IS_ENV_WITH_UPDATES_ENABLED } from './PlatformUtils';\nimport { setCustomSourceTransformer } from './resolveAssetSource';\n\n// Override React Native's asset resolution for `Image` components in contexts where it matters\nif (IS_ENV_WITH_UPDATES_ENABLED) {\n  setCustomSourceTransformer((resolver) => {\n    try {\n      // B<PERSON><PERSON> is using the hashAssetFiles plugin if and only if the fileHashes property exists\n      if (resolver.asset.fileHashes) {\n        const asset = Asset.fromMetadata(resolver.asset);\n        return resolver.fromSource(asset.downloaded ? asset.localUri! : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch {\n      return resolver.defaultAsset();\n    }\n  });\n}\n"]}