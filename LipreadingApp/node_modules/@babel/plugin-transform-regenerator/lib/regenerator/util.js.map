{"version": 3, "names": ["_core", "require", "newHelpersAvailable", "exports", "file", "availableHelper", "t", "isIdentifier", "addHelper", "name", "runtimeProperty", "helper", "memberExpression", "isArrowFunctionExpression", "body", "callExpression", "identifier", "isReference", "path", "isReferenced", "parentPath", "isAssignmentExpression", "left", "node"], "sources": ["../../src/regenerator/util.ts"], "sourcesContent": ["import type { PluginPass } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\nexport let newHelpersAvailable: (file: PluginPass) => boolean;\nif (!process.env.BABEL_8_BREAKING) {\n  newHelpersAvailable = (file: PluginPass) => {\n    if (!process.env.IS_PUBLISH && process.env.FORCE_OLD_REGENERATOR) {\n      // Only for testing purposes.\n      return false;\n    }\n    return (\n      file.availableHelper(\"regenerator\") &&\n      // At this point, we can safely try to inject the `regenerator` helper.\n      // If this plugin tries to inject any helper, than we are sure that\n      // `regenerator` is one of them.\n      !t.isIdentifier(file.addHelper(\"regenerator\"), {\n        // This is a special marker returned by transform-runtime, which means\n        // \"the version of `@babel/runtime` does not have the helper\".\n        // Normally transform-runtime will fallback to just injecting the\n        // helper inline, but we special handle this case to instead be able\n        // to fallback to the old `regeneratorRuntime` helper\n        name: \"__interal_marker_fallback_regenerator__\",\n      })\n    );\n  };\n}\n\nexport let runtimeProperty: (file: PluginPass, name: any) => any;\nif (!process.env.BABEL_8_BREAKING) {\n  runtimeProperty = function (file, name) {\n    const helper = file.addHelper(\"regeneratorRuntime\");\n    return t.memberExpression(\n      // In some cases, `helper` will be (() => regeneratorRuntime).\n      // Se the implementation in transform-runtime for more details.\n      t.isArrowFunctionExpression(helper) &&\n        t.isIdentifier((helper as any).body)\n        ? (helper as any).body\n        : t.callExpression(helper, []),\n      t.identifier(name),\n      false,\n    );\n  };\n}\n\nexport function isReference(path: any) {\n  return (\n    path.isReferenced() ||\n    path.parentPath.isAssignmentExpression({ left: path.node })\n  );\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AAEO,IAAIC,mBAAkD,GAAAC,OAAA,CAAAD,mBAAA;AAC1B;EACjCC,OAAA,CAAAD,mBAAA,GAAAA,mBAAmB,GAAIE,IAAgB,IAAK;IAAA;IAK1C,OACEA,IAAI,CAACC,eAAe,CAAC,aAAa,CAAC,IAInC,CAACC,WAAC,CAACC,YAAY,CAACH,IAAI,CAACI,SAAS,CAAC,aAAa,CAAC,EAAE;MAM7CC,IAAI,EAAE;IACR,CAAC,CAAC;EAEN,CAAC;AACH;AAEO,IAAIC,eAAqD,GAAAP,OAAA,CAAAO,eAAA;AAC7B;EACjCP,OAAA,CAAAO,eAAA,GAAAA,eAAe,GAAG,SAAAA,CAAUN,IAAI,EAAEK,IAAI,EAAE;IACtC,MAAME,MAAM,GAAGP,IAAI,CAACI,SAAS,CAAC,oBAAoB,CAAC;IACnD,OAAOF,WAAC,CAACM,gBAAgB,CAGvBN,WAAC,CAACO,yBAAyB,CAACF,MAAM,CAAC,IACjCL,WAAC,CAACC,YAAY,CAAEI,MAAM,CAASG,IAAI,CAAC,GACjCH,MAAM,CAASG,IAAI,GACpBR,WAAC,CAACS,cAAc,CAACJ,MAAM,EAAE,EAAE,CAAC,EAChCL,WAAC,CAACU,UAAU,CAACP,IAAI,CAAC,EAClB,KACF,CAAC;EACH,CAAC;AACH;AAEO,SAASQ,WAAWA,CAACC,IAAS,EAAE;EACrC,OACEA,IAAI,CAACC,YAAY,CAAC,CAAC,IACnBD,IAAI,CAACE,UAAU,CAACC,sBAAsB,CAAC;IAAEC,IAAI,EAAEJ,IAAI,CAACK;EAAK,CAAC,CAAC;AAE/D", "ignoreList": []}