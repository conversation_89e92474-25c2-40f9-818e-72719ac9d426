{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCreateClassFeaturesPlugin", "generateUid", "scope", "denyList", "name", "uid", "i", "has", "mapLast", "arr", "fn", "length", "slice", "_default", "exports", "default", "declare", "types", "t", "template", "traverse", "assertVersion", "rawNamedEvaluationVisitor", "buildNamedEvaluationVisitor", "path", "isClassExpression", "node", "body", "el", "isStaticBlock", "isClassProperty", "isClassPrivateProperty", "static", "classPath", "state", "nameNode", "stringLiteral", "get", "unshiftContainer", "staticBlock", "statement", "ast", "addHelper", "classAccessorProperty", "ClassAccessorProperty", "namedEvaluationVisitor", "visitors", "explode", "maybeSequenceExpression", "expressions", "sequenceExpression", "blocksToExpressions", "blocks", "map", "block", "isExpressionStatement", "inheritsComments", "expression", "prependToInitializer", "prop", "value", "expr", "unaryExpression", "manipulateOptions", "_", "parser", "plugins", "push", "pre", "enableFeature", "file", "FEATURES", "staticBlocks", "visitor", "ClassBody", "classBody", "parentPath", "id", "type", "isStatement", "_namedEvaluationVisit", "enter", "for<PERSON>ach", "f", "call", "pendingStaticBlocks", "lastStaticProp", "remove", "tmp", "generateDeclaredUidIdentifier", "arrowBody", "needsCompletionValue", "isExpression", "thisExpression", "blockStatement", "returnStatement", "init", "privateNames", "Set", "isPrivate", "add", "staticBlockPrivateId", "staticBlockRef", "privateName", "identifier", "pushContainer", "classPrivateProperty", "staticBlockClosureCall", "callExpression", "cloneNode", "replaceWith", "parent", "insertAfter", "expressionStatement"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { Node<PERSON><PERSON>, Scope, types as t } from \"@babel/core\";\n\nimport {\n  buildNamedEvaluationVisitor,\n  enableFeature,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\n\n/**\n * Generate a uid that is not in `denyList`\n *\n * @param {Scope} scope\n * @param {Set<string>} denyList a deny list that the generated uid should avoid\n * @returns\n */\nfunction generateUid(scope: Scope, denyList: Set<string>) {\n  const name = \"\";\n  let uid;\n  let i = 1;\n  do {\n    uid = `_${name}`;\n    if (i > 1) uid += i;\n    i++;\n  } while (denyList.has(uid));\n  return uid;\n}\n\nfunction mapLast<T>(arr: T[], fn: (value: T) => T): T[] {\n  if (arr.length === 0) return arr;\n  return [...arr.slice(0, -1), fn(arr[arr.length - 1])];\n}\n\nexport default declare(({ types: t, template, traverse, assertVersion }) => {\n  assertVersion(REQUIRED_VERSION(\"^7.12.0\"));\n\n  const rawNamedEvaluationVisitor = buildNamedEvaluationVisitor(\n    (path: NodePath) => {\n      if (!path.isClassExpression()) return false;\n      for (let i = path.node.body.body.length - 1; i >= 0; i--) {\n        const el = path.node.body.body[i];\n        if (t.isStaticBlock(el)) {\n          return true;\n        }\n        if (\n          (t.isClassProperty(el) || t.isClassPrivateProperty(el)) &&\n          el.static\n        ) {\n          break;\n        }\n      }\n      return false;\n    },\n    (classPath: NodePath<t.ClassExpression>, state, name) => {\n      const nameNode = typeof name === \"string\" ? t.stringLiteral(name) : name;\n\n      classPath.get(\"body\").unshiftContainer(\n        \"body\",\n        t.staticBlock([\n          template.statement.ast`\n            ${state.addHelper(\"setFunctionName\")}(this, ${nameNode});\n          `,\n        ]),\n      );\n    },\n  );\n\n  if (!process.env.BABEL_8_BREAKING && !t.classAccessorProperty) {\n    // For old versions of Babel 7, with no ClassAccessorProperty support.\n    delete rawNamedEvaluationVisitor.ClassAccessorProperty;\n  }\n\n  const namedEvaluationVisitor = traverse.visitors.explode(\n    rawNamedEvaluationVisitor,\n  );\n\n  const maybeSequenceExpression = (\n    expressions: t.Expression[],\n  ): t.Expression => {\n    if (expressions.length === 1) {\n      return expressions[0];\n    } else {\n      return t.sequenceExpression(expressions);\n    }\n  };\n\n  const blocksToExpressions = (blocks: Array<t.StaticBlock>) =>\n    blocks.map(block => {\n      const { body } = block;\n      if (body.length === 1 && t.isExpressionStatement(body[0])) {\n        // We special-case the single expression case to avoid the iife, since\n        // it's common.\n        return t.inheritsComments(\n          t.inheritsComments(body[0].expression, body[0]),\n          block,\n        );\n      }\n      return t.inheritsComments(\n        template.expression.ast`(() => { ${body} })()`,\n        block,\n      );\n    });\n\n  const prependToInitializer = (\n    prop: t.ClassProperty | t.ClassPrivateProperty,\n    expressions: t.Expression[],\n  ) => {\n    prop.value = prop.value\n      ? t.sequenceExpression([...expressions, prop.value])\n      : maybeSequenceExpression(\n          mapLast(expressions, expr => t.unaryExpression(\"void\", expr)),\n        );\n  };\n\n  return {\n    name: \"transform-class-static-block\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"classStaticBlock\"),\n\n    pre() {\n      // Enable this in @babel/helper-create-class-features-plugin, so that it\n      // can be handled by the private fields and methods transform.\n      enableFeature(this.file, FEATURES.staticBlocks, /* loose */ false);\n    },\n\n    visitor: {\n      // Run on ClassBody and not on class so that if @babel/helper-create-class-features-plugin\n      // is enabled it can generate optimized output without passing from the intermediate\n      // private fields representation.\n      ClassBody(classBody) {\n        const { scope } = classBody;\n\n        // If needed, add the name to the class\n        let parentPath: NodePath<t.Node> = classBody.parentPath;\n        if (parentPath.isClassExpression() && !parentPath.node.id) {\n          do ({ parentPath } = parentPath);\n          while (\n            parentPath &&\n            !namedEvaluationVisitor[parentPath.type] &&\n            !parentPath.isStatement()\n          );\n          if (parentPath) {\n            namedEvaluationVisitor[parentPath.type]?.enter.forEach(f =>\n              f.call(this, parentPath, this),\n            );\n          }\n        }\n\n        const pendingStaticBlocks: Array<t.StaticBlock> = [];\n        let lastStaticProp:\n          | null\n          | NodePath<t.ClassProperty>\n          | NodePath<t.ClassPrivateProperty> = null;\n\n        for (const path of classBody.get(\"body\")) {\n          if (path.isStaticBlock()) {\n            pendingStaticBlocks.push(path.node);\n            path.remove();\n          } else if (\n            path.isClassProperty({ static: true }) ||\n            path.isClassPrivateProperty({ static: true })\n          ) {\n            lastStaticProp = path;\n\n            if (pendingStaticBlocks.length > 0) {\n              // push static blocks right before the initializer of the next\n              // static field in the class.\n\n              prependToInitializer(\n                path.node,\n                blocksToExpressions(pendingStaticBlocks),\n              );\n              pendingStaticBlocks.length = 0;\n            }\n          }\n        }\n\n        if (pendingStaticBlocks.length > 0) {\n          // if there are static blocks not followed by a static field where\n          // we can inject them, wrap them in a function and push it in the\n          // last static field in the class (or create a new one, if needed).\n          // After the class body runs, call the function to run the remaining\n          // static blocks bodies.\n\n          const tmp = scope.generateDeclaredUidIdentifier(\"staticBlock\");\n          let arrowBody;\n          const needsCompletionValue = classBody.parentPath.isExpression();\n          if (\n            pendingStaticBlocks.length > 1 ||\n            (pendingStaticBlocks[0].body.length === 1 &&\n              t.isExpressionStatement(pendingStaticBlocks[0].body[0]))\n          ) {\n            const expressions = blocksToExpressions(pendingStaticBlocks);\n            if (needsCompletionValue) {\n              expressions.push(t.thisExpression());\n            }\n            arrowBody = maybeSequenceExpression(expressions);\n          } else {\n            arrowBody = t.blockStatement(pendingStaticBlocks[0].body);\n            if (needsCompletionValue) {\n              arrowBody.body.push(t.returnStatement(t.thisExpression()));\n            }\n          }\n          const init = template.expression.ast`${tmp} = () => ${arrowBody}`;\n\n          if (lastStaticProp) {\n            prependToInitializer(lastStaticProp.node, [init]);\n          } else {\n            // If there are no static fields at all, it's safe to inject a\n            // new private properties before running the static blocks because\n            // there is no code that could have already made the class\n            // non-extensible.\n\n            const privateNames = new Set<string>();\n            for (const path of classBody.get(\"body\")) {\n              if (path.isPrivate()) {\n                privateNames.add(path.get(\"key.id\").node.name);\n              }\n            }\n            const staticBlockPrivateId = generateUid(scope, privateNames);\n            const staticBlockRef = t.privateName(\n              t.identifier(staticBlockPrivateId),\n            );\n            classBody.pushContainer(\"body\", [\n              t.classPrivateProperty(staticBlockRef, init, [], true),\n            ]);\n          }\n\n          const staticBlockClosureCall = t.callExpression(t.cloneNode(tmp), []);\n          if (classBody.parentPath.isClassExpression()) {\n            // We don't use .insertAfter() because we don't need to insert the\n            // tmp variable to preserve the class as the result value, because\n            // the call will already return the class itself.\n            classBody.parentPath.replaceWith(\n              t.sequenceExpression([\n                classBody.parent as t.ClassExpression,\n                staticBlockClosureCall,\n              ]),\n            );\n          } else {\n            classBody.parentPath.insertAfter(\n              t.expressionStatement(staticBlockClosureCall),\n            );\n          }\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAGA,IAAAC,gCAAA,GAAAD,OAAA;AAaA,SAASE,WAAWA,CAACC,KAAY,EAAEC,QAAqB,EAAE;EACxD,MAAMC,IAAI,GAAG,EAAE;EACf,IAAIC,GAAG;EACP,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACDD,GAAG,GAAG,IAAID,IAAI,EAAE;IAChB,IAAIE,CAAC,GAAG,CAAC,EAAED,GAAG,IAAIC,CAAC;IACnBA,CAAC,EAAE;EACL,CAAC,QAAQH,QAAQ,CAACI,GAAG,CAACF,GAAG,CAAC;EAC1B,OAAOA,GAAG;AACZ;AAEA,SAASG,OAAOA,CAAIC,GAAQ,EAAEC,EAAmB,EAAO;EACtD,IAAID,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE,OAAOF,GAAG;EAChC,OAAO,CAAC,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEF,EAAE,CAACD,GAAG,CAACA,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAc,CAAC,KAAK;EAC1EA,aAAa,sCAA4B,CAAC;EAE1C,MAAMC,yBAAyB,GAAG,IAAAC,4DAA2B,EAC1DC,IAAc,IAAK;IAClB,IAAI,CAACA,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE,OAAO,KAAK;IAC3C,KAAK,IAAInB,CAAC,GAAGkB,IAAI,CAACE,IAAI,CAACC,IAAI,CAACA,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxD,MAAMsB,EAAE,GAAGJ,IAAI,CAACE,IAAI,CAACC,IAAI,CAACA,IAAI,CAACrB,CAAC,CAAC;MACjC,IAAIY,CAAC,CAACW,aAAa,CAACD,EAAE,CAAC,EAAE;QACvB,OAAO,IAAI;MACb;MACA,IACE,CAACV,CAAC,CAACY,eAAe,CAACF,EAAE,CAAC,IAAIV,CAAC,CAACa,sBAAsB,CAACH,EAAE,CAAC,KACtDA,EAAE,CAACI,MAAM,EACT;QACA;MACF;IACF;IACA,OAAO,KAAK;EACd,CAAC,EACD,CAACC,SAAsC,EAAEC,KAAK,EAAE9B,IAAI,KAAK;IACvD,MAAM+B,QAAQ,GAAG,OAAO/B,IAAI,KAAK,QAAQ,GAAGc,CAAC,CAACkB,aAAa,CAAChC,IAAI,CAAC,GAAGA,IAAI;IAExE6B,SAAS,CAACI,GAAG,CAAC,MAAM,CAAC,CAACC,gBAAgB,CACpC,MAAM,EACNpB,CAAC,CAACqB,WAAW,CAAC,CACZpB,QAAQ,CAACqB,SAAS,CAACC,GAAG;AAChC,cAAcP,KAAK,CAACQ,SAAS,CAAC,iBAAiB,CAAC,UAAUP,QAAQ;AAClE,WAAW,CACF,CACH,CAAC;EACH,CACF,CAAC;EAED,IAAqC,CAACjB,CAAC,CAACyB,qBAAqB,EAAE;IAE7D,OAAOrB,yBAAyB,CAACsB,qBAAqB;EACxD;EAEA,MAAMC,sBAAsB,GAAGzB,QAAQ,CAAC0B,QAAQ,CAACC,OAAO,CACtDzB,yBACF,CAAC;EAED,MAAM0B,uBAAuB,GAC3BC,WAA2B,IACV;IACjB,IAAIA,WAAW,CAACtC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOsC,WAAW,CAAC,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,OAAO/B,CAAC,CAACgC,kBAAkB,CAACD,WAAW,CAAC;IAC1C;EACF,CAAC;EAED,MAAME,mBAAmB,GAAIC,MAA4B,IACvDA,MAAM,CAACC,GAAG,CAACC,KAAK,IAAI;IAClB,MAAM;MAAE3B;IAAK,CAAC,GAAG2B,KAAK;IACtB,IAAI3B,IAAI,CAAChB,MAAM,KAAK,CAAC,IAAIO,CAAC,CAACqC,qBAAqB,CAAC5B,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAGzD,OAAOT,CAAC,CAACsC,gBAAgB,CACvBtC,CAAC,CAACsC,gBAAgB,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC8B,UAAU,EAAE9B,IAAI,CAAC,CAAC,CAAC,CAAC,EAC/C2B,KACF,CAAC;IACH;IACA,OAAOpC,CAAC,CAACsC,gBAAgB,CACvBrC,QAAQ,CAACsC,UAAU,CAAChB,GAAG,YAAYd,IAAI,OAAO,EAC9C2B,KACF,CAAC;EACH,CAAC,CAAC;EAEJ,MAAMI,oBAAoB,GAAGA,CAC3BC,IAA8C,EAC9CV,WAA2B,KACxB;IACHU,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,GACnB1C,CAAC,CAACgC,kBAAkB,CAAC,CAAC,GAAGD,WAAW,EAAEU,IAAI,CAACC,KAAK,CAAC,CAAC,GAClDZ,uBAAuB,CACrBxC,OAAO,CAACyC,WAAW,EAAEY,IAAI,IAAI3C,CAAC,CAAC4C,eAAe,CAAC,MAAM,EAAED,IAAI,CAAC,CAC9D,CAAC;EACP,CAAC;EAED,OAAO;IACLzD,IAAI,EAAE,8BAA8B;IACpC2D,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAE1DC,GAAGA,CAAA,EAAG;MAGJ,IAAAC,8CAAa,EAAC,IAAI,CAACC,IAAI,EAAEC,yCAAQ,CAACC,YAAY,EAAc,KAAK,CAAC;IACpE,CAAC;IAEDC,OAAO,EAAE;MAIPC,SAASA,CAACC,SAAS,EAAE;QACnB,MAAM;UAAEzE;QAAM,CAAC,GAAGyE,SAAS;QAG3B,IAAIC,UAA4B,GAAGD,SAAS,CAACC,UAAU;QACvD,IAAIA,UAAU,CAACnD,iBAAiB,CAAC,CAAC,IAAI,CAACmD,UAAU,CAAClD,IAAI,CAACmD,EAAE,EAAE;UACzD,GAAG,CAAC;YAAED;UAAW,CAAC,GAAGA,UAAU,EAAE,QAE/BA,UAAU,IACV,CAAC/B,sBAAsB,CAAC+B,UAAU,CAACE,IAAI,CAAC,IACxC,CAACF,UAAU,CAACG,WAAW,CAAC,CAAC;UAE3B,IAAIH,UAAU,EAAE;YAAA,IAAAI,qBAAA;YACd,CAAAA,qBAAA,GAAAnC,sBAAsB,CAAC+B,UAAU,CAACE,IAAI,CAAC,aAAvCE,qBAAA,CAAyCC,KAAK,CAACC,OAAO,CAACC,CAAC,IACtDA,CAAC,CAACC,IAAI,CAAC,IAAI,EAAER,UAAU,EAAE,IAAI,CAC/B,CAAC;UACH;QACF;QAEA,MAAMS,mBAAyC,GAAG,EAAE;QACpD,IAAIC,cAGgC,GAAG,IAAI;QAE3C,KAAK,MAAM9D,IAAI,IAAImD,SAAS,CAACtC,GAAG,CAAC,MAAM,CAAC,EAAE;UACxC,IAAIb,IAAI,CAACK,aAAa,CAAC,CAAC,EAAE;YACxBwD,mBAAmB,CAAClB,IAAI,CAAC3C,IAAI,CAACE,IAAI,CAAC;YACnCF,IAAI,CAAC+D,MAAM,CAAC,CAAC;UACf,CAAC,MAAM,IACL/D,IAAI,CAACM,eAAe,CAAC;YAAEE,MAAM,EAAE;UAAK,CAAC,CAAC,IACtCR,IAAI,CAACO,sBAAsB,CAAC;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC,EAC7C;YACAsD,cAAc,GAAG9D,IAAI;YAErB,IAAI6D,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,EAAE;cAIlC+C,oBAAoB,CAClBlC,IAAI,CAACE,IAAI,EACTyB,mBAAmB,CAACkC,mBAAmB,CACzC,CAAC;cACDA,mBAAmB,CAAC1E,MAAM,GAAG,CAAC;YAChC;UACF;QACF;QAEA,IAAI0E,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,EAAE;UAOlC,MAAM6E,GAAG,GAAGtF,KAAK,CAACuF,6BAA6B,CAAC,aAAa,CAAC;UAC9D,IAAIC,SAAS;UACb,MAAMC,oBAAoB,GAAGhB,SAAS,CAACC,UAAU,CAACgB,YAAY,CAAC,CAAC;UAChE,IACEP,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,IAC7B0E,mBAAmB,CAAC,CAAC,CAAC,CAAC1D,IAAI,CAAChB,MAAM,KAAK,CAAC,IACvCO,CAAC,CAACqC,qBAAqB,CAAC8B,mBAAmB,CAAC,CAAC,CAAC,CAAC1D,IAAI,CAAC,CAAC,CAAC,CAAE,EAC1D;YACA,MAAMsB,WAAW,GAAGE,mBAAmB,CAACkC,mBAAmB,CAAC;YAC5D,IAAIM,oBAAoB,EAAE;cACxB1C,WAAW,CAACkB,IAAI,CAACjD,CAAC,CAAC2E,cAAc,CAAC,CAAC,CAAC;YACtC;YACAH,SAAS,GAAG1C,uBAAuB,CAACC,WAAW,CAAC;UAClD,CAAC,MAAM;YACLyC,SAAS,GAAGxE,CAAC,CAAC4E,cAAc,CAACT,mBAAmB,CAAC,CAAC,CAAC,CAAC1D,IAAI,CAAC;YACzD,IAAIgE,oBAAoB,EAAE;cACxBD,SAAS,CAAC/D,IAAI,CAACwC,IAAI,CAACjD,CAAC,CAAC6E,eAAe,CAAC7E,CAAC,CAAC2E,cAAc,CAAC,CAAC,CAAC,CAAC;YAC5D;UACF;UACA,MAAMG,IAAI,GAAG7E,QAAQ,CAACsC,UAAU,CAAChB,GAAG,GAAG+C,GAAG,YAAYE,SAAS,EAAE;UAEjE,IAAIJ,cAAc,EAAE;YAClB5B,oBAAoB,CAAC4B,cAAc,CAAC5D,IAAI,EAAE,CAACsE,IAAI,CAAC,CAAC;UACnD,CAAC,MAAM;YAML,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;YACtC,KAAK,MAAM1E,IAAI,IAAImD,SAAS,CAACtC,GAAG,CAAC,MAAM,CAAC,EAAE;cACxC,IAAIb,IAAI,CAAC2E,SAAS,CAAC,CAAC,EAAE;gBACpBF,YAAY,CAACG,GAAG,CAAC5E,IAAI,CAACa,GAAG,CAAC,QAAQ,CAAC,CAACX,IAAI,CAACtB,IAAI,CAAC;cAChD;YACF;YACA,MAAMiG,oBAAoB,GAAGpG,WAAW,CAACC,KAAK,EAAE+F,YAAY,CAAC;YAC7D,MAAMK,cAAc,GAAGpF,CAAC,CAACqF,WAAW,CAClCrF,CAAC,CAACsF,UAAU,CAACH,oBAAoB,CACnC,CAAC;YACD1B,SAAS,CAAC8B,aAAa,CAAC,MAAM,EAAE,CAC9BvF,CAAC,CAACwF,oBAAoB,CAACJ,cAAc,EAAEN,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,CACvD,CAAC;UACJ;UAEA,MAAMW,sBAAsB,GAAGzF,CAAC,CAAC0F,cAAc,CAAC1F,CAAC,CAAC2F,SAAS,CAACrB,GAAG,CAAC,EAAE,EAAE,CAAC;UACrE,IAAIb,SAAS,CAACC,UAAU,CAACnD,iBAAiB,CAAC,CAAC,EAAE;YAI5CkD,SAAS,CAACC,UAAU,CAACkC,WAAW,CAC9B5F,CAAC,CAACgC,kBAAkB,CAAC,CACnByB,SAAS,CAACoC,MAAM,EAChBJ,sBAAsB,CACvB,CACH,CAAC;UACH,CAAC,MAAM;YACLhC,SAAS,CAACC,UAAU,CAACoC,WAAW,CAC9B9F,CAAC,CAAC+F,mBAAmB,CAACN,sBAAsB,CAC9C,CAAC;UACH;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}