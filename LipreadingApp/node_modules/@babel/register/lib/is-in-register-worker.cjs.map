{"version": 3, "names": ["envVarName", "envVarValue", "markInRegisterWorker", "env", "Object", "assign", "isInRegisterWorker", "process", "module", "exports"], "sources": ["../src/is-in-register-worker.cts"], "sourcesContent": ["/**\n * Since workers inherit the exec options from the parent thread, we\n * must be careful to avoid infinite \"@babel/register\" setup loops.\n *\n * If @babel/register is imported using the -r/--require flag, the worker\n * will have the same flag and we must avoid registering the @babel/register\n * hook again.\n *\n * - markInRegisterWorker() can be used to mark a set of env vars (that will\n *   be forwarded to a worker) as being in the @babel/register worker.\n * - isInRegisterWorker will be true in @babel/register workers.\n */\n\nconst envVarName = \"___INTERNAL___IS_INSIDE_BABEL_REGISTER_WORKER___\";\nconst envVarValue = \"yes_I_am\";\n\nconst markInRegisterWorker = (env: NodeJS.ProcessEnv) => ({\n  ...env,\n  [envVarName]: envVarValue,\n});\nconst isInRegisterWorker = process.env[envVarName] === envVarValue;\n\nexport = { markInRegisterWorker, isInRegisterWorker };\n"], "mappings": ";;AAaA,MAAMA,UAAU,GAAG,kDAAkD;AACrE,MAAMC,WAAW,GAAG,UAAU;AAE9B,MAAMC,oBAAoB,GAAIC,GAAsB,IAAAC,MAAA,CAAAC,MAAA,KAC/CF,GAAG;EACN,CAACH,UAAU,GAAGC;AAAW,EACzB;AACF,MAAMK,kBAAkB,GAAGC,OAAO,CAACJ,GAAG,CAACH,UAAU,CAAC,KAAKC,WAAW;AAACO,MAAA,CAAAC,OAAA,GAE1D;EAAEP,oBAAoB;EAAEI;AAAmB,CAAC", "ignoreList": []}