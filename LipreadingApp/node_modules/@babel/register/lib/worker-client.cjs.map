{"version": 3, "names": ["path", "require", "types", "ACTIONS", "_send", "WeakMap", "_eCache", "Client", "constructor", "send", "_classPrivateFieldInitSpec", "_classPrivateFieldSet", "getDefaultExtensions", "_classPrivateFieldGet2", "_classPrivateFieldGet", "call", "GET_DEFAULT_EXTENSIONS", "undefined", "setOptions", "options", "SET_OPTIONS", "transform", "code", "filename", "TRANSFORM", "_worker", "_signal", "WorkerClient", "action", "payload", "subChannel", "_get_worker_threads", "MessageChannel", "postMessage", "signal", "port", "port1", "Atomics", "wait", "message", "receiveMessageOnPort", "port2", "error", "Object", "assign", "errorData", "result", "Worker", "resolve", "__dirname", "env", "_get_markInRegisterWorker", "process", "Int32Array", "SharedArrayBuffer", "unref", "_this", "_this2", "markInRegisterWorker", "module", "exports", "_LocalClient", "_handleMessage", "LocalClient", "_assertClassBrand$_", "_assert<PERSON>lassBrand", "_", "TRANSFORM_SYNC", "isLocalClient"], "sources": ["../src/worker-client.cts"], "sourcesContent": ["import type { IClient, Options } from \"./types.cts\";\n\nimport path = require(\"node:path\");\nimport types = require(\"./types.cts\");\n\nimport ACTIONS = types.ACTIONS;\n\nclass Client implements IClient {\n  #send;\n\n  constructor(send: (action: ACTIONS, payload: any) => any) {\n    this.#send = send;\n  }\n\n  #eCache: string[];\n  getDefaultExtensions(): string[] {\n    return (this.#eCache ??= this.#send(\n      ACTIONS.GET_DEFAULT_EXTENSIONS,\n      undefined,\n    ));\n  }\n\n  setOptions(options: Options): void {\n    return this.#send(ACTIONS.SET_OPTIONS, options);\n  }\n\n  transform(\n    code: string,\n    filename: string,\n  ): { code: string; map: object } | null {\n    return this.#send(ACTIONS.TRANSFORM, { code, filename });\n  }\n}\n\n// We need to run Babel in a worker because require hooks must\n// run synchronously, but many steps of Babel's config loading\n// (which is done for each file) can be asynchronous\nclass WorkerClient extends Client {\n  // These two require() calls are in deferred so that they are not imported in\n  // older Node.js versions (which don't support workers).\n  // TODO: Hoist them in Babel 8.\n\n  static get #worker_threads() {\n    return require(\"node:worker_threads\") as typeof import(\"worker_threads\");\n  }\n\n  static get #markInRegisterWorker() {\n    return require(\"./is-in-register-worker.cjs\").markInRegisterWorker;\n  }\n\n  #worker = new WorkerClient.#worker_threads.Worker(\n    path.resolve(__dirname, \"./worker/index.cjs\"),\n    { env: WorkerClient.#markInRegisterWorker(process.env) },\n  );\n\n  #signal = new Int32Array(new SharedArrayBuffer(4));\n\n  constructor() {\n    super((action, payload) => {\n      this.#signal[0] = 0;\n      const subChannel = new WorkerClient.#worker_threads.MessageChannel();\n\n      this.#worker.postMessage(\n        { signal: this.#signal, port: subChannel.port1, action, payload },\n        [subChannel.port1],\n      );\n\n      Atomics.wait(this.#signal, 0, 0);\n      const { message } = WorkerClient.#worker_threads.receiveMessageOnPort(\n        subChannel.port2,\n      );\n\n      if (message.error) throw Object.assign(message.error, message.errorData);\n      else return message.result;\n    });\n\n    // The worker will never exit by itself. Prevent it from keeping\n    // the main process alive.\n    this.#worker.unref();\n  }\n}\n\nexport = { WorkerClient };\n\nif (!process.env.BABEL_8_BREAKING) {\n  module.exports.LocalClient = class LocalClient extends Client {\n    isLocalClient = true;\n\n    static #handleMessage: (action: ACTIONS, payload: any) => any;\n\n    constructor() {\n      LocalClient.#handleMessage ??= require(\"./worker/handle-message.cjs\");\n\n      super((action, payload) => {\n        return LocalClient.#handleMessage(\n          action === ACTIONS.TRANSFORM ? ACTIONS.TRANSFORM_SYNC : action,\n          payload,\n        );\n      });\n    }\n  };\n}\n"], "mappings": ";;;;;;;MAEOA,IAAI,GAAAC,OAAA,CAAW,MAAW;AAAA,MAC1BC,KAAK,GAAAD,OAAA,CAAW,aAAa;AAAA,IAE7BE,OAAO,GAAGD,KAAK,CAACC,OAAO;AAAA,IAAAC,KAAA,OAAAC,OAAA;AAAA,IAAAC,OAAA,OAAAD,OAAA;AAE9B,MAAME,MAAM,CAAoB;EAG9BC,WAAWA,CAACC,IAA4C,EAAE;IAF1DC,0BAAA,OAAAN,KAAK;IAMLM,0BAAA,OAAAJ,OAAO;IAHLK,qBAAA,CAAKP,KAAK,EAAV,IAAI,EAASK,IAAJ,CAAC;EACZ;EAGAG,oBAAoBA,CAAA,EAAa;IAAA,IAAAC,sBAAA;IAC/B,QAAAA,sBAAA,GAAQC,qBAAA,CAAKR,OAAO,EAAZ,IAAW,CAAC,YAAAO,sBAAA,GAAZF,qBAAA,CAAKL,OAAO,EAAZ,IAAI,EAAaQ,qBAAA,CAAKV,KAAK,EAAV,IAAS,CAAC,CAAAW,IAAA,CAAV,IAAI,EAC3BZ,OAAO,CAACa,sBAAsB,EAC9BC,SAAS,CAFQ,CAAC;EAItB;EAEAC,UAAUA,CAACC,OAAgB,EAAQ;IACjC,OAAOL,qBAAA,CAAKV,KAAK,EAAV,IAAS,CAAC,CAAAW,IAAA,CAAV,IAAI,EAAOZ,OAAO,CAACiB,WAAW,EAAED,OAAO;EAChD;EAEAE,SAASA,CACPC,IAAY,EACZC,QAAgB,EACsB;IACtC,OAAOT,qBAAA,CAAKV,KAAK,EAAV,IAAS,CAAC,CAAAW,IAAA,CAAV,IAAI,EAAOZ,OAAO,CAACqB,SAAS,EAAE;MAAEF,IAAI;MAAEC;IAAS,CAAC;EACzD;AACF;AAAC,IAAAE,OAAA,OAAApB,OAAA;AAAA,IAAAqB,OAAA,OAAArB,OAAA;AAKD,MAAMsB,YAAY,SAASpB,MAAM,CAAC;EAoBhCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAACoB,MAAM,EAAEC,OAAO,KAAK;MACzBf,qBAAA,CAAKY,OAAO,EAAZ,IAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACnB,MAAMI,UAAU,GAAG,KAAiBC,mBAAe,CAA5BJ,YAA2B,CAAC,CAACK,cAAc,EAAC,CAAC;MAEpElB,qBAAA,CAAKW,OAAO,EAAZ,IAAW,CAAC,CAACQ,WAAW,CACtB;QAAEC,MAAM,EAAEpB,qBAAA,CAAKY,OAAO,EAAZ,IAAW,CAAC;QAAES,IAAI,EAAEL,UAAU,CAACM,KAAK;QAAER,MAAM;QAAEC;MAAQ,CAAC,EACjE,CAACC,UAAU,CAACM,KAAK,CACnB,CAAC;MAEDC,OAAO,CAACC,IAAI,CAACxB,qBAAA,CAAKY,OAAO,EAAZ,IAAW,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAChC,MAAM;QAAEa;MAAQ,CAAC,GAAgBR,mBAAe,CAA5BJ,YAA2B,CAAC,CAACa,oBAAoB,CACnEV,UAAU,CAACW,KACb,CAAC;MAED,IAAIF,OAAO,CAACG,KAAK,EAAE,MAAMC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACM,SAAS,CAAC,CAAC,KACpE,OAAON,OAAO,CAACO,MAAM;IAC5B,CAAC,CAAC;IAxBJpC,0BAAA,OAAAe,OAAO,EAAG,KAAiBM,mBAAe,CAA5BJ,YAA2B,CAAC,CAACoB,MAAM,EAC/C/C,IAAI,CAACgD,OAAO,CAACC,SAAS,EAAE,oBAAoB,CAAC,EAC7C;MAAEC,GAAG,EAAeC,yBAAqB,CAAlCxB,YAAiC,CAAC,CAAAZ,IAAA,CAAlCY,YAAY,EAAuByB,OAAO,CAACF,GAAG;IAAE,CACzD,CAAC;IAEDxC,0BAAA,OAAAgB,OAAO,EAAG,IAAI2B,UAAU,CAAC,IAAIC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAuBhDxC,qBAAA,CAAKW,OAAO,EAAZ,IAAW,CAAC,CAAC8B,KAAK,CAAC,CAAC;EACtB;AACF;AAAC,SAAAxB,oBAAAyB,KAAA,EAtC8B;EAC3B,OAAOvD,OAAO,CAAC,gBAAqB,CAAC;AACvC;AAAC,SAAAkD,0BAAAM,MAAA,EAEkC;EACjC,OAAOxD,OAAO,CAAC,6BAA6B,CAAC,CAACyD,oBAAoB;AACpE;AAACC,MAAA,CAAAC,OAAA,GAkCM;EAAEjC;AAAa,CAAC;AAEU;EAAA,IAAAkC,YAAA,EAAAC,cAAA;EACjCH,MAAM,CAACC,OAAO,CAACG,WAAW,IAAAF,YAAA,GAAG,MAAME,WAAW,SAASxD,MAAM,CAAC;IAK5DC,WAAWA,CAAA,EAAG;MAAA,IAAAwD,mBAAA;MACZ,CAAAA,mBAAA,GAAAC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAACD,cAAc,EAAAI,CAAA,YAAAF,mBAAA,GAAdF,cAAc,CAAAI,CAAA,GAAAD,iBAAA,CAAAJ,YAAA,EAA1BE,WAAW,EAAoB9D,OAAO,CAAC,6BAA6B,CAAC,CAA3C;MAE1B,KAAK,CAAC,CAAC2B,MAAM,EAAEC,OAAO,KAAK;QACzB,OAAOoC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAACD,cAAc,EAAAI,CAAA,CAAAnD,IAAA,CAA1BgD,WAAW,EAChBnC,MAAM,KAAKzB,OAAO,CAACqB,SAAS,GAAGrB,OAAO,CAACgE,cAAc,GAAGvC,MAAM,EAC9DC,OAAO;MAEX,CAAC,CAAC;MAAC,KAZLuC,aAAa,GAAG,IAAI;IAapB;EACF,CAAC,EAAAN,cAAA;IAAAI,CAAA;EAAA,GAAAL,YAAA;AACH", "ignoreList": []}