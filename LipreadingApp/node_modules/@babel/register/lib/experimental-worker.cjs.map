{"version": 3, "names": ["major", "minor", "process", "versions", "node", "split", "map", "Number", "Error", "hook", "require", "workerClient", "client", "register", "opts", "WorkerClient", "module", "exports", "Object", "assign", "revert", "default", "__esModule", "isInRegisterWorker"], "sources": ["../src/experimental-worker.cts"], "sourcesContent": ["// TODO: Move this file to index.js in Babel 8\nimport type { IClient, Options } from \"./types.cts\";\n\nconst [major, minor] = process.versions.node.split(\".\").map(Number);\n\nif (major < 12 || (major === 12 && minor < 3)) {\n  throw new Error(\n    \"@babel/register/experimental-worker requires Node.js >= 12.3.0\",\n  );\n}\n\nimport hook = require(\"./hook.cjs\");\nimport workerClient = require(\"./worker-client.cjs\");\n\nlet client: IClient;\nfunction register(opts?: Options) {\n  client ||= new workerClient.WorkerClient();\n  hook.register(client, opts);\n}\n\nexport = Object.assign(register, {\n  revert: hook.revert,\n  default: register,\n  __esModule: true,\n});\n\nif (!require(\"./is-in-register-worker.cjs\").isInRegisterWorker) {\n  register();\n}\n"], "mappings": ";;AAGA,MAAM,CAACA,KAAK,EAAEC,KAAK,CAAC,GAAGC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;AAEnE,IAAIP,KAAK,GAAG,EAAE,IAAKA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAE,EAAE;EAC7C,MAAM,IAAIO,KAAK,CACb,gEACF,CAAC;AACH;AAAC,MAEMC,IAAI,GAAAC,OAAA,CAAW,YAAY;AAAA,MAC3BC,YAAY,GAAAD,OAAA,CAAW,qBAAqB;AAEnD,IAAIE,MAAe;AACnB,SAASC,QAAQA,CAACC,IAAc,EAAE;EAChCF,MAAM,KAANA,MAAM,GAAK,IAAID,YAAY,CAACI,YAAY,CAAC,CAAC;EAC1CN,IAAI,CAACI,QAAQ,CAACD,MAAM,EAAEE,IAAI,CAAC;AAC7B;AAACE,MAAA,CAAAC,OAAA,GAEQC,MAAM,CAACC,MAAM,CAACN,QAAQ,EAAE;EAC/BO,MAAM,EAAEX,IAAI,CAACW,MAAM;EACnBC,OAAO,EAAER,QAAQ;EACjBS,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,IAAI,CAACZ,OAAO,CAAC,6BAA6B,CAAC,CAACa,kBAAkB,EAAE;EAC9DV,QAAQ,CAAC,CAAC;AACZ", "ignoreList": []}