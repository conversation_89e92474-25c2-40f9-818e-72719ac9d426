{"expo": {"name": "Lipreading AI", "slug": "lipreading-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#667eea"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.student.lipreadingapp", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to capture lip movements for AI analysis.", "NSMicrophoneUsageDescription": "This app needs microphone access for video recording with audio.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to save recorded videos."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-camera", {"cameraPermission": "Allow Lipreading AI to access your camera to capture lip movements for analysis."}], ["expo-media-library", {"photosPermission": "Allow Lipreading AI to access your photos to save recorded videos.", "savePhotosPermission": "Allow Lipreading AI to save videos to your photo library.", "isAccessMediaLocationEnabled": true}]]}}